<?php

namespace Database\Seeders;

use App\Domain\Email\Models\Email;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class EmailSeeder extends Seeder
{
    public function run(): void
    {
        $template = null;

        try {
            $template = file_get_contents(storage_path('app/email-template.html'));
        } catch (\Exception $exception) {
        }

        foreach ($this->getEvents('Domain\\Email\\Dto\\Mails') as $event) {
            $event = Str::start($event, 'App\\');
            $email = Email::query()->where(Email::PROPERTY_EVENT, $event)->first();

            if (!!$email) {
                continue;
            }

            Email::query()->create([
                Email::PROPERTY_EVENT => $event,
                Email::PROPERTY_HTML => $template,
            ]);
        }
    }

    private function getEvents(string $namespace): array
    {
        $path = base_path() . '/app/' . str_replace('\\', '/', $namespace);

        $classes = [];

        foreach (scandir($path) as $item) {
            if ($item === '.' || $item === '..') {
                continue;
            }

            if (Str::contains($item, '.php')) {
                $classes[] = Str::replace('/', '\\', sprintf('%s/%s', $namespace, Str::remove('.php', $item)));
                continue;
            }

            $classes = array_merge(
                $classes,
                $this->getEvents(Str::replace('/', '\\', sprintf('%s/%s', $namespace, $item))),
            );
        }

        return $classes;
    }
}
