<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('stripe_prices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->references('id')->on('stripe_products')->cascadeOnDelete();
            $table->string('stripe_id');
            $table->bigInteger('price')->nullable();
            $table->string('currency')->nullable();
            $table->boolean('default')->default(false);
            $table->boolean('is_hidden')->default(false);
            $table->string('type');
            $table->string('recurring_interval')->nullable();
            $table->string('recurring_interval_count')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stripe_prices');
    }
};
