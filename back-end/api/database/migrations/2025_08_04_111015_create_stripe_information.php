<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('stripe_information', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_id')->references('id')->on('accounts')->cascadeOnDelete();
            $table->string('email')->nullable();
            $table->string('collection_type')->nullable();
            $table->string('status')->nullable();
            $table->string('pm_type')->nullable();
            $table->integer('pm_last_four')->nullable();
            $table->string('street')->nullable();
            $table->integer('house_number')->nullable();
            $table->string('house_number_addition')->nullable();
            $table->string('zipcode')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stripe_information');
    }
};
