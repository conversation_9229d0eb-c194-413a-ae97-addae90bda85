<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('google_ad_campaign_insights', function (Blueprint $table) {
            $table->float('search_impression_share')->default(0)->after('profit');
            $table->float('search_top_impression_share')->default(0)->after('search_impression_share');
            $table->float('search_absolute_top_impression_share')->default(0)->after('search_top_impression_share');
            $table->float('content_impression_share')->default(0)->after('search_absolute_top_impression_share');
            $table->float('content_rank_lost_impression_share')->default(0)->after('content_impression_share');
            $table
                ->float('content_budget_lost_impression_share')
                ->default(0)
                ->after('content_rank_lost_impression_share');
        });
    }

    public function down(): void
    {
        Schema::table('google_ad_campaign_insights', function (Blueprint $table) {
            $table->dropColumn('search_impression_share');
            $table->dropColumn('search_top_impression_share');
            $table->dropColumn('search_absolute_top_impression_share');
            $table->dropColumn('content_impression_share');
            $table->dropColumn('content_rank_lost_impression_share');
            $table->dropColumn('content_budget_lost_impression_share');
        });
    }
};
