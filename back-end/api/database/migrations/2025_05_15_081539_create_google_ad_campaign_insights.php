<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('google_ad_campaign_insights', function (Blueprint $table) {
            $table->id();
            $table->foreignId('google_ad_campaign_id')->references('id')->on('google_ad_campaigns')->cascadeOnDelete();
            $table->date('date');
            $table->integer('clicks');
            $table->integer('video_views');
            $table->integer('ctr');
            $table->integer('impressions');
            $table->string('average_cpc');
            $table->string('average_cpm');
            $table->string('spend');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_ad_campaign_insights');
    }
};
