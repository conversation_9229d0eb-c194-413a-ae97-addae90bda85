<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('google_ad_account_scripts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('google_ad_account_id')->references('id')->on('google_ad_accounts')->cascadeOnDelete();
            $table->foreignId('google_file_id')->references('id')->on('google_files')->cascadeOnDelete();
            $table->string('type');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_ad_account_scripts');
    }
};
