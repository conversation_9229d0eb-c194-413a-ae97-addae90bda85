<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('google_ad_account_quality_scores', function (Blueprint $table) {
            $table->id();
            $table->foreignId('google_ad_account_id')->references('id')->on('google_ad_accounts')->cascadeOnDelete();
            $table->date('date');
            $table->string('factor');
            $table->string('metric');
            $table->string('rating');
            $table->float('value');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_ad_account_quality_scores');
    }
};
