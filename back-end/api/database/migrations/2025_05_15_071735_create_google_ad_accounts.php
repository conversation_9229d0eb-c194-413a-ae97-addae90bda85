<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('google_ad_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('google_account_id')->references('id')->on('google_accounts')->cascadeOnDelete();
            $table->bigInteger('customer_id')->nullable();
            $table->bigInteger('external_id');
            $table->string('name')->nullable();
            $table->string('status');
            $table->string('time_zone');
            $table->string('currency');
            $table->integer('level');
            $table->boolean('is_hidden');
            $table->boolean('is_manager');
            $table->boolean('is_test_account');
            $table->dateTime('last_synced_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_ad_accounts');
    }
};
