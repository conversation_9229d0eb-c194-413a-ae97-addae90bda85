<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('emails', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_id')->nullable()->references('id')->on('accounts')->cascadeOnDelete();
            $table->foreignId('updated_by')->nullable()->references('id')->on('users')->nullOnUpdate();
            $table->string('name')->nullable();
            $table->string('subject')->nullable();
            $table->string('event');
            $table->string('language')->default('en');
            $table->boolean('whitelabel')->default(false);
            $table->boolean('cc_sender')->default(false);
            $table->json('cc')->nullable();
            $table->longText('html')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('emails');
    }
};
