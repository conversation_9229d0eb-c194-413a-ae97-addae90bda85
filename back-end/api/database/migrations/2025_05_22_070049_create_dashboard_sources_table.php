<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('dashboard_sources', function (Blueprint $table) {
            $table->id();
            $table->foreignId('dashboard_id')->references('id')->on('dashboards')->cascadeOnDelete();
            $table->unsignedBigInteger('source_id');
            $table->string('source_type');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('dashboard_sources');
    }
};
