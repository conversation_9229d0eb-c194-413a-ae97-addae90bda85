<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('google_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_id')->references('id')->on('accounts')->cascadeOnDelete();
            $table->string('external_id');
            $table->string('email');
            $table->string('name');
            $table->string('status')->nullable();
            $table->longText('image_url')->nullable();
            $table->longText('refresh_token')->nullable();
            $table->longText('token')->nullable();
            $table->json('scopes')->nullable();
            $table->dateTime('token_created_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_accounts');
    }
};
