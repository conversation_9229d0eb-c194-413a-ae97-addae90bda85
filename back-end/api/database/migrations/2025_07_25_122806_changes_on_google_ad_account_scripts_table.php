<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('google_ad_account_scripts', function (Blueprint $table) {
            $table->unsignedBigInteger('google_file_id')->nullable()->change();
            $table->string('status')->nullable()->after('type');
            $table->string('error')->nullable()->after('status');
            $table->json('error_details')->nullable()->after('error');
            $table
                ->foreignId('google_drive_id')
                ->nullable()
                ->after('google_file_id')
                ->references('id')
                ->on('google_drives')
                ->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('google_ad_account_scripts', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('error');
            $table->dropColumn('error_details');
            $table->dropConstrainedForeignId('google_drive_id');
        });

        Schema::table('google_ad_account_scripts', function (Blueprint $table) {
            $table->dropColumn('google_drive_id');
        });
    }
};
