<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('google_ad_account_auction_insights', function (Blueprint $table) {
            $table->id();
            $table->foreignId('google_ad_account_id')->references('id')->on('google_ad_accounts')->cascadeOnDelete();
            $table->string('domain');
            $table->date('date');
            $table->float('impression_share')->nullable();
            $table->float('overlap_rate')->nullable();
            $table->float('position_above_rate')->nullable();
            $table->float('top_of_page_rate')->nullable();
            $table->float('absolute_top_of_page_rate')->nullable();
            $table->float('outranking_share')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_ad_account_auction_insights');
    }
};
