<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('google_ad_account_keyword_quality_scores', function (Blueprint $table) {
            $table->id();
            $table
                ->foreignId('google_ad_account_id')
                ->references('id', 'keyword_add_account_id_foreign')
                ->on('google_ad_accounts')
                ->cascadeOnDelete();
            $table->date('date');
            $table->string('keyword');
            $table->float('score')->nullable();
            $table->integer('impressions');
            $table->integer('clicks');
            $table->float('cost');
            $table->float('conversions');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_ad_account_keyword_quality_scores');
    }
};
