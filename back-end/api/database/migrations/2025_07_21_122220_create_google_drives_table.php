<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('google_drives', function (Blueprint $table) {
            $table->id();
            $table->foreignId('google_account_id')->references('id')->on('google_accounts')->cascadeOnDelete();
            $table->string('external_id');
            $table->string('name');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_drives');
    }
};
