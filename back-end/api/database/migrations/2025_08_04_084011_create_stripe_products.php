<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('stripe_products', function (Blueprint $table) {
            $table->id();
            $table->string('stripe_id');
            $table->string('name');
            $table->longText('description')->nullable();
            $table->json('features')->nullable();
            $table->boolean('whitelabel')->default(false);
            $table->boolean('is_hidden')->default(false);
            $table->boolean('has_trial')->default(false);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stripe_products');
    }
};
