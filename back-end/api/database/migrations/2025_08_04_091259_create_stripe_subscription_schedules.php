<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('stripe_subscription_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->references('id')->on('stripe_subscriptions')->cascadeOnDelete();
            $table->foreignId('product_id')->nullable()->references('id')->on('stripe_products')->nullOnDelete();
            $table->foreignId('price_id')->nullable()->references('id')->on('stripe_prices')->nullOnDelete();
            $table->string('stripe_id');
            $table->dateTime('start_date')->nullable();
            $table->dateTime('canceled_at')->nullable();
            $table->dateTime('completed_at')->nullable();
            $table->dateTime('released_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stripe_subscription_schedules');
    }
};
