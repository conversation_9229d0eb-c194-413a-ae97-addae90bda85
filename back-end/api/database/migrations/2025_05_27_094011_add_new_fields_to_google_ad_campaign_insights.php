<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('google_ad_campaign_insights', function (Blueprint $table) {
            $table->float('conversions')->default(0)->after('spend');
            $table->float('conversion_rate')->default(0)->after('conversions');
            $table->float('revenue')->default(0)->after('conversion_rate');
            $table->float('roas')->default(0)->after('revenue');
            $table->float('profit')->default(0)->after('roas');
        });
    }

    public function down(): void
    {
        Schema::table('google_ad_campaign_insights', function (Blueprint $table) {
            $table->dropColumn('conversions');
            $table->dropColumn('conversion_rate');
            $table->dropColumn('revenue');
            $table->dropColumn('roas');
            $table->dropColumn('profit');
        });
    }
};
