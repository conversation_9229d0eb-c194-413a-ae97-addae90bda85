<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('stripe_subscription_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->references('id')->on('stripe_subscriptions')->cascadeOnDelete();
            $table->string('stripe_id');
            $table->string('stripe_product');
            $table->string('stripe_price');
            $table->bigInteger('quantity')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stripe_subscription_items');
    }
};
