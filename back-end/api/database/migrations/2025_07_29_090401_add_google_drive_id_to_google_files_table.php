<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('google_files', function (Blueprint $table) {
            $table
                ->foreignId('google_drive_id')
                ->nullable()
                ->after('google_account_id')
                ->references('id')
                ->on('google_drives')
                ->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('google_files', function (Blueprint $table) {
            $table->dropConstrainedForeignId('google_drive_id');
        });
    }
};
