<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dashboard_whitelabels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_id')->references('id')->on('accounts')->cascadeOnDelete();
            $table->boolean('first_change')->default(false);
            $table->string('language')->nullable();
            $table->string('zero_ssl_id')->nullable();
            $table->string('zero_ssl_status')->nullable();
            $table->string('domain')->nullable();
            $table->dateTime('ssl_certificate_expires_at')->nullable();
            $table->string('logo')->nullable();
            $table->integer('logo_size')->nullable();
            $table->string('accent_color')->nullable();
            $table->string('accent_text_color')->nullable();
            $table->string('header_background_color')->nullable();
            $table->string('header_text_color')->nullable();
            $table->string('background_color')->nullable();
            $table->string('text_color')->nullable();
            $table->string('widget_background_color')->nullable();
            $table->string('widget_text_color')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dashboard_whitelabels');
    }
};
