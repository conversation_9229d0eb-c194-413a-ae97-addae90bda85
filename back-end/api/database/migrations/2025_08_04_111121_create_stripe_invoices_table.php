<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('stripe_invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_id')->references('id')->on('accounts')->cascadeOnDelete();
            $table->string('stripe_id');
            $table->string('number')->nullable();
            $table->string('status')->nullable();
            $table->bigInteger('total_including_vat');
            $table->bigInteger('total_excluding_vat');
            $table->bigInteger('amount_total');
            $table->bigInteger('amount_remaining')->nullable();
            $table->bigInteger('amount_paid')->nullable();
            $table->dateTime('period_start')->nullable();
            $table->dateTime('period_end')->nullable();
            $table->dateTime('due_date')->nullable();
            $table->dateTime('invoiced_date')->nullable();
            $table->dateTime('paid_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stripe_invoices');
    }
};
