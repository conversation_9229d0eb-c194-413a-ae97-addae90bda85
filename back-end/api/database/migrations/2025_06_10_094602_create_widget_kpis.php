<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('widget_kpis', function (Blueprint $table) {
            $table->id();
            $table->foreignId('widget_id')->references('id')->on('widgets')->cascadeOnDelete();
            $table->float('target');
            $table->integer('year');
            $table->string('unit')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('widget_kpis');
    }
};
