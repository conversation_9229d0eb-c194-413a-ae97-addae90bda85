<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('dashboard_recipient', function (Blueprint $table) {
            $table->id();
            $table->foreignId('dashboard_id')->references('id')->on('dashboards')->cascadeOnDelete();
            $table->string('company');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('dashboard_recipient');
    }
};
