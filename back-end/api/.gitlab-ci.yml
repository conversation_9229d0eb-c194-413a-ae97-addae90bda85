.php_image: &php_image registry.gitlab.com/localium/tooling/docker-containers/php8.3:v0.0.8

cache:
  key: back-end_api_npm
  policy: pull-push
  paths:
    - .npm/

variables:
  MYSQL_ROOT_PASSWORD: password
  MYSQL_DATABASE: laravel
  MYSQL_USER: laravel
  MYSQL_PASSWORD: laravel

back-end:api:lint:
  stage: lint
  image: *php_image
  extends:
    - .back-end:api:working-directory
  script:
    # vendor files
    - npm ci --cache .npm --prefer-offline
    - COMPOSER_CACHE_DIR=composer-global-cache composer install --prefer-dist --no-ansi --no-interaction --no-progress
  artifacts:
    expire_in: 1 days
    paths:
      - ./back-end/api/node_modules/
      - ./back-end/api/vendor/

back-end:api:build:
  stage: build
  image: *php_image
  extends:
    - .back-end:api:working-directory
  needs:
    - back-end:api:lint
  when: on_success
  script:
    # env
    - cp .env.example .env
  artifacts:
    expire_in: 1 days
    paths:
      - ./back-end/api/app
      - ./back-end/api/bootstrap
      - ./back-end/api/config
      - ./back-end/api/database
      - ./back-end/api/resources
      - ./back-end/api/public
      - ./back-end/api/routes
      - ./back-end/api/vendor
      - ./back-end/api/artisan
      - ./back-end/api/storage/app

#back-end:api:phpunit:
#  stage: test
#  image: *php_image
#  extends:
#    - .back-end:api:working-directory
#  needs:
#    - back-end:api:build
#  script:
#    - COMPOSER_CACHE_DIR=composer-global-cache composer install --no-ansi --no-interaction --no-progress
#    - cp .env.testing.example .env.testing
#    # Grant all privileges to enable parallel testing in mysql
#    - service mariadb start
#    - mysql --user root --password="${MYSQL_ROOT_PASSWORD}" --host=127.0.0.1 --execute="GRANT ALL PRIVILEGES ON *.* TO '${MYSQL_USER}'@'%'; FLUSH PRIVILEGES;"
#    - php artisan test --stop-on-failure
#  except:
#    - tags

back-end:api:acc:release:
  stage: staging
  when: manual
  extends: .back-end:api:deploy
  needs:
    - back-end:api:build
#  only:
#    - /^release\//
  variables: {
    ONEPASSWORD: "CI Secrets Eaglo/API - Acceptance"
  }
  environment:
    name: 'BACK-END/API/ACC'
    url: https://dashboard.acc.linkmyagency.dev/api
    deployment_tier: staging

#back-end:api:prod:release:
#  stage: production
#  when: manual
#  extends: .back-end:api:deploy
#  needs:
#    - back-end:api:build
#  #  only:
#  #    - tags
#  variables: {
#    ONEPASSWORD: "CI Secrets LinkHealthMonitor/API - Production"
#  }
#  environment:
#    name: 'BACK-END/API/PROD'
#    url: https://dashboard.linkhealthmonitor.com/api
#    deployment_tier: production

.back-end:api:deploy:
  image: *php_image
  before_script:
    - curl -sSfLo op.zip https://cache.agilebits.com/dist/1P/op2/pkg/v2.19.0/op_linux_amd64_v2.19.0.zip
    - unzip -o op.zip
    - rm op.zip
    - export OP_SERVICE_ACCOUNT_TOKEN=$OP_SERVICE_ACCOUNT
    # set 1password values
    - export USER=$(./op read "op://$ONEPASSWORD/SSH/ssh_user")
    - export PORT=$(./op read "op://$ONEPASSWORD/SSH/ssh_port")
    - export HOST=$(./op read "op://$ONEPASSWORD/Server/server_host")
    - export PHP=$(./op read "op://$ONEPASSWORD/Server/php")
    - export BASE_PATH=$(./op read "op://$ONEPASSWORD/Server/server_path")
    - export ENV=$(./op read "op://$ONEPASSWORD/environment")
    - export SSH="ssh -tt ${USER}@${HOST} -p ${PORT}"
    - export CURRENT_PATH="${BASE_PATH}/current"
    - export STORAGE_PATH="${BASE_PATH}/storage"
    - export RELEASES_PATH="${BASE_PATH}/releases"
    - export RELEASE_PATH="${BASE_PATH}/releases/$CI_JOB_ID"
    # SSH
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    # Copy the SSH key
    - echo "$SSH_KEY" > ~/.ssh/id_rsa
    # Set the correct permissions for the SSH folder and key
    - chmod 700 ~/.ssh
    - chmod 600 ~/.ssh/id_rsa
    # Add SSH key
    - ssh-add ~/.ssh/id_rsa
    # Disable strict host key checking
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    # Create release folder
    - $SSH "mkdir -p -m 775 ${RELEASE_PATH}"
    # Create storage directories
    - $SSH "mkdir -p -m 775 ${STORAGE_PATH}/app/public"
    - $SSH "mkdir -p -m 775 ${STORAGE_PATH}/framework/cache"
    - $SSH "mkdir -p -m 775 ${STORAGE_PATH}/framework/cache/data"
    - $SSH "mkdir -p -m 775 ${STORAGE_PATH}/framework/views"
    - $SSH "mkdir -p -m 775 ${STORAGE_PATH}/framework/testing"
    - $SSH "mkdir -p -m 775 ${STORAGE_PATH}/framework/sessions"
    # Link storage directory to release
    - $SSH "ln -nfs ${STORAGE_PATH} ${RELEASE_PATH}/storage"
    # Create environment
    - $SSH "echo '${ENV}' > ${RELEASE_PATH}/.env"
    # Remove optional files
    - rm -rf ./back-end/api/src/Packages
    - rm -rf ./back-end/api/node_modules
    - rm -rf ./back-end/api/package-lock.json
    - rm -rf ./back-end/api/package.json
    - rm -rf ./back-end/api/tailwind.config.js
    - rm -rf ./back-end/api/webpack.mix.js
    - rm -rf ./back-end/api/phpunit.dusk.xml
    - rm -rf ./back-end/api/phpunit.xml
    - rm -rf ./back-end/api/README.md
    - rm -rf ./back-end/api/storage
    # Compress files
    - tar -czf source.tar.gz -C ./back-end/api .
    # Upload files
    - scp -P${PORT} -r source.tar.gz ${USER}@${HOST}:${RELEASE_PATH}
    # Uncompress files - source
    - $SSH "tar -xzf ${RELEASE_PATH}/source.tar.gz -C ${RELEASE_PATH}/"
    # Delete archive
    - $SSH "rm -rf ${RELEASE_PATH}/source.tar.gz"
  script:
    # Before activate
    - $SSH "${PHP} ${RELEASE_PATH}/artisan migrate --force"
    - $SSH "${PHP} ${RELEASE_PATH}/artisan storage:link"
    - $SSH "${PHP} ${RELEASE_PATH}/artisan cache:clear"
    - $SSH "${PHP} ${RELEASE_PATH}/artisan route:clear"
    - $SSH "${PHP} ${RELEASE_PATH}/artisan config:clear"
    - $SSH "${PHP} ${RELEASE_PATH}/artisan optimize:clear"
    - $SSH "${PHP} ${RELEASE_PATH}/artisan route:cache";
    - $SSH "${PHP} ${RELEASE_PATH}/artisan config:cache";
    # Active new release
    - $SSH "ln -nfs ${RELEASE_PATH} ${CURRENT_PATH}";
    # Restart supervisors
    - $SSH "cd ${CURRENT_PATH} && ${PHP} artisan runcloud:restart_supervisors";
  after_script:
    - curl -sSfLo op.zip https://cache.agilebits.com/dist/1P/op2/pkg/v2.19.0/op_linux_amd64_v2.19.0.zip
    - unzip -o op.zip
    - rm op.zip
    - export OP_SERVICE_ACCOUNT_TOKEN=$OP_SERVICE_ACCOUNT
    # set 1password values
    - export USER=$(./op read "op://$ONEPASSWORD/SSH/ssh_user")
    - export PORT=$(./op read "op://$ONEPASSWORD/SSH/ssh_port")
    - export HOST=$(./op read "op://$ONEPASSWORD/Server/server_host")
    - export BASE_PATH=$(./op read "op://$ONEPASSWORD/Server/server_path")
    - export SSH="ssh -tt ${USER}@${HOST} -p ${PORT}"
    - export RELEASES_PATH="${BASE_PATH}/releases"
    # Clean up old releases
    - $SSH "cd ${RELEASES_PATH} && ls -tr | head -n -5 | xargs --no-run-if-empty rm -rf"


.back-end:api:working-directory:
  before_script:
    - cd ./back-end/api
