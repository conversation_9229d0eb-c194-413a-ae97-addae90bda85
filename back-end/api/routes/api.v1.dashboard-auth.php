<?php

use App\Http\API\Dashboard\Authentication\Controllers\AuthController;
use App\Http\API\Dashboard\Dashboard\Controllers\DashboardController;
use App\Http\API\Dashboard\Email\Controllers\EmailController;
use App\Http\API\Dashboard\Google\Controllers\GoogleAccountController;
use App\Http\API\Dashboard\Google\Controllers\GoogleAdAccountController;
use App\Http\API\Dashboard\Google\Controllers\GoogleAuthenticationController;
use App\Http\API\Dashboard\Google\Controllers\GoogleDriveController;
use App\Http\API\Dashboard\Google\Controllers\GoogleScriptController;
use App\Http\API\Dashboard\Profile\Controllers\UserController;
use App\Http\API\Dashboard\Stripe\Controllers\BillingController;
use App\Http\API\Dashboard\Stripe\Controllers\InvoiceController;
use Illuminate\Support\Facades\Route;

Route::prefix('auth')
    ->name('auth.')
    ->group(function () {
        Route::get('me', [AuthController::class, 'me'])->name('me');
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');
    });

Route::prefix('users')
    ->name('users.')
    ->group(function () {
        Route::get('', [UserController::class, 'index'])->name('index');
        Route::post('', [UserController::class, 'store'])->name('store');

        Route::prefix('{id}')->group(function () {
            Route::get('', [UserController::class, 'show'])->name('show');
            Route::post('', [UserController::class, 'update'])->name('update');
            Route::delete('', [UserController::class, 'delete'])->name('delete');
        });
    });

Route::prefix('google')
    ->name('google.')
    ->group(function () {
        Route::prefix('authentication')
            ->name('authentication.')
            ->group(function () {
                Route::post('oauthRedirectLink', [GoogleAuthenticationController::class, 'oauthRedirectLink'])->name(
                    'oauthRedirectLink',
                );
            });

        Route::prefix('accounts')
            ->name('accounts.')
            ->group(function () {
                Route::get('', [GoogleAccountController::class, 'index'])->name('index');
                Route::delete('{id}', [GoogleAccountController::class, 'delete'])->name('delete');
            });

        Route::prefix('ad')
            ->name('ad.')
            ->group(function () {
                Route::prefix('accounts')
                    ->name('accounts.')
                    ->group(function () {
                        Route::get('', [GoogleAdAccountController::class, 'index'])->name('index');
                        Route::get('{id}', [GoogleAdAccountController::class, 'show'])->name('show');
                    });
            });

        Route::prefix('drive')
            ->name('drive.')
            ->group(function () {
                Route::get('', [GoogleDriveController::class, 'index'])->name('index');
            });

        Route::prefix('scripts')
            ->name('scripts.')
            ->group(function () {
                Route::post('', [GoogleScriptController::class, 'store'])->name('store');

                Route::prefix('{id}')->group(function () {
                    Route::get('', [GoogleScriptController::class, 'show'])->name('show');
                    Route::get('script', [GoogleScriptController::class, 'script'])->name('script');
                });
            });
    });

Route::prefix('dashboards')
    ->name('dashboards.')
    ->group(function () {
        Route::get('', [DashboardController::class, 'index'])->name('index');
        Route::post('', [DashboardController::class, 'store'])->name('store');
        Route::get('configuration', [DashboardController::class, 'configuration'])->name('configuration');

        Route::prefix('{id}')->group(function () {
            Route::get('', [DashboardController::class, 'show'])->name('show');
            Route::put('', [DashboardController::class, 'update'])->name('update');
            Route::delete('', [DashboardController::class, 'delete'])->name('delete');
        });
    });

Route::prefix('emails')
    ->name('emails.')
    ->group(function () {
        Route::get('', [EmailController::class, 'index'])->name('index');

        Route::prefix('{id}')->group(function () {
            Route::get('', [EmailController::class, 'show'])->name('show');
            Route::put('', [EmailController::class, 'update'])->name('update');
            Route::post('add-language', [EmailController::class, 'addLanguage'])->name('add-language');
            Route::delete('', [EmailController::class, 'delete'])->name('delete');
            Route::post('preview', [EmailController::class, 'preview'])->name('preview');
        });
    });

Route::prefix('billing')
    ->name('billing.')
    ->group(function () {
        Route::put('', [BillingController::class, 'update'])->name('update');
        Route::get('setup-intent', [BillingController::class, 'setupIntent'])->name('setup-intent');
        Route::get('products', [BillingController::class, 'products'])->name('products');
        Route::post('subscribe', [BillingController::class, 'subscribe'])->name('subscribe');
        Route::post('extend-trial', [BillingController::class, 'extendTrial'])->name('extend-trial');
        Route::prefix('invoices')
            ->name('invoices.')
            ->group(function () {
                Route::get('', [InvoiceController::class, 'index'])->name('index');
                Route::get('{id}/download', [InvoiceController::class, 'download'])->name('download');
            });
    });
