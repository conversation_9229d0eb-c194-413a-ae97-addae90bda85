<?php

use App\Http\API\Dashboard\Authentication\Controllers\AuthController;
use App\Http\API\Dashboard\Google\Controllers\GoogleAuthenticationController;
use Illuminate\Support\Facades\Route;

Route::prefix('auth')
    ->name('auth.')
    ->group(function () {
        Route::post('login', [AuthController::class, 'login'])->name('login');
        Route::post('register', [AuthController::class, 'register'])->name('register');
    });

Route::prefix('google')
    ->name('google.')
    ->group(function () {
        Route::prefix('authentication')
            ->name('authentication.')
            ->group(function () {
                Route::get('callback', [GoogleAuthenticationController::class, 'callback'])->name('callback');
            });
    });
