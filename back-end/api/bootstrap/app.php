<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        using: function () {
            \App\Providers\RouteServiceProvider::routes();
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->statefulApi();
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->withCommands([__DIR__ . '/../app/Commands'])
    ->withSchedule(function (\Illuminate\Console\Scheduling\Schedule $schedule) {
        \App\Providers\ScheduleServiceProvider::boot($schedule);
    })
    ->withEvents(discover: [__DIR__ . '/../app/Domain/Email/Support/Listeners'])
    ->create();
