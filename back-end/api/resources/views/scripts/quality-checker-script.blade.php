// COPYRIGHT
// PPC Mastery - <PERSON>
// <EMAIL>
//
// ABOUT THE SCRIPT
// The script retrieves keyword performance data from Google Ads, focusing on quality scores and associated factors.
// This data will then be used to populate Quality Score metrics inside of our "PPPC Mastery Control Center" Looker Studio dashboard.
// This altered version also provides Cost, Clicks, and Conversions per keyword, along with Quality Score and Impressions.
//
// IMPORTANT NOTES
// - Let the script run daily
// - Make a copy of the Google Sheet and replace the URL in the Customization Section below
// - Link your Google Sheet to the Looker Studio dashboard.
//
// ---- Script Starts Below ----

// --- CUSTOMIZATION ---
var SPREADSHEET_URL = '{{ $url }}'; // Replace with your Google Sheet URL

// Set this to the date you will run the script for the first time (in 'YYYY-MM-DD' format).
// On this date, the script will fetch data starting from January 1st of that year.
// On all other days, it will fetch only yesterday's data.
var MANUAL_FIRST_RUN_DATE = '{{ $date }}';
// --- <PERSON><PERSON> CUSTOMIZATION ---


function main() {
var today = new Date();
var yesterday = new Date(today);
yesterday.setDate(today.getDate() - 1);

var todayString = Utilities.formatDate(today, AdsApp.currentAccount().getTimeZone(), 'yyyy-MM-dd');
var isFirstRun = (todayString === MANUAL_FIRST_RUN_DATE);

var reportStartDate;
var reportEndDate;

if (isFirstRun) {
Logger.log('First run detected. Fetching data from January 1st of this year.');
// Set the start date to January 1st of the current year.
var firstDayOfYear = new Date(today.getFullYear(), 0, 1); // Month is 0-indexed (0 = January)
reportStartDate = firstDayOfYear;
reportEndDate = yesterday;
} else {
Logger.log('Standard daily run. Fetching data for yesterday.');
reportStartDate = yesterday;
reportEndDate = yesterday;
}

// Format dates for the report query (YYYYMMDD)
var fromDateString = Utilities.formatDate(reportStartDate, 'GMT', 'yyyyMMdd');
var toDateString = Utilities.formatDate(reportEndDate, 'GMT', 'yyyyMMdd');

Logger.log('Requesting report for date range: ' + fromDateString + ' to ' + toDateString);

var fields = [
"Date",
"QualityScore",
"Criteria", // This is the keyword
"SearchPredictedCtr",
"CreativeQualityScore",
"PostClickQualityScore",
"Impressions",
"Clicks",
"Cost",
"Conversions",
"ConversionValue"
];

var report = AdsApp.report(
"SELECT " + fields.join(", ") +
" FROM KEYWORDS_PERFORMANCE_REPORT " +
" WHERE AdNetworkType1 = 'SEARCH' AND Impressions > 0 " +
" AND Status = 'ENABLED' AND AdGroupStatus = 'ENABLED' AND CampaignStatus = 'ENABLED'" +
" DURING " + fromDateString + "," + toDateString
);

var rows = report.rows();

var metrics = ["Impressions", "Clicks", "Cost", "Conversions", "ConversionValue", "KeywordCount"];
var headers = ["Below average", "Average", "Above average"];
var displayNames = {
"SearchPredictedCtr": "Expected CTR",
"CreativeQualityScore": "Ad relevance",
"PostClickQualityScore": "Landing page experience"
};

var data = []; // For Quality Score breakdown (main "Data" sheet)
var keywordQSData = []; // For "Keyword Quality Score" sheet
var keywordPerformanceData = []; // For "Keyword Performance" sheet

while (rows.hasNext()) {
var row = rows.next();
// Get the date for the current row and format it.
var currentDate = Utilities.formatDate(new Date(row["Date"]), 'GMT', 'yyyy-MM-dd');
var keyword = row["Criteria"]; // Get the keyword

// Collect data for Quality Score breakdown (main "Data" sheet)
metrics.forEach(function(metric) {
Object.keys(displayNames).forEach(function(factor) {
var header = row[factor];

if (headers.includes(header)) {
var existingEntry = data.find(e => e.date === currentDate && e.factor === displayNames[factor] && e.rating === header && e.metricName === metric);

if (existingEntry) {
existingEntry.value += parseFloat(row[metric]) || 0;
} else {
data.push({
date: currentDate,
factor: displayNames[factor],
metricName: metric,
rating: header,
value: parseFloat(row[metric]) || 0
});
}
}
});
});

// For "Keyword Quality Score" sheet (original functionality)
if (row["Impressions"]) {
var qualityScore = (row["QualityScore"] && !isNaN(row["QualityScore"])) ? parseFloat(row["QualityScore"]) : 0;
var impressions = qualityScore === 0 ? 0 : parseInt(row["Impressions"]);

keywordQSData.push({
date: currentDate,
keyword: keyword,
qualityScore: qualityScore,
impressions: impressions
});
}

// Handle KeywordCount for the main "Data" sheet
Object.keys(displayNames).forEach(function(factor) {
var header = row[factor];

if (headers.includes(header)) {
var existingEntry = data.find(e => e.date === currentDate && e.factor === displayNames[factor] && e.rating === header && e.metricName === "KeywordCount");

if (existingEntry) {
existingEntry.value += 1;
} else {
data.push({
date: currentDate,
factor: displayNames[factor],
metricName: "KeywordCount",
rating: header,
value: 1
});
}
}
});

// --- RE-ADDED: Collect data for Keyword Performance including Quality Score and Impressions ---
keywordPerformanceData.push({
date: currentDate,
keyword: keyword,
qualityScore: (row["QualityScore"] && !isNaN(row["QualityScore"])) ? parseFloat(row["QualityScore"]) : 0,
impressions: parseFloat(row["Impressions"]) || 0,
clicks: parseFloat(row["Clicks"]) || 0,
cost: parseFloat(row["Cost"]) || 0,
conversions: parseFloat(row["Conversions"]) || 0
});
// --- END RE-ADDED ---
}

var spreadsheet = SpreadsheetApp.openByUrl(SPREADSHEET_URL);

// Log the main data (Quality Score Breakdown)
var mainSheet = spreadsheet.getSheetByName('Data') || spreadsheet.insertSheet('Data');
mainSheet.clear(); // Note: This clears all previous data in the sheet upon every run.
mainSheet.appendRow(["Date", "Quality Score Factor", "Metric Name", "Rating", "Value"]);
data.sort((a, b) => a.date.localeCompare(b.date) || a.factor.localeCompare(b.factor) || a.metricName.localeCompare(b.metricName) || a.rating.localeCompare(b.rating));
data.forEach(function(entry) {
mainSheet.appendRow([entry.date, entry.factor, entry.metricName, entry.rating, entry.value]);
});

// Log the Keyword Quality Score data (original dedicated sheet)
var keywordQSSheet = spreadsheet.getSheetByName('Keyword Quality Score') || spreadsheet.insertSheet('Keyword Quality Score');
keywordQSSheet.clear(); // Note: This clears all previous data in the sheet upon every run.
keywordQSSheet.appendRow(["Date", "Keyword", "Quality Score", "Impressions"]);
keywordQSData.forEach(function(entry) {
keywordQSSheet.appendRow([entry.date, entry.keyword, entry.qualityScore, entry.impressions]);
});

// --- RE-ADDED: Log the Keyword Performance data with Quality Score and Impressions ---
var keywordPerformanceSheet = spreadsheet.getSheetByName('Keyword Quality Score') || spreadsheet.insertSheet('Keyword Quality Score');
keywordPerformanceSheet.clear(); // Clears all previous data in the sheet upon every run.
keywordPerformanceSheet.appendRow(["Date", "Keyword", "Quality Score", "Impressions", "Clicks", "Cost", "Conversions"]);
keywordPerformanceData.sort((a, b) => a.date.localeCompare(b.date) || a.keyword.localeCompare(b.keyword)); // Sort by date and then keyword
keywordPerformanceData.forEach(function(entry) {
keywordPerformanceSheet.appendRow([entry.date, entry.keyword, entry.qualityScore, entry.impressions, entry.clicks, entry.cost, entry.conversions]);
});
// --- END RE-ADDED ---

Logger.log('Script finished. Data written to spreadsheet: ' + SPREADSHEET_URL);
}
