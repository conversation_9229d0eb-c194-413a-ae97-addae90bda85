---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---

# Your rule content

**Generation getters setters in model**
Template:
public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }
    
    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        
        return $this;
    }

- needs to be generated based on consts with the prefix PROPERTY. the naming needs to be CamelCase
- The return types/types of the input need to be based from the migrations in the database/migrations folder
- no doc blocks
- For timestamps and datetimes use Illuminate\Support\Carbon which needs to be imported to the class.
- also generate the property consts based on the migration and for the value it needs to be the column name and the name of the consts needs to have PROPERTY_ prefix and in snake case and all caps


**Const properties**
- property consts need to always to be defined as followed: `public const PROPERTY_<name>`


**Import classes**
- Always import classes. is there a name conflict rename te newest import with something from the specific domain it is in.
- example `public const factory_class = \Tests\Factory\UserFactory` needs to be `public const factory_class = UserFactory` and needs to me imported


*Never use doc blocks*

**Build up model consts**
- TABLE_NAME
- PROPERTIES
- FACTORY
- RELATIONS


**Factories**
- Always call factories from the model, for exameple: `User::factory()->create()`
- only make the definition method and functions to determine states and set the correct model with `$model = <model>`
- Dont make a `public static function new()`
- when referencing to another model or relation use for example: `Account::factory()` always call the factory method

**Tests**
- when generating models in test always use factories
- use the following naming convetion for test function names: `test_ExampleDoesTheFollowing`
- use when possible functions from the TestCase parent
- when writing route tests use named routes instead of url's. For the name also include the prefix from the RouteServiceProvider. Always make sure all the required fields for requests are included.