<?php

namespace App\Http\Support\Requests;

use Illuminate\Foundation\Http\FormRequest;

class IndexRequest extends FormRequest
{
    public const REQUEST_PAGE = 'page';
    public const REQUEST_PAGE_SIZE = 'page_size';
    public const REQUEST_SEARCH = 'search';

    public function rules(): array
    {
        return [
            self::REQUEST_PAGE => ['sometimes', 'nullable', 'numeric'],
            self::REQUEST_PAGE_SIZE => ['sometimes', 'nullable', 'numeric'],
            self::REQUEST_SEARCH => ['sometimes', 'nullable', 'string'],
        ];
    }

    public function getPage(): ?int
    {
        return $this->input(self::REQUEST_PAGE);
    }

    public function getPageSize(): int
    {
        return $this->input(self::REQUEST_PAGE_SIZE) ?? 15;
    }

    public function getSearch(): ?string
    {
        return $this->input(self::REQUEST_SEARCH);
    }
}
