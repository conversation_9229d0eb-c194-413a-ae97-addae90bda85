<?php

namespace App\Http\API\Webhooks\Stripe\Controllers;

use App\Domain\Stripe\Actions\HandleStripeEvent;
use App\Http\Support\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Stripe\Event;
use UnexpectedValueException;

class StripeController extends Controller
{
    public function webhook(Request $request): JsonResponse
    {
        $event = null;

        try {
            $event = Event::constructFrom($request->toArray());
        } catch (UnexpectedValueException $ex) {
            abort(400);
        }

        if (!$event) {
            abort(400);
        }

        app(HandleStripeEvent::class)->execute($event);

        return response()->json();
    }
}
