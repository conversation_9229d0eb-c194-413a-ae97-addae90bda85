<?php

namespace App\Http\API\Dashboard\Dashboard\Resources;

use App\Domain\Dashboard\Models\Section;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SectionResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_TYPE = 'type';
    public const RESPONSE_PLACEMENT = 'placement';
    public const RESPONSE_WIDGETS = 'widgets';

    /** @var Section $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_TYPE => $this->resource->getType(),
            self::RESPONSE_PLACEMENT => $this->resource->getPlacement(),
            self::RESPONSE_WIDGETS => WidgetResource::collection($this->whenLoaded(Section::RELATION_WIDGETS)),
        ];
    }
}
