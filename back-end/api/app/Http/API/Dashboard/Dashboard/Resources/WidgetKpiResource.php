<?php

namespace App\Http\API\Dashboard\Dashboard\Resources;

use App\Domain\Dashboard\Models\WidgetKpi;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetKpiResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_TARGET = 'target';
    public const RESPONSE_YEAR = 'year';
    public const RESPONSE_UNIT = 'unit';

    /** @var WidgetKpi $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_TARGET => $this->resource->getTarget(),
            self::RESPONSE_YEAR => $this->resource->getYear(),
            self::RESPONSE_UNIT => $this->resource->getUnit(),
        ];
    }
}
