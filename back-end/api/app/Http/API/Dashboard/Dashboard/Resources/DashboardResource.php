<?php

namespace App\Http\API\Dashboard\Dashboard\Resources;

use App\Domain\Dashboard\Actions\GetSignedDashboardUrl;
use App\Domain\Dashboard\Models\Dashboard;
use App\Http\API\Dashboard\Profile\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DashboardResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_CREATED_AT = 'created_at';
    public const RESPONSE_UPDATED_AT = 'updated_at';
    public const RESPONSE_PAGES = 'pages';
    public const RESPONSE_USER = 'user';
    public const RESPONSE_SOURCES = 'sources';
    public const RESPONSE_URL = 'url';
    public const RESPONSE_RECIPIENT = 'recipient';
    public const RESPONSE_BUSINESS_TYPE = 'business_type';

    /** @var Dashboard $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_CREATED_AT => $this->resource->getCreatedAt(),
            self::RESPONSE_UPDATED_AT => $this->resource->getUpdatedAt(),
            self::RESPONSE_BUSINESS_TYPE => $this->resource->getBusinessType(),
            self::RESPONSE_PAGES => PageResource::collection($this->whenLoaded(Dashboard::RELATION_PAGES)),
            self::RESPONSE_USER => UserResource::make($this->whenLoaded(Dashboard::RELATION_USER)),
            self::RESPONSE_SOURCES => DashboardSourceResource::collection(
                $this->whenLoaded(Dashboard::RELATION_SOURCES),
            ),
            self::RESPONSE_URL => app(GetSignedDashboardUrl::class)->execute($this->resource),
            self::RESPONSE_RECIPIENT => RecipientResource::make($this->whenLoaded(Dashboard::RELATION_RECIPIENT)),
        ];
    }
}
