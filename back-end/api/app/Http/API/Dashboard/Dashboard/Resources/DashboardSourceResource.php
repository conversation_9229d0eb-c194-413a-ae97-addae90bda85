<?php

namespace App\Http\API\Dashboard\Dashboard\Resources;

use App\Domain\Dashboard\Models\DashboardSource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class DashboardSourceResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_SOURCE_ID = 'source_id';
    public const RESPONSE_SOURCE_TYPE = 'source_type';

    /** @var DashboardSource $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_SOURCE_ID => $this->resource->getSourceId(),
            self::RESPONSE_SOURCE_TYPE => Str::afterLast($this->resource->getSourceType(), '\\'),
        ];
    }
}
