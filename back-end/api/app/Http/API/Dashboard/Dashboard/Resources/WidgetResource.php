<?php

namespace App\Http\API\Dashboard\Dashboard\Resources;

use App\Domain\Dashboard\Models\Widget;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_TYPE = 'type';
    public const RESPONSE_KPIS = 'kpis';

    /** @var Widget $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_TYPE => $this->resource->getType(),
            self::RESPONSE_KPIS => WidgetKpiResource::collection($this->whenLoaded(Widget::RELATION_KPIS)),
        ];
    }
}
