<?php

namespace App\Http\API\Dashboard\Dashboard\Controllers;

use App\Domain\Dashboard\Actions\GetDashboardConfiguration;
use App\Domain\Dashboard\Actions\UpdateOrCreateDashboard;
use App\Domain\Dashboard\Models\Dashboard;
use App\Domain\Dashboard\Models\Page;
use App\Domain\Dashboard\Models\Section;
use App\Domain\Dashboard\Models\Widget;
use App\Http\API\Dashboard\Dashboard\Requests\DashboardRequest;
use App\Http\API\Dashboard\Dashboard\Resources\DashboardResource;
use App\Http\Support\Controllers\Controller;
use App\Http\Support\Requests\IndexRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class DashboardController extends Controller
{
    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        $dashboards = Dashboard::query()->when(
            $request->getSearch(),
            fn(Builder $query) => $query->whereCustomLike(Dashboard::PROPERTY_NAME, $request->getSearch()),
        );

        if ($request->getPage()) {
            $dashboards = $dashboards->paginate($request->getPageSize());
        } else {
            $dashboards = $dashboards->get();
        }

        return DashboardResource::collection($dashboards);
    }

    public function show(int $id): DashboardResource
    {
        $dashboard = Dashboard::query()
            ->with([
                implode('.', [
                    Dashboard::RELATION_PAGES,
                    Page::RELATION_SECTIONS,
                    Section::RELATION_WIDGETS,
                    Widget::RELATION_KPIS,
                ]),
                Dashboard::RELATION_SOURCES,
                Dashboard::RELATION_USER,
                Dashboard::RELATION_RECIPIENT,
            ])
            ->findOrFail($id);

        return DashboardResource::make($dashboard);
    }

    public function store(DashboardRequest $request): DashboardResource
    {
        $dashboard = app(UpdateOrCreateDashboard::class)->execute($request->toDto(), $this->getUser()->getAccount());

        return DashboardResource::make($dashboard);
    }

    public function update(DashboardRequest $request, int $id): DashboardResource
    {
        $dashboard = Dashboard::query()->findOrFail($id);

        $dashboard = app(UpdateOrCreateDashboard::class)->execute(
            $request->toDto(),
            $this->getUser()->getAccount(),
            $dashboard,
        );

        return DashboardResource::make($dashboard);
    }

    public function delete(int $id): JsonResponse
    {
        $dashboard = Dashboard::query()->findOrFail($id);

        $dashboard->delete();

        return response()->json();
    }

    public function configuration(): JsonResponse
    {
        return response()->json(['data' => app(GetDashboardConfiguration::class)->execute()]);
    }
}
