<?php

namespace App\Http\API\Dashboard\Dashboard\Requests;

use App\Domain\Dashboard\Dto\DashboardDto;
use App\Domain\Dashboard\Dto\GeneralDto;
use App\Domain\Dashboard\Dto\KpiDto;
use App\Domain\Dashboard\Dto\KpiEntryDto;
use App\Domain\Dashboard\Dto\PageDto;
use App\Domain\Dashboard\Dto\RecipientDto;
use App\Domain\Dashboard\Dto\SectionDto;
use App\Domain\Dashboard\Dto\SourceDto;
use App\Domain\Dashboard\Support\Enums\DashboardBusinessType;
use App\Domain\Dashboard\Support\Enums\PageType;
use App\Domain\Dashboard\Support\Enums\SectionType;
use App\Domain\Dashboard\Support\Enums\WidgetKpiUnit;
use App\Domain\Dashboard\Support\Enums\WidgetType;
use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Profile\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DashboardRequest extends FormRequest
{
    public const REQUEST_GENERAL = 'general';
    public const REQUEST_NAME = 'name';
    public const REQUEST_PAGES = 'pages';
    public const REQUEST_SECTIONS = 'sections';
    public const REQUEST_ENABLED = 'enabled';
    public const REQUEST_TYPE = 'type';
    public const REQUEST_USER_ID = 'user_id';
    public const REQUEST_SOURCES = 'sources';
    public const REQUEST_GOOGLE_AD_ACCOUNT_IDS = 'google_ad_account_ids';
    public const REQUEST_RECIPIENT = 'recipient';
    public const REQUEST_COMPANY = 'company';
    public const REQUEST_KPIS = 'kpis';
    public const REQUEST_ENTRIES = 'entries';
    public const REQUEST_YEAR = 'year';
    public const REQUEST_TARGET = 'target';
    public const REQUEST_UNIT = 'unit';
    public const REQUEST_BUSINESS_TYPE = 'business_type';

    public function rules(): array
    {
        return array_merge(
            $this->generalRules(),
            $this->pagesRules(),
            $this->sourcesRules(),
            $this->recipientRules(),
            $this->kpisRules(),
        );
    }

    public function toDto(): DashboardDto
    {
        return (new DashboardDto())
            ->setGeneral($this->getGeneralDto())
            ->setPages($this->getPages())
            ->setSources($this->getSources())
            ->setRecipient($this->getRecipientDto())
            ->setKpis($this->getKpis());
    }

    private function getGeneralDto(): GeneralDto
    {
        return (new GeneralDto())
            ->setName($this->input(implode('.', [self::REQUEST_GENERAL, self::REQUEST_NAME])))
            ->setUserId($this->input(implode('.', [self::REQUEST_GENERAL, self::REQUEST_USER_ID])))
            ->setType(
                DashboardBusinessType::from(
                    $this->input(implode('.', [self::REQUEST_GENERAL, self::REQUEST_BUSINESS_TYPE])),
                ),
            );
    }

    /**
     * @return SourceDto[]
     */
    private function getSources(): array
    {
        $dataInputs = [
            implode('.', [self::REQUEST_SOURCES, self::REQUEST_GOOGLE_AD_ACCOUNT_IDS]) => GoogleAdAccount::class,
        ];

        $sources = [];

        foreach ($dataInputs as $dataInput => $type) {
            $sources[] = (new SourceDto())->setIds($this->input($dataInput))->setType($type);
        }

        return $sources;
    }

    /**
     * @return PageDto[];
     */
    private function getPages(): array
    {
        $pages = [];

        foreach ($this->input(self::REQUEST_PAGES) as $page) {
            $sections = [];

            foreach ($page[self::REQUEST_SECTIONS] as $section) {
                $sections[] = (new SectionDto())->setType(SectionType::from($section[self::REQUEST_TYPE]));
            }

            $pages[] = (new PageDto())->setType(PageType::from($page[self::REQUEST_TYPE]))->setSections($sections);
        }

        return $pages;
    }

    private function getRecipientDto(): RecipientDto
    {
        return (new RecipientDto())->setCompany(
            $this->input(implode('.', [self::REQUEST_RECIPIENT, self::REQUEST_COMPANY])),
        );
    }

    /**
     * @return KpiDto[]
     */
    private function getKpis(): array
    {
        $kpis = [];

        foreach ($this->input(self::REQUEST_KPIS) as $kpi) {
            $entries = [];

            foreach ($kpi[self::REQUEST_ENTRIES] as $entry) {
                $entries[] = (new KpiEntryDto())
                    ->setUnit($entry[self::REQUEST_UNIT])
                    ->setYear($entry[self::REQUEST_YEAR])
                    ->setTarget($entry[self::REQUEST_TARGET]);
            }

            $kpis[] = (new KpiDto())->setType(WidgetType::from($kpi[self::REQUEST_TYPE]))->setEntries($entries);
        }

        return $kpis;
    }

    private function generalRules(): array
    {
        return [
            self::REQUEST_GENERAL => ['required', 'array'],
            implode('.', [self::REQUEST_GENERAL, self::REQUEST_NAME]) => ['required', 'string'],
            implode('.', [self::REQUEST_GENERAL, self::REQUEST_USER_ID]) => [
                'required',
                'numeric',
                Rule::exists(User::TABLE_NAME, User::PROPERTY_ID),
            ],
            implode('.', [self::REQUEST_GENERAL, self::REQUEST_BUSINESS_TYPE]) => [
                'required',
                'string',
                Rule::enum(DashboardBusinessType::class),
            ],
        ];
    }

    private function pagesRules(): array
    {
        return [
            self::REQUEST_PAGES => ['required', 'array'],
            implode('.', [self::REQUEST_PAGES, '*', self::REQUEST_ENABLED]) => ['boolean'],
            implode('.', [self::REQUEST_PAGES, '*', self::REQUEST_TYPE]) => ['string', Rule::enum(PageType::class)],
            implode('.', [self::REQUEST_PAGES, '*', self::REQUEST_SECTIONS]) => ['array'],
            implode('.', [self::REQUEST_PAGES, '*', self::REQUEST_SECTIONS, '*', self::REQUEST_ENABLED]) => ['boolean'],
            implode('.', [self::REQUEST_PAGES, '*', self::REQUEST_SECTIONS, '*', self::REQUEST_TYPE]) => [
                'string',
                Rule::enum(SectionType::class),
            ],
        ];
    }

    private function sourcesRules(): array
    {
        return [
            self::REQUEST_SOURCES => ['required', 'array'],
            implode('.', [self::REQUEST_SOURCES, self::REQUEST_GOOGLE_AD_ACCOUNT_IDS]) => ['array', 'nullable'],
            implode('.', [self::REQUEST_SOURCES, self::REQUEST_GOOGLE_AD_ACCOUNT_IDS, '*']) => [
                Rule::exists(GoogleAdAccount::TABLE_NAME, GoogleAdAccount::PROPERTY_ID),
            ],
        ];
    }

    private function recipientRules(): array
    {
        return [
            self::REQUEST_RECIPIENT => ['present', 'array'],
            implode('.', [self::REQUEST_RECIPIENT, self::REQUEST_COMPANY]) => ['required', 'string'],
        ];
    }

    private function kpisRules(): array
    {
        return [
            self::REQUEST_KPIS => ['array', 'present'],
            implode('.', [self::REQUEST_KPIS, '*', self::REQUEST_TYPE]) => ['string', Rule::enum(WidgetType::class)],
            implode('.', [self::REQUEST_KPIS, '*', self::REQUEST_ENTRIES]) => ['array'],
            implode('.', [self::REQUEST_KPIS, '*', self::REQUEST_ENTRIES, '*', self::REQUEST_YEAR]) => ['numeric'],
            implode('.', [self::REQUEST_KPIS, '*', self::REQUEST_ENTRIES, '*', self::REQUEST_TARGET]) => ['numeric'],
            implode('.', [self::REQUEST_KPIS, '*', self::REQUEST_ENTRIES, '*', self::REQUEST_UNIT]) => [
                Rule::enum(WidgetKpiUnit::class),
                'nullable',
            ],
        ];
    }
}
