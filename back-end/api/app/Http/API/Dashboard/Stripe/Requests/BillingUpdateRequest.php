<?php

namespace App\Http\API\Dashboard\Stripe\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BillingUpdateRequest extends FormRequest
{
    public const REQUEST_EMAIL = 'email';
    public const REQUEST_STREET = 'street';
    public const REQUEST_HOUSE_NUMBER = 'house_number';
    public const REQUEST_HOUSE_NUMBER_ADDITION = 'house_number_addition';
    public const REQUEST_ZIPCODE = 'zipcode';
    public const REQUEST_CITY = 'city';
    public const REQUEST_COUNTRY = 'country';

    public function rules(): array
    {
        return [
            self::REQUEST_EMAIL => ['sometimes', 'string', 'nullable'],
            self::REQUEST_STREET => ['sometimes', 'string', 'nullable'],
            self::REQUEST_HOUSE_NUMBER => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_HOUSE_NUMBER_ADDITION => ['sometimes', 'string', 'nullable'],
            self::REQUEST_ZIPCODE => ['sometimes', 'string', 'nullable'],
            self::REQUEST_CITY => ['sometimes', 'string', 'nullable'],
            self::REQUEST_COUNTRY => ['sometimes', 'string', 'nullable'],
        ];
    }

    public function getEmail(): ?string
    {
        return $this->input(self::REQUEST_EMAIL);
    }

    public function getStreet(): ?string
    {
        return $this->input(self::REQUEST_STREET);
    }

    public function getHouseNumber(): ?int
    {
        return $this->input(self::REQUEST_HOUSE_NUMBER);
    }

    public function getHouseNumberAddition(): ?string
    {
        return $this->input(self::REQUEST_HOUSE_NUMBER_ADDITION);
    }

    public function getZipcode(): ?string
    {
        return $this->input(self::REQUEST_ZIPCODE);
    }

    public function getCity(): ?string
    {
        return $this->input(self::REQUEST_CITY);
    }

    public function getCountry(): ?string
    {
        return $this->input(self::REQUEST_COUNTRY);
    }
}
