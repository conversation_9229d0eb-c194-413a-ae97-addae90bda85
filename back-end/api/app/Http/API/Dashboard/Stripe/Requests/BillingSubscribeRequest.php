<?php

namespace App\Http\API\Dashboard\Stripe\Requests;

use App\Domain\Stripe\Models\Product;
use App\Domain\Stripe\Support\Enums\RecurringInterval;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BillingSubscribeRequest extends FormRequest
{
    public const REQUEST_PRODUCT_ID = 'product_id';
    public const REQUEST_RECURRING_INTERVAL = 'recurring_interval';

    public function rules(): array
    {
        return [
            self::REQUEST_PRODUCT_ID => ['required', Rule::exists(Product::TABLE_NAME, Product::PROPERTY_ID)],
            self::REQUEST_RECURRING_INTERVAL => ['required', 'string', Rule::enum(RecurringInterval::class)],
        ];
    }

    public function getProduct(): Product
    {
        return Product::query()
            ->with([Product::RELATION_MONTHLY_PRICE, Product::RELATION_YEARLY_PRICE])
            ->findOrFail($this->input(self::REQUEST_PRODUCT_ID));
    }

    public function getRecurringInterval(): RecurringInterval
    {
        return RecurringInterval::from($this->input(self::REQUEST_RECURRING_INTERVAL));
    }
}
