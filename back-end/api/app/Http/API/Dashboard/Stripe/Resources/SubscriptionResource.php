<?php

namespace App\Http\API\Dashboard\Stripe\Resources;

use App\Domain\Stripe\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_PRODUCT_ID = 'product_id';
    public const RESPONSE_STATUS = 'status';
    public const RESPONSE_VALID = 'valid';
    public const RESPONSE_TRIAL_ENDS_AT = 'trail_ends_at';
    public const RESPONSE_PERIOD_STARTS_AT = 'period_starts_at';
    public const RESPONSE_PRICE = 'price';
    public const RESPONSE_PRODUCT = 'product';
    public const RESPONSE_ACTIVE_SCHEDULE = 'active_schedule';
    public const RESPONSE_TRAIL_DAYS_REMAINING = 'trial_days_remaining';

    /** @var Subscription $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_PRODUCT_ID => $this->resource->getProductId(),
            self::RESPONSE_STATUS => $this->resource->getStripeStatus(),
            self::RESPONSE_VALID => $this->resource->valid(),
            self::RESPONSE_TRIAL_ENDS_AT => $this->resource->getTrialEndsAt(),
            self::RESPONSE_PERIOD_STARTS_AT => $this->resource->getPeriodStartsAt(),
            self::RESPONSE_TRAIL_DAYS_REMAINING => $this->resource->getTrialEndsAt()?->diffInDays(now()),
            self::RESPONSE_PRICE => PriceResource::make($this->whenLoaded(Subscription::RELATION_PRICE)),
            self::RESPONSE_PRODUCT => ProductResource::make($this->whenLoaded(Subscription::RELATION_PRODUCT)),
            self::RESPONSE_ACTIVE_SCHEDULE => SubscriptionScheduleResource::make(
                $this->whenLoaded(Subscription::RELATION_ACTIVE_SCHEDULE),
            ),
        ];
    }
}
