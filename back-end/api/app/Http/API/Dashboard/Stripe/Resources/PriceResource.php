<?php

namespace App\Http\API\Dashboard\Stripe\Resources;

use App\Domain\Stripe\Models\Price;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PriceResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_PRICE = 'price';
    public const RESPONSE_RECURRING_INTERVAL = 'recurring_interval';
    public const RESPONSE_CURRENCY = 'currency';

    /** @var Price $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_PRICE => $this->resource->getPrice(),
            self::RESPONSE_RECURRING_INTERVAL => $this->resource->getRecurringInterval(),
            self::RESPONSE_CURRENCY => strtoupper($this->resource->getCurrency()),
        ];
    }
}
