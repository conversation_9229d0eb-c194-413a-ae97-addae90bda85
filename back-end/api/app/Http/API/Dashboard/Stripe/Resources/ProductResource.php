<?php

namespace App\Http\API\Dashboard\Stripe\Resources;

use App\Domain\Stripe\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_DESCRIPTION = 'description';
    public const RESPONSE_FEATURES = 'features';
    public const RESPONSE_LIMIT = 'limit';
    public const RESPONSE_WHITELABEL = 'whitelabel';
    public const RESPONSE_MONTHLY_PRICE = 'monthly_price';
    public const RESPONSE_YEARLY_PRICE = 'yearly_price';

    /** @var Product $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_DESCRIPTION => $this->resource->getDescription(),
            self::RESPONSE_FEATURES => $this->resource->getFeatures(),
            self::RESPONSE_LIMIT => $this->resource->getLimit(),
            self::RESPONSE_WHITELABEL => $this->resource->getWhitelabel(),
            self::RESPONSE_MONTHLY_PRICE => PriceResource::make($this->whenLoaded(Product::RELATION_MONTHLY_PRICE)),
            self::RESPONSE_YEARLY_PRICE => PriceResource::make($this->whenLoaded(Product::RELATION_YEARLY_PRICE)),
        ];
    }
}
