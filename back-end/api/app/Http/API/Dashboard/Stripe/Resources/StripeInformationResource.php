<?php

namespace App\Http\API\Dashboard\Stripe\Resources;

use App\Domain\Stripe\Models\StripeInformation;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StripeInformationResource extends JsonResource
{
    public const RESPONSE_EMAIL = 'email';
    public const RESPONSE_PM_TYPE = 'pm_type';
    public const RESPONSE_PM_LAST_FOUR = 'pm_last_four';
    public const RESPONSE_STREET = 'street';
    public const RESPONSE_HOUSE_NUMBER = 'house_number';
    public const RESPONSE_HOUSE_NUMBER_ADDITION = 'house_number_addition';
    public const RESPONSE_ZIPCODE = 'zipcode';
    public const RESPONSE_CITY = 'city';
    public const RESPONSE_COUNTRY = 'country';

    /** @var StripeInformation $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_EMAIL => $this->resource->getEmail(),
            self::RESPONSE_PM_TYPE => $this->resource->getPmType(),
            self::RESPONSE_PM_LAST_FOUR => $this->resource->getPmLastFour(),
            self::RESPONSE_STREET => $this->resource->getStreet(),
            self::RESPONSE_HOUSE_NUMBER => $this->resource->getHouseNumber(),
            self::RESPONSE_HOUSE_NUMBER_ADDITION => $this->resource->getHouseNumberAddition(),
            self::RESPONSE_ZIPCODE => $this->resource->getZipcode(),
            self::RESPONSE_CITY => $this->resource->getCity(),
            self::RESPONSE_COUNTRY => $this->resource->getCountry(),
        ];
    }
}
