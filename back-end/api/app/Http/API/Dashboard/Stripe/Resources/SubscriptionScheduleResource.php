<?php

namespace App\Http\API\Dashboard\Stripe\Resources;

use App\Domain\Stripe\Models\SubscriptionSchedule;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionScheduleResource extends JsonResource
{
    public const RESPONSE_START_DATE = 'start_date';
    public const RESPONSE_PRICE = 'price';
    public const RESPONSE_PRODUCT_ID = 'product_id';
    public const RESPONSE_PRICE_ID = 'price_id';

    /** @var SubscriptionSchedule $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_START_DATE => $this->resource->getStartDate(),
            self::RESPONSE_PRICE_ID => $this->resource->getPriceId(),
            self::RESPONSE_PRODUCT_ID => $this->resource->getProductId(),
            self::RESPONSE_PRICE => PriceResource::make($this->whenLoaded(SubscriptionSchedule::RELATION_PRICE)),
        ];
    }
}
