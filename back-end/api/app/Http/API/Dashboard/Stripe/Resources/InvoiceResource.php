<?php

namespace App\Http\API\Dashboard\Stripe\Resources;

use App\Domain\Stripe\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InvoiceResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_STRIPE_ID = 'stripe_id';
    public const RESPONSE_NUMBER = 'number';
    public const RESPONSE_STATUS = 'status';
    public const RESPONSE_TOTAL_INCLUDING_VAT = 'total_including_vat';
    public const RESPONSE_TOTAL_EXCLUDING_VAT = 'total_excluding_vat';
    public const RESPONSE_DUE_DATE = 'due_date';
    public const RESPONSE_INVOICED_DATE = 'invoiced_date';
    public const RESPONSE_PAID_AT = 'paid_at';
    public const RESPONSE_CREATED_AT = 'created_at';
    public const RESPONSE_UPDATED_AT = 'updated_at';

    /** @var Invoice $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_STRIPE_ID => $this->resource->getStripeId(),
            self::RESPONSE_NUMBER => $this->resource->getNumber(),
            self::RESPONSE_STATUS => $this->resource->getStatus(),
            self::RESPONSE_TOTAL_INCLUDING_VAT => $this->resource->getTotalIncludingVat(),
            self::RESPONSE_TOTAL_EXCLUDING_VAT => $this->resource->getTotalExcludingVat(),
            self::RESPONSE_DUE_DATE => $this->resource->getDueDate(),
            self::RESPONSE_INVOICED_DATE => $this->resource->getInvoicedDate(),
            self::RESPONSE_PAID_AT => $this->resource->getPaidAt(),
            self::RESPONSE_CREATED_AT => $this->resource->getCreatedAt(),
            self::RESPONSE_UPDATED_AT => $this->resource->getUpdatedAt(),
        ];
    }
}
