<?php

namespace App\Http\API\Dashboard\Stripe\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Stripe\SetupIntent as SetupIntentModel;

class BillingSetupIntentResource extends JsonResource
{
    public const RESPONSE_CLIENT_SECRET = 'client_secret';

    /** @var SetupIntentModel $resource */
    public $resource;

    /**
     * @param $request
     * @return string[]
     */
    public function toArray($request): array
    {
        return [
            self::RESPONSE_CLIENT_SECRET => $this->resource->client_secret,
        ];
    }
}
