<?php

namespace App\Http\API\Dashboard\Stripe\Controllers;

use App\Domain\Stripe\Actions\CreateTrialSubscription;
use App\Domain\Stripe\Actions\ScheduleSubscriptionChange;
use App\Domain\Stripe\Models\Price;
use App\Domain\Stripe\Models\Product;
use App\Domain\Stripe\Models\StripeInformation;
use App\Domain\Stripe\Models\Subscription;
use App\Domain\Stripe\Models\SubscriptionSchedule;
use App\Domain\Stripe\Support\Enums\SubscriptionStripeStatus;
use App\Http\API\Dashboard\Stripe\Requests\BillingSubscribeRequest;
use App\Http\API\Dashboard\Stripe\Requests\BillingUpdateRequest;
use App\Http\API\Dashboard\Stripe\Resources\BillingSetupIntentResource;
use App\Http\API\Dashboard\Stripe\Resources\ProductResource;
use App\Http\API\Dashboard\Stripe\Resources\StripeInformationResource;
use App\Http\API\Dashboard\Stripe\Resources\SubscriptionResource;
use App\Http\Support\Controllers\Controller;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class BillingController extends Controller
{
    public function update(BillingUpdateRequest $request): StripeInformationResource
    {
        $information = StripeInformation::query()->updateOrCreate(
            [
                StripeInformation::PROPERTY_ACCOUNT_ID => $this->getUser()->getAccountId(),
            ],
            [
                StripeInformation::PROPERTY_EMAIL => $request->getEmail(),
                StripeInformation::PROPERTY_STREET => $request->getStreet(),
                StripeInformation::PROPERTY_HOUSE_NUMBER => $request->getHouseNumber(),
                StripeInformation::PROPERTY_HOUSE_NUMBER_ADDITION => $request->getHouseNumberAddition(),
                StripeInformation::PROPERTY_ZIPCODE => $request->getZipcode(),
                StripeInformation::PROPERTY_CITY => $request->getCity(),
                StripeInformation::PROPERTY_COUNTRY => $request->getCountry(),
            ],
        );

        return StripeInformationResource::make($information);
    }

    public function setupIntent(): BillingSetupIntentResource
    {
        $account = $this->getUser()?->getAccount();

        if (!$account->getStripeId()) {
            $account->createAsStripeCustomer();
        }

        $setupIntent = $account->createSetupIntent([
            'customer' => $account->getStripeId(),
            'payment_method_types' => explode(',', config('eaglo.stripe.subscription.payment_method_types')),
        ]);

        return BillingSetupIntentResource::make($setupIntent);
    }

    public function products(): AnonymousResourceCollection
    {
        $products = Product::query()
            ->with([Product::RELATION_YEARLY_PRICE, Product::RELATION_MONTHLY_PRICE])
            ->withAggregate(Product::RELATION_MONTHLY_PRICE, Price::PROPERTY_PRICE)
            ->where(Product::PROPERTY_IS_HIDDEN, false)
            ->orderBy('monthly_price_price')
            ->get();

        return ProductResource::collection($products);
    }

    public function subscribe(BillingSubscribeRequest $request): SubscriptionResource
    {
        $product = $request->getProduct();

        $account = $this->getUser()?->getAccount();

        if (!$account->hasDefaultPaymentMethod()) {
            abort(404, 'missing payment method');
        }

        $subscription = $account->getActiveSubscription();

        $moreExpensiveProduct =
            $subscription && $subscription->getPrice()?->getPrice() < $product->getMonthlyPrice()?->getPrice();

        $price = $request->getRecurringInterval()->getPriceFromProduct($product);

        if ($moreExpensiveProduct) {
            $subscription = $subscription->swap($price?->getStripeId());
        }

        if ($subscription && !$moreExpensiveProduct) {
            app(ScheduleSubscriptionChange::class)->execute($subscription, $price);
        }

        if (!$subscription) {
            $subscription = $account->newSubscription('default', $price->getStripeId())->create();
        }

        $subscription->load(
            Subscription::RELATION_PRICE,
            implode('.', [Subscription::RELATION_ACTIVE_SCHEDULE, SubscriptionSchedule::RELATION_PRICE]),
        );

        return SubscriptionResource::make($subscription);
    }

    public function extendTrial(): SubscriptionResource
    {
        $account = $this->getUser()?->getAccount();

        if (!$account) {
            abort(404);
        }

        $subscription = $account->getActiveSubscription();

        if ($subscription && $subscription?->getStripeStatus() !== SubscriptionStripeStatus::TRIALING) {
            abort(404);
        }

        if (!$subscription) {
            $subscription = app(CreateTrialSubscription::class)->execute($account, 7);
        } else {
            $subscription->extendTrial(now()->addDays(7))->save();
        }

        return SubscriptionResource::make($subscription);
    }
}
