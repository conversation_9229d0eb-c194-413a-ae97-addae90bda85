<?php

namespace App\Http\API\Dashboard\Stripe\Controllers;

use App\Domain\Stripe\Models\Invoice;
use App\Http\API\Dashboard\Stripe\Resources\InvoiceResource;
use App\Http\Support\Controllers\Controller;
use App\Http\Support\Requests\IndexRequest;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Symfony\Component\HttpFoundation\Response;

class InvoiceController extends Controller
{
    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        $invoices = Invoice::query()
            ->where(Invoice::PROPERTY_ACCOUNT_ID, $this->getUser()?->getAccount()?->getId())
            ->orderBy(Invoice::PROPERTY_INVOICED_DATE, 'DESC');

        if ($request->getPage()) {
            $invoices = $invoices->paginate($request->getPageSize());
        } else {
            $invoices = $invoices->get();
        }

        return InvoiceResource::collection($invoices);
    }

    public function download(string $id): Response
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::query()->findOrFail($id);

        return $this->getUser()?->getAccount()?->downloadInvoice($invoice->getStripeId());
    }
}
