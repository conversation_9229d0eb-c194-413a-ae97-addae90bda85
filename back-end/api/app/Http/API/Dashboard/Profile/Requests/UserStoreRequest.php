<?php

namespace App\Http\API\Dashboard\Profile\Requests;

use App\Domain\Profile\Dto\UpdateOrCreateUserDto;
use App\Domain\Profile\Models\User;
use Illuminate\Validation\Rule;

class UserStoreRequest extends UserRequest
{
    public const REQUEST_EMAIL = 'email';

    public function rules(): array
    {
        return array_merge(parent::rules(), [
            self::REQUEST_EMAIL => [
                'required',
                'string',
                'email',
                Rule::unique(User::TABLE_NAME, User::PROPERTY_EMAIL),
            ],
        ]);
    }

    public function toDto(): UpdateOrCreateUserDto
    {
        $dto = parent::toDto();

        $dto->setEmail($this->input(self::REQUEST_EMAIL));

        return $dto;
    }
}
