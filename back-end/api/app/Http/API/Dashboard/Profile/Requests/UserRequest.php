<?php

namespace App\Http\API\Dashboard\Profile\Requests;

use App\Domain\Profile\Dto\UpdateOrCreateUserDto;
use App\Domain\Profile\Support\Enums\UserRole;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserRequest extends FormRequest
{
    public const REQUEST_FIRSTNAME = 'firstname';
    public const REQUEST_LASTNAME = 'lastname';
    public const REQUEST_IMAGE = 'image';
    public const REQUEST_ROLE = 'role';

    public function rules(): array
    {
        return [
            self::REQUEST_FIRSTNAME => ['required', 'string'],
            self::REQUEST_LASTNAME => ['required', 'string'],
            self::REQUEST_ROLE => ['required', Rule::enum(UserRole::class)],
            self::REQUEST_IMAGE => ['sometimes', 'nullable', 'image'],
        ];
    }

    public function toDto(): UpdateOrCreateUserDto
    {
        return (new UpdateOrCreateUserDto())
            ->setFirstname($this->input(self::REQUEST_FIRSTNAME))
            ->setLastname($this->input(self::REQUEST_LASTNAME))
            ->setImage($this->file(self::REQUEST_IMAGE))
            ->setRole(UserRole::from($this->input(self::REQUEST_ROLE)));
    }
}
