<?php

namespace App\Http\API\Dashboard\Profile\Resources;

use App\Domain\Profile\Models\Account;
use App\Http\API\Dashboard\Stripe\Resources\StripeInformationResource;
use App\Http\API\Dashboard\Stripe\Resources\SubscriptionResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AccountResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_STRIPE_INFORMATION = 'stripe_information';
    public const RESPONSE_SUBSCRIPTION = 'subscription';

    /** @var Account $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_STRIPE_INFORMATION => StripeInformationResource::make(
                $this->whenLoaded(Account::RELATION_STRIPE_INFORMATION),
            ),
            self::RESPONSE_SUBSCRIPTION => SubscriptionResource::make(
                $this->whenLoaded(Account::RELATION_ACTIVE_SUBSCRIPTION),
            ),
        ];
    }
}
