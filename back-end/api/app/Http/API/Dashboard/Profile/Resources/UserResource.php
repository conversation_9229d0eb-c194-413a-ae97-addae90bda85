<?php

namespace App\Http\API\Dashboard\Profile\Resources;

use App\Domain\Profile\Models\User;
use App\Domain\Support\Actions\GetMediaUrl;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_FIRSTNAME = 'firstname';
    public const RESPONSE_LASTNAME = 'lastname';
    public const RESPONSE_EMAIL = 'email';
    public const RESPONSE_IMAGE_URL = 'image_url';
    public const RESPONSE_ROLE = 'role';

    /** @var User $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_FIRSTNAME => $this->resource->getFirstname(),
            self::RESPONSE_LASTNAME => $this->resource->getLastname(),
            self::RESPONSE_EMAIL => $this->resource->getEmail(),
            self::RESPONSE_ROLE => $this->resource->getRole(),
            self::RESPONSE_IMAGE_URL => app(GetMediaUrl::class)->execute($this->resource->getImagePath()),
        ];
    }
}
