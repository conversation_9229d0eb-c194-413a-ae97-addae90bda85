<?php

namespace App\Http\API\Dashboard\Profile\Controllers;

use App\Domain\Profile\Models\Account;
use App\Http\API\Dashboard\Profile\Requests\AccountRequest;
use App\Http\API\Dashboard\Profile\Resources\AccountResource;
use App\Http\Support\Controllers\Controller;

class AccountController extends Controller
{
    public function update(AccountRequest $request): AccountResource
    {
        $account = $this->getUser()->getAccount();

        $account->update([
            Account::PROPERTY_NAME => $request->getName(),
        ]);

        return AccountResource::make($account);
    }
}
