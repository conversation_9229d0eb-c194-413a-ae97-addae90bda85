<?php

namespace App\Http\API\Dashboard\Profile\Controllers;

use App\Domain\Profile\Actions\UpdateOrCreateUser;
use App\Domain\Profile\Models\User;
use App\Http\API\Dashboard\Profile\Requests\UserRequest;
use App\Http\API\Dashboard\Profile\Requests\UserStoreRequest;
use App\Http\API\Dashboard\Profile\Resources\UserResource;
use App\Http\Support\Controllers\Controller;
use App\Http\Support\Requests\IndexRequest;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class UserController extends Controller
{
    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        $users = User::query()->when(
            $request->getSearch(),
            fn(Builder $query) => $query->whereCustomLike(
                [User::PROPERTY_FIRSTNAME, User::PROPERTY_LASTNAME, User::PROPERTY_EMAIL],
                $request->getSearch(),
            ),
        );

        if ($request->getPage()) {
            $users = $users->paginate($request->getPageSize());
        } else {
            $users = $users->get();
        }

        return UserResource::collection($users);
    }

    public function store(UserStoreRequest $request): UserResource
    {
        $user = app(UpdateOrCreateUser::class)->execute($this->getUser()->getAccount(), $request->toDto());

        return UserResource::make($user);
    }

    public function show(string $id): UserResource
    {
        $user = User::query()->findOrFail($id);

        return UserResource::make($user);
    }

    public function update(UserRequest $request, string $id): UserResource
    {
        $user = User::query()->findOrFail($id);

        $dto = $request->toDto();
        $dto->setEmail($user->getEmail());

        $user = app(UpdateOrCreateUser::class)->execute($this->getUser()->getAccount(), $dto);

        return UserResource::make($user);
    }

    public function delete(string $id): JsonResponse
    {
        $user = User::query()->findOrFail($id);

        $user->delete();

        return response()->json();
    }
}
