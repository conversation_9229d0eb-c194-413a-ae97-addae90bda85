<?php

namespace App\Http\API\Dashboard\Google\Requests;

use App\Domain\Google\Actions\Authentication\GoogleOAuthRedirectLink;
use App\Domain\Profile\Models\Account;
use App\Support\Traits\HashChecker;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Lara<PERSON>\Socialite\Contracts\Provider;
use Lara<PERSON>\Socialite\Contracts\User;
use Laravel\Socialite\Facades\Socialite;

class GoogleAuthenticationCallbackRequest extends FormRequest
{
    use HashChe<PERSON>;

    public const REQUEST_AUTH_USER = 'authuser';
    public const REQUEST_STATE = GoogleOauthRedirectLink::PARAMETER_STATE;
    public const REQUEST_STATE_HASH = GoogleOauthRedirectLink::STATE_PARAMETER_HASH;
    public const REQUEST_STATE_TIMESTAMP = GoogleOauthRedirectLink::STATE_PARAMETER_TIMESTAMP;
    public const REQUEST_STATE_ACCOUNT_ID = GoogleOAuthRedirectLink::STATE_ACCOUNT_ID;
    public const REQUEST_SCOPE = 'scope';

    public function authorize(): bool
    {
        if ($this->invalidHash($this->getTimestamp(), $this->getHash())) {
            return false;
        }

        return true;
    }

    public function rules(): array
    {
        return [
            self::REQUEST_AUTH_USER => ['required', 'string'],
            self::REQUEST_STATE => ['required', 'json'],
            self::REQUEST_SCOPE => ['required', 'string'],
        ];
    }

    public function getSocialite(): Provider
    {
        $driver = Socialite::driver('google');

        if (method_exists($driver, 'stateless')) {
            $driver->stateless();
        }

        return $driver;
    }

    public function getGoogleUser(): ?User
    {
        try {
            return $this->getSocialite()->user();
        } catch (\Exception $exception) {
            return null;
        }
    }

    public function getState(): array
    {
        $state = request()->input(self::REQUEST_STATE, '[]');

        return json_decode($state, true);
    }

    public function getHash(): ?string
    {
        return Arr::get($this->getState(), self::REQUEST_STATE_HASH);
    }

    public function getTimestamp(): ?int
    {
        return Arr::get($this->getState(), self::REQUEST_STATE_TIMESTAMP);
    }

    public function getScopes(): array
    {
        $scopes = explode(' ', request()->input(self::REQUEST_SCOPE));

        return array_values(
            array_filter($scopes, function ($item) {
                return Str::startsWith($item, 'https://');
            }),
        );
    }

    public function getAccount(): Account
    {
        return Account::query()->findOrFail(Arr::get($this->getState(), self::REQUEST_STATE_ACCOUNT_ID));
    }
}
