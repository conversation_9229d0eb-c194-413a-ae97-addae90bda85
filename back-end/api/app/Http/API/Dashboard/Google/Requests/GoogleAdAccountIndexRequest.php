<?php

namespace App\Http\API\Dashboard\Google\Requests;

use App\Domain\Google\Support\Enums\Ads\GoogleScriptType;
use App\Http\Support\Requests\IndexRequest;
use Illuminate\Validation\Rule;

class GoogleAdAccountIndexRequest extends IndexRequest
{
    public const REQUEST_SCRIPT_TYPE = 'script_type';
    public const REQUEST_HAS_SCRIPT = 'has_script';

    public function rules(): array
    {
        return [
            self::REQUEST_SCRIPT_TYPE => ['sometimes', 'nullable', Rule::enum(GoogleScriptType::class)],
            self::REQUEST_HAS_SCRIPT => ['sometimes', 'bool'],
        ];
    }

    public function getScriptRelation(): ?string
    {
        return GooglescriptType::tryFrom($this->input(self::REQUEST_SCRIPT_TYPE))?->relation();
    }

    public function getHasScript(): bool
    {
        $hasScript = $this->input(self::REQUEST_HAS_SCRIPT);

        return $hasScript === null ? true : $hasScript;
    }
}
