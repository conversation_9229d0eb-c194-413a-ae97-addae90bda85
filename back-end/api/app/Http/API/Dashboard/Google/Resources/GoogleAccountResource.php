<?php

namespace App\Http\API\Dashboard\Google\Resources;

use App\Domain\Google\Models\GoogleAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoogleAccountResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_EMAIL = 'email';
    public const RESPONSE_IMAGE_URL = 'image_url';

    /** @var GoogleAccount $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_EMAIL => $this->resource->getEmail(),
            self::RESPONSE_IMAGE_URL => $this->resource->getImageUrl(),
        ];
    }
}
