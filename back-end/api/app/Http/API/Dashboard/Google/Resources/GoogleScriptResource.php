<?php

namespace App\Http\API\Dashboard\Google\Resources;

use App\Domain\Google\Models\GoogleScript;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoogleScriptResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_TYPE = 'type';
    public const RESPONSE_STATUS = 'status';
    public const RESPONSE_ERROR = 'error';
    public const RESPONSE_GOOGLE_AD_ACCOUNT = 'google_ad_account';
    public const RESPONSE_GOOGLE_FILE = 'google_file';

    /** @var GoogleScript $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_TYPE => $this->resource->getType(),
            self::RESPONSE_STATUS => $this->resource->getStatus(),
            self::RESPONSE_ERROR => $this->resource->getError(),
            self::RESPONSE_GOOGLE_AD_ACCOUNT => GoogleAdAccountResource::make(
                $this->whenLoaded(GoogleScript::RELATION_GOOGLE_AD_ACCOUNT),
            ),
            self::RESPONSE_GOOGLE_FILE => GoogleFileResource::make(
                $this->whenLoaded(GoogleScript::RELATION_GOOGLE_FILE),
            ),
        ];
    }
}
