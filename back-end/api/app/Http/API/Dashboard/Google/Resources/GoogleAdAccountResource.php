<?php

namespace App\Http\API\Dashboard\Google\Resources;

use App\Domain\Google\Models\GoogleAdAccount;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoogleAdAccountResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_EXTERNAL_ID = 'external_id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_QUALITY_SCORE_SCRIPT = 'quality_score_script';
    public const RESPONSE_AUCTION_INSIGHTS_SCRIPT = 'auction_insights_script';

    /** @var GoogleAdAccount $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_EXTERNAL_ID => $this->resource->getExternalId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_QUALITY_SCORE_SCRIPT => GoogleScriptResource::make(
                $this->whenLoaded(GoogleAdAccount::RELATION_QUALITY_SCORE_SCRIPT),
            ),
            self::RESPONSE_AUCTION_INSIGHTS_SCRIPT => GoogleScriptResource::make(
                $this->whenLoaded(GoogleAdAccount::RELATION_AUCTION_INSIGHTS_SCRIPT),
            ),
        ];
    }
}
