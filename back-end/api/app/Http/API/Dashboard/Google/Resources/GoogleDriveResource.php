<?php

namespace App\Http\API\Dashboard\Google\Resources;

use App\Domain\Google\Models\GoogleDrive;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoogleDriveResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_EXTERNAL_ID = 'external_id';
    public const RESPONSE_NAME = 'name';

    /** @var GoogleDrive $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_EXTERNAL_ID => $this->resource->getExternalId(),
            self::RESPONSE_NAME => $this->resource->getName(),
        ];
    }
}
