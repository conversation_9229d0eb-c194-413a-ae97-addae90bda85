<?php

namespace App\Http\API\Dashboard\Google\Resources;

use App\Domain\Google\Models\GoogleFile;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoogleFileResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_EXTERNAL_ID = 'external_id';
    public const RESPONSE_TYPE = 'type';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_URL = 'url';
    public const RESPONSE_EXTERNAL_CREATED_AT = 'external_created_at';
    public const RESPONSE_EXTERNAL_UPDATED_AT = 'external_updated_at';

    /** @var GoogleFile $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_EXTERNAL_ID => $this->resource->getExternalId(),
            self::RESPONSE_TYPE => $this->resource->getType(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_URL => $this->resource->getUrl(),
            self::RESPONSE_EXTERNAL_CREATED_AT => $this->resource->getExternalCreatedAt(),
            self::RESPONSE_EXTERNAL_UPDATED_AT => $this->resource->getExternalUpdatedAt(),
        ];
    }
}
