<?php

namespace App\Http\API\Dashboard\Google\Controllers;

use App\Domain\Google\Models\GoogleDrive;
use App\Http\API\Dashboard\Google\Resources\GoogleDriveResource;
use App\Http\Support\Controllers\Controller;
use App\Http\Support\Requests\IndexRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GoogleDriveController extends Controller
{
    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        $drives = GoogleDrive::query()
            ->when(
                $request->getSearch(),
                fn(Builder $query) => $query->whereCustomLike($request->getSearch(), [
                    GoogleDrive::PROPERTY_NAME,
                    GoogleDrive::PROPERTY_EXTERNAL_ID,
                ]),
            )
            ->orderBy(GoogleDrive::PROPERTY_NAME, 'ASC');

        if ($request->getPage()) {
            $drives = $drives->paginate($request->getPage());
        } else {
            $drives = $drives->get();
        }

        return GoogleDriveResource::collection($drives);
    }
}
