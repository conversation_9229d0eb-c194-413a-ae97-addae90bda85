<?php

namespace App\Http\API\Dashboard\Google\Controllers;

use App\Domain\Google\Models\GoogleAccount;
use App\Http\API\Dashboard\Google\Resources\GoogleAccountResource;
use App\Http\Support\Controllers\Controller;
use App\Http\Support\Requests\IndexRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GoogleAccountController extends Controller
{
    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        $accounts = GoogleAccount::query()->when(
            $request->getSearch(),
            fn(Builder $query) => $query->whereCustomLike(
                [GoogleAccount::PROPERTY_NAME, GoogleAccount::PROPERTY_EMAIL],
                $request->getSearch(),
            ),
        );

        if ($request->getPage()) {
            $accounts = $accounts->paginate($request->getPageSize());
        } else {
            $accounts = $accounts->get();
        }

        return GoogleAccountResource::collection($accounts);
    }

    public function delete(int $id): JsonResponse
    {
        $account = GoogleAccount::query()->findOrFail($id);

        $account->delete();

        return response()->json();
    }
}
