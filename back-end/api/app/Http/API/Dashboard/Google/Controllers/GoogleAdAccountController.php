<?php

namespace App\Http\API\Dashboard\Google\Controllers;

use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Google\Models\GoogleScript;
use App\Http\API\Dashboard\Google\Requests\GoogleAdAccountIndexRequest;
use App\Http\API\Dashboard\Google\Resources\GoogleAdAccountResource;
use App\Http\Support\Controllers\Controller;
use App\Http\Support\Requests\IndexRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GoogleAdAccountController extends Controller
{
    public function index(GoogleAdAccountIndexRequest $request): AnonymousResourceCollection
    {
        $accounts = GoogleAdAccount::query()
            ->when(
                $request->getSearch(),
                fn(Builder $query) => $query->whereCustomLike(
                    [GoogleAdAccount::PROPERTY_EXTERNAL_ID, GoogleAdAccount::PROPERTY_NAME],
                    $request->getSearch(),
                ),
            )
            ->when(
                $request->getScriptRelation(),
                fn(Builder $query) => $query
                    ->when(
                        $request->getHasScript(),
                        fn(Builder $query) => $query->whereHas($request->getScriptRelation()),
                    )
                    ->when(
                        !$request->getHasScript(),
                        fn(Builder $query) => $query->whereDoesntHave($request->getScriptRelation()),
                    )
                    ->with(implode('.', [$request->getScriptRelation(), GoogleScript::RELATION_GOOGLE_FILE])),
            )
            ->orderBy(GoogleAdAccount::PROPERTY_NAME, 'ASC');

        if ($request->getPage()) {
            $accounts = $accounts->paginate($request->getPageSize());
        } else {
            $accounts = $accounts->get();
        }

        return GoogleAdAccountResource::collection($accounts);
    }

    public function show(int $id): GoogleAdAccountResource
    {
        $account = GoogleAdAccount::query()->findOrFail($id);

        return GoogleAdAccountResource::make($account);
    }
}
