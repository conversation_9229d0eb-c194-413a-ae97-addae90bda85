<?php

namespace App\Http\API\Dashboard\Google\Controllers;

use App\Domain\Google\Actions\Authentication\BySocialiteUser;
use App\Domain\Google\Actions\Authentication\GoogleOAuthRedirectLink;
use App\Http\API\Dashboard\Google\Requests\GoogleAuthenticationCallbackRequest;
use App\Http\API\Dashboard\Google\Resources\GoogleAuthenticationOauthRedirectLinkResource;
use App\Http\Support\Controllers\Controller;
use Illuminate\Http\RedirectResponse;

class GoogleAuthenticationController extends Controller
{
    public function oauthRedirectLink(): GoogleAuthenticationOauthRedirectLinkResource
    {
        $link = app(GoogleOauthRedirectLink::class)->execute($this->getUser()->getAccount());

        return GoogleAuthenticationOauthRedirectLinkResource::make($link);
    }

    public function callback(GoogleAuthenticationCallbackRequest $request): RedirectResponse
    {
        app(BySocialiteUser::class)->execute($request->getAccount(), $request->getGoogleUser(), $request->getScopes());

        return response()->redirectTo(sprintf('%s/#/settings/google', config('app.url')));
    }
}
