<?php

namespace App\Http\API\Dashboard\Email\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmailAddLanguageRequest extends FormRequest
{
    public const REQUEST_LANGUAGE = 'language';

    public function rules(): array
    {
        return [
            self::REQUEST_LANGUAGE => ['required', 'string'],
        ];
    }

    public function getLanguage(): string
    {
        return $this->input(self::REQUEST_LANGUAGE);
    }
}
