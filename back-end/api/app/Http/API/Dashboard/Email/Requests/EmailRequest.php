<?php

namespace App\Http\API\Dashboard\Email\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmailRequest extends FormRequest
{
    public const REQUEST_HTML = 'html';
    public const REQUEST_CC_SENDER = 'cc_sender';
    public const REQUEST_CC = 'cc';
    public const REQUEST_SUBJECT = 'subject';

    public function rules(): array
    {
        return [
            self::REQUEST_HTML => ['required', 'string'],
            self::REQUEST_CC_SENDER => ['sometimes', 'nullable', 'bool'],
            self::REQUEST_CC => ['sometimes', 'nullable', 'array'],
            self::REQUEST_CC . '.*' => ['email'],
            self::REQUEST_SUBJECT => ['sometimes', 'nullable', 'string'],
        ];
    }

    public function getHtml(): string
    {
        return $this->input(self::REQUEST_HTML);
    }

    public function getCcSender(): bool
    {
        return !!$this->input(self::REQUEST_CC_SENDER);
    }

    public function getCc(): ?array
    {
        return $this->input(self::REQUEST_CC);
    }

    public function getSubject(): ?string
    {
        return $this->input(self::REQUEST_SUBJECT);
    }
}
