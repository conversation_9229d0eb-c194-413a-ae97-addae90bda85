<?php

namespace App\Http\API\Dashboard\Email\Requests;

use App\Http\Support\Requests\IndexRequest;

class EmailIndexRequest extends IndexRequest
{
    public const REQUEST_LANGUAGE = 'language';

    public function rules(): array
    {
        return array_merge(parent::rules(), [self::REQUEST_LANGUAGE => ['sometimes', 'string', 'nullable']]);
    }

    public function getLanguage(): ?string
    {
        return $this->input(self::REQUEST_LANGUAGE);
    }
}
