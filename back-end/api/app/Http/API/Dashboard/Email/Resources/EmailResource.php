<?php

namespace App\Http\API\Dashboard\Email\Resources;

use App\Domain\Email\Actions\GetVariables;
use App\Domain\Email\Models\Email;
use App\Http\API\Dashboard\Profile\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EmailResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_ACCOUNT_ID = 'account_id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_UPDATED_AT = 'updated_at';
    public const RESPONSE_UPDATED_BY = 'updated_by';
    public const RESPONSE_VARIABLES = 'variables';
    public const RESPONSE_LANGUAGE = 'language';
    public const RESPONSE_CC = 'cc';
    public const RESPONSE_CC_SENDER = 'cc_sender';
    public const RESPONSE_SUBJECT = 'subject';
    public const RESPONSE_EVENT = 'event';
    public const RESPONSE_HTML = 'html';

    /** @var Email $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_ACCOUNT_ID => $this->resource->getAccountId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_UPDATED_AT => $this->resource->getUpdatedAt(),
            self::RESPONSE_LANGUAGE => $this->resource->getLanguage(),
            self::RESPONSE_CC => $this->resource->getCc(),
            self::RESPONSE_CC_SENDER => $this->resource->getCcSender(),
            self::RESPONSE_SUBJECT => $this->resource->getSubject(),
            self::RESPONSE_EVENT => $this->resource->getEvent(),
            self::RESPONSE_HTML => $this->resource->getHtml(),
            self::RESPONSE_UPDATED_BY => UserResource::make($this->whenLoaded(Email::RELATION_UPDATED_BY)),
            self::RESPONSE_VARIABLES => app(GetVariables::class)->execute($this->resource),
        ];
    }
}
