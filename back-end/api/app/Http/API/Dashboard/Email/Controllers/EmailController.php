<?php

namespace App\Http\API\Dashboard\Email\Controllers;

use App\Domain\Email\Actions\SendPreview;
use App\Domain\Email\Models\Email;
use App\Http\API\Dashboard\Email\Requests\EmailAddLanguageRequest;
use App\Http\API\Dashboard\Email\Requests\EmailIndexRequest;
use App\Http\API\Dashboard\Email\Requests\EmailRequest;
use App\Http\API\Dashboard\Email\Resources\EmailResource;
use App\Http\Support\Controllers\Controller;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

class EmailController extends Controller
{
    public function index(EmailIndexRequest $request): AnonymousResourceCollection
    {
        $emails = Email::query()
            ->with(Email::RELATION_UPDATED_BY)
            ->where(Email::PROPERTY_WHITELABEL, true)

            ->when(
                $request->getSearch(),
                fn(Builder $query) => $query->whereCustomLike(Email::PROPERTY_NAME, $request->getSearch()),
            )
            ->when(
                $request->getLanguage(),
                fn(Builder $query) => $query->whereCustomLike(Email::PROPERTY_LANGUAGE, $request->getLanguage()),
            )
            ->whereIn(
                DB::raw(
                    sprintf('(%1$s.%2$s, %1$s.%3$s)', Email::TABLE_NAME, Email::PROPERTY_ID, Email::PROPERTY_EVENT),
                ),
                function ($query) {
                    $query
                        ->select(
                            DB::raw(
                                sprintf(
                                    'MAX(%1$s.%2$s), %1$s.%3$s',
                                    Email::TABLE_NAME,
                                    Email::PROPERTY_ID,
                                    Email::PROPERTY_EVENT,
                                ),
                            ),
                        )
                        ->from(Email::TABLE_NAME)
                        ->where(Email::PROPERTY_WHITELABEL, true)
                        ->where(
                            fn($subquery) => $subquery
                                ->where(Email::PROPERTY_ACCOUNT_ID, $this->getUser()->getAccountId())
                                ->orWhereNull(Email::PROPERTY_ACCOUNT_ID),
                        )
                        ->groupBy(
                            implode('.', [Email::TABLE_NAME, Email::PROPERTY_EVENT]),
                            implode('.', [Email::TABLE_NAME, Email::PROPERTY_LANGUAGE]),
                        );
                },
            )
            ->orderBy(Email::PROPERTY_NAME)
            ->orderBy(Email::PROPERTY_LANGUAGE);

        if ($request->getPage()) {
            $emails = $emails->paginate($request->getPageSize());
        } else {
            $emails = $emails->get();
        }

        return EmailResource::collection($emails);
    }

    public function show(string $emailId): EmailResource
    {
        /** @var Email $email */
        $email = Email::query()->findOrFail($emailId);

        return EmailResource::make($email);
    }

    public function update(EmailRequest $request, string $emailId): EmailResource
    {
        /** @var Email $email */
        $email = Email::query()->findOrFail($emailId);

        if (!$email->getAccountId()) {
            $data = array_merge($email->toArray(), [Email::PROPERTY_ACCOUNT_ID => $this->getUser()->getAccountId()]);

            $email = Email::query()->updateOrCreate(
                [
                    Email::PROPERTY_EVENT => $email->getEvent(),
                    Email::PROPERTY_ACCOUNT_ID => $this->getUser()->getAccountId(),
                    Email::PROPERTY_LANGUAGE => $email->getLanguage(),
                ],
                $data,
            );
        }

        $email
            ->setHtml($request->getHtml())
            ->setCcSender($request->getCcSender())
            ->setCc($request->getCc())
            ->setUpdatedBy($this->getUser()->getId())
            ->setSubject($request->getSubject() ?? $email->getSubject())
            ->save();

        return EmailResource::make($email);
    }

    public function addLanguage(EmailAddLanguageRequest $request, string $emailId): EmailResource
    {
        $email = Email::query()->findOrFail($emailId);

        $email = Email::query()->updateOrCreate(
            [
                Email::PROPERTY_EVENT => $email->getEvent(),
                Email::PROPERTY_ACCOUNT_ID => $this->getUser()->getAccountId(),
                Email::PROPERTY_LANGUAGE => $request->getLanguage(),
            ],
            array_merge($email->toArray(), [
                Email::PROPERTY_LANGUAGE => $request->getLanguage(),
                Email::PROPERTY_ACCOUNT_ID => $this->getUser()->getAccountId(),
            ]),
        );

        return EmailResource::make($email);
    }

    public function delete(string $emailId): JsonResponse
    {
        /** @var Email $email */
        $email = Email::query()->findOrFail($emailId);

        if ($email->getLanguage() === 'en') {
            abort(400);
        }

        $email->delete();

        return response()->json();
    }

    public function preview(string $id): JsonResponse
    {
        $email = Email::query()->findOrFail($id);

        $send = app(SendPreview::class)->execute($email, $this->getUser());

        if (!$send) {
            abort(400);
        }

        return response()->json();
    }
}
