<?php

namespace App\Http\API\Dashboard\Authentication\Controllers;

use App\Domain\Profile\Actions\RegisterAccount;
use App\Http\API\Dashboard\Authentication\Requests\AuthLoginRequest;
use App\Http\API\Dashboard\Authentication\Requests\AuthRegisterRequest;
use App\Http\API\Dashboard\Authentication\Resources\MeResource;
use App\Http\Support\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;

class AuthController extends Controller
{
    public function login(AuthLoginRequest $request): JsonResponse
    {
        $credentials = $request->validated();

        if (!Auth::attempt($credentials)) {
            abort(401);
        }

        if (!$this->getUser()->getVerifiedAt() || !$this->getUser()->getAccount()->getVerifiedAt()) {
            abort(403);
        }

        if ($request->hasSession()) {
            $request->session()->regenerate();
        }

        $this->getUser()->setLastLoginAt(now())->save();

        return response()->json();
    }

    public function register(AuthRegisterRequest $request): JsonResponse
    {
        app(RegisterAccount::class)->execute($request->toDto());

        return response()->json();
    }

    public function logout(Request $request): JsonResponse
    {
        if (!!Cookie::get('impersonating')) {
            Auth::guard('user')->logout();
        } elseif ($request->hasSession()) {
            $request->session()?->invalidate();
        }

        return response()->json();
    }

    public function me(): MeResource
    {
        $user = $this->getUser();

        return MeResource::make($user);
    }
}
