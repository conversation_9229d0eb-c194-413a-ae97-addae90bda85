<?php

namespace App\Http\API\Dashboard\Authentication\Requests;

use App\Domain\Profile\Dto\RegisterAccountDto;
use Illuminate\Foundation\Http\FormRequest;

class AuthRegisterRequest extends FormRequest
{
    public const REQUEST_NAME = 'name';
    public const REQUEST_FIRSTNAME = 'firstname';
    public const REQUEST_LASTNAME = 'lastname';
    public const REQUEST_EMAIL = 'email';
    public const REQUEST_PASSWORD = 'password';

    public function rules(): array
    {
        return [
            self::REQUEST_NAME => ['required', 'string'],
            self::REQUEST_FIRSTNAME => ['required', 'string'],
            self::REQUEST_LASTNAME => ['required', 'string'],
            self::REQUEST_EMAIL => ['required', 'email'],
            self::REQUEST_PASSWORD => ['required', 'string'],
        ];
    }

    public function toDto(): RegisterAccountDto
    {
        return (new RegisterAccountDto())
            ->setName($this->input(self::REQUEST_NAME))
            ->setFirstname($this->input(self::REQUEST_FIRSTNAME))
            ->setLastname($this->input(self::REQUEST_LASTNAME))
            ->setEmail($this->input(self::REQUEST_EMAIL))
            ->setPassword($this->input(self::REQUEST_PASSWORD));
    }
}
