<?php

namespace App\Http\API\Dashboard\Authentication\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AuthLoginRequest extends FormRequest
{
    public const REQUEST_EMAIL = 'email';
    public const REQUEST_PASSWORD = 'password';

    public function rules(): array
    {
        return [
            self::REQUEST_EMAIL => ['required', 'email'],
            self::REQUEST_PASSWORD => ['required', 'string'],
        ];
    }

    public function getEmail(): string
    {
        return $this->input(self::REQUEST_EMAIL);
    }

    public function getPassword(): string
    {
        return $this->input(self::REQUEST_PASSWORD);
    }
}
