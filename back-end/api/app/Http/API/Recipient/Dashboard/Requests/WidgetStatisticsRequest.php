<?php

namespace App\Http\API\Recipient\Dashboard\Requests;

use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Support\Enums\WidgetAccuracy;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;

class WidgetStatisticsRequest extends FormRequest
{
    public const REQUEST_START_DATE = 'start_date';
    public const REQUEST_END_DATE = 'end_date';
    public const REQUEST_ACCURACY = 'accuracy';

    public function rules(): array
    {
        return [
            self::REQUEST_START_DATE => ['sometimes', 'nullable', 'date', 'string'],
            self::REQUEST_END_DATE => ['sometimes', 'nullable', 'date', 'string'],
            self::REQUEST_ACCURACY => ['sometimes', 'nullable', Rule::enum(WidgetAccuracy::class)],
        ];
    }

    public function toStatisticsFilter(): WidgetStatisticsFilter
    {
        $filter = new WidgetStatisticsFilter();

        $startDate = $this->input(self::REQUEST_START_DATE);
        $endDate = $this->input(self::REQUEST_END_DATE);

        $filter->setStartDate($startDate ? Carbon::parse($startDate) : now()->subWeeks(2));
        $filter->setEndDate($endDate ? Carbon::parse($endDate) : now()->subDay());
        $filter->setAccuracy(WidgetAccuracy::tryFrom($this->input(self::REQUEST_ACCURACY)));

        return $filter;
    }
}
