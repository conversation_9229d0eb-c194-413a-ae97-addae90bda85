<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\PeriodAnalysisColumnDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PeriodAnalysisColumnResource extends JsonResource
{
    /** @var PeriodAnalysisColumnDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'value' => $this->resource->getValue(),
            'formatted_value' => $this->resource->getFormattedValue(),
            'prefix' => $this->resource->getPrefix(),
            'suffix' => $this->resource->getSuffix(),
            'styling' => $this->resource->getStyling(),
        ];
    }
}
