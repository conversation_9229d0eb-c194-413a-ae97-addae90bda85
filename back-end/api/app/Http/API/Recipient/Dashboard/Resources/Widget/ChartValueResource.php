<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\ChartValueDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChartValueResource extends JsonResource
{
    public const RESPONSE_X = 'x';
    public const RESPONSE_Y = 'y';
    public const RESPONSE_PREFIX = 'prefix';
    public const RESPONSE_SUFFIX = 'suffix';
    public const RESPONSE_VALUE_FORMATTED = 'value_formatted';

    /** @var ChartValueDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_X => $this->resource->getDate()->format('Y-m-d'),
            self::RESPONSE_Y => $this->resource->getValue(),
            self::RESPONSE_PREFIX => $this->resource->getPrefix(),
            self::RESPONSE_SUFFIX => $this->resource->getSuffix(),
            self::RESPONSE_VALUE_FORMATTED => $this->resource->getValueFormatted(),
        ];
    }
}
