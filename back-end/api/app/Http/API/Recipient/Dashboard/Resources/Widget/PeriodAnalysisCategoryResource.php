<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\PeriodAnalysisCategoryDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PeriodAnalysisCategoryResource extends JsonResource
{
    /** @var PeriodAnalysisCategoryDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'name' => $this->resource->getName(),
            'rows' => PeriodAnalysisRowResource::collection($this->resource->getRows()),
        ];
    }
}
