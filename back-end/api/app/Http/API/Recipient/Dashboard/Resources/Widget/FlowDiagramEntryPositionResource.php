<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\FlowDiagramEntryPositonDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FlowDiagramEntryPositionResource extends JsonResource
{
    public const RESPONSE_X = 'x';
    public const RESPONSE_Y = 'y';

    /** @var FlowDiagramEntryPositonDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_X => $this->resource->getX(),
            self::RESPONSE_Y => $this->resource->getY(),
        ];
    }
}
