<?php

namespace App\Http\API\Recipient\Dashboard\Resources;

use App\Domain\Dashboard\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PageResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_SECTIONS = 'sections';

    /** @var Page $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_SECTIONS => SectionResource::collection($this->whenLoaded(Page::RELATION_SECTIONS)),
        ];
    }
}
