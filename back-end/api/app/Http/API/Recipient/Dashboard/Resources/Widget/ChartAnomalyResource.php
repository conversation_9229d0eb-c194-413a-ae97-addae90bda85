<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\ChartAnomalyDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChartAnomalyResource extends JsonResource
{
    public const RESPONSE_VALUE = 'value';
    public const RESPONSE_DATE = 'date';
    public const RESPONSE_COLOR = 'color';
    public const RESPONSE_SERIES_INDEX = 'series_index';

    /** @var ChartAnomalyDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_VALUE => $this->resource->getValue(),
            self::RESPONSE_DATE => $this->resource->getDate()->format('Y-m-d'),
            self::RESPONSE_COLOR => $this->resource->getColor(),
            self::RESPONSE_SERIES_INDEX => $this->resource->getSeriesIndex(),
        ];
    }
}
