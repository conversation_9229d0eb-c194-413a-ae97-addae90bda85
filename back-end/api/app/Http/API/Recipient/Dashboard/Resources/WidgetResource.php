<?php

namespace App\Http\API\Recipient\Dashboard\Resources;

use App\Domain\Dashboard\Models\Widget;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_DATA_TYPE = 'data_type';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_COL_SPAN = 'col_span';
    public const RESPONSE_SETTINGS = 'settings';

    /** @var Widget $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_DATA_TYPE => $this->resource->getDataType(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_SETTINGS => $this->resource->getSettings(),
            self::RESPONSE_COL_SPAN => $this->resource->getColSpan(),
        ];
    }
}
