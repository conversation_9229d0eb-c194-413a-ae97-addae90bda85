<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\PieChartSeriesDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PieChartSeriesResource extends JsonResource
{
    public const RESPONSE_LABEL = 'label';
    public const RESPONSE_VALUE = 'value';
    public const RESPONSE_FORMATTED_VALUE = 'formatted_value';
    public const RESPONSE_COLOR = 'color';

    /** @var PieChartSeriesDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_LABEL => $this->resource->getLabel(),
            self::RESPONSE_VALUE => $this->resource->getValue(),
            self::RESPONSE_FORMATTED_VALUE => $this->resource->getFormattedValue(),
            self::RESPONSE_COLOR => $this->resource->getColor(),
        ];
    }
}
