<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\FlowDiagramGroupDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FlowDiagramGroupResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_X = 'x';
    public const RESPONSE_WIDTH = 'width';

    /** @var FlowDiagramGroupDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_X => $this->resource->getX(),
            self::RESPONSE_WIDTH => $this->resource->getWidth(),
        ];
    }
}
