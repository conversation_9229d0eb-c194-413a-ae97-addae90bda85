<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\FlowDiagramDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FlowDiagramResource extends JsonResource
{
    public const RESPONSE_GROUPS = 'groups';
    public const RESPONSE_CONNECTIONS = 'connections';
    public const RESPONSE_ENTRIES = 'entries';

    /** @var FlowDiagramDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_GROUPS => FlowDiagramGroupResource::collection($this->resource->getGroups()),
            self::RESPONSE_CONNECTIONS => FlowDiagramConnectionResource::collection($this->resource->getConnections()),
            self::RESPONSE_ENTRIES => FlowDiagramEntryResource::collection($this->resource->getEntries()),
        ];
    }
}
