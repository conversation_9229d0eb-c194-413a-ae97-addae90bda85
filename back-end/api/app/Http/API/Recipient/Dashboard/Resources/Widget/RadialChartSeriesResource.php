<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\RadialChartSeriesDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class Radial<PERSON>hartSeriesResource extends JsonResource
{
    public const RESPONSE_PERCENTAGE = 'percentage';
    public const RESPONSE_FORMATTED_VALUE = 'formatted_value';
    public const RESPONSE_TITLE = 'title';
    public const RESPONSE_PREFIX = 'prefix';
    public const RESPONSE_SUFFIX = 'suffix';

    /** @var RadialChartSeriesDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_PERCENTAGE => $this->resource->getPercentage(),
            self::RESPONSE_FORMATTED_VALUE => $this->resource->getFormattedValue(),
            self::RESPONSE_TITLE => $this->resource->getTitle(),
            self::RESPONSE_PREFIX => $this->resource->getPrefix(),
            self::RESPONSE_SUFFIX => $this->resource->getSuffix(),
        ];
    }
}
