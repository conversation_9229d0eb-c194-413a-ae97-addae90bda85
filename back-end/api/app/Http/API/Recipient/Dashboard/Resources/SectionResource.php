<?php

namespace App\Http\API\Recipient\Dashboard\Resources;

use App\Domain\Dashboard\Models\Section;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SectionResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_WIDGETS = 'widgets';
    public const RESPONSE_GRID_COLUMNS = 'grid_columns';

    /** @var Section $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->getId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_GRID_COLUMNS => $this->resource->getGridColumns(),
            self::RESPONSE_WIDGETS => WidgetResource::collection($this->whenLoaded(Section::RELATION_WIDGETS)),
        ];
    }
}
