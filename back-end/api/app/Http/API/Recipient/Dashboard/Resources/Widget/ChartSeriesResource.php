<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\ChartSeriesDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChartSeriesResource extends JsonResource
{
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_DATA = 'data';

    /** @var ChartSeriesDto $resource*/
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_NAME => $this->resource->getTitle(),
            self::RESPONSE_DATA => ChartValueResource::collection($this->resource->getValues()),
        ];
    }
}
