<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\SummaryFieldDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SummaryFieldResource extends JsonResource
{
    public const RESPONSE_TITLE = 'title';
    public const RESPONSE_VALUE = 'value';
    public const RESPONSE_VALUE_FORMATTED = 'value_formatted';
    public const RESPONSE_PREFIX = 'prefix';
    public const RESPONSE_SUFFIX = 'suffix';

    /** @var SummaryFieldDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_TITLE => $this->resource->getTitle(),
            self::RESPONSE_VALUE => $this->resource->getValue(),
            self::RESPONSE_VALUE_FORMATTED => $this->resource->getValueFormatted(),
            self::RESPONSE_PREFIX => $this->resource->getPrefix(),
            self::RESPONSE_SUFFIX => $this->resource->getSuffix(),
        ];
    }
}
