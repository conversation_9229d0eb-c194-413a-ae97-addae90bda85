<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\DataComparisonDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DataComparisonResource extends JsonResource
{
    public const RESPONSE_VALUE = 'value';
    public const RESPONSE_VALUE_FORMATTED = 'value_formatted';
    public const RESPONSE_COMPARISON = 'comparison';
    public const RESPONSE_COMPARISON_FORMATTED = 'comparison_formatted';

    /** @var DataComparisonDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_VALUE => $this->resource->getValue(),
            self::RESPONSE_VALUE_FORMATTED => $this->resource->getValueFormatted(),
            self::RESPONSE_COMPARISON => $this->resource->getComparison(),
            self::RESPONSE_COMPARISON_FORMATTED => $this->resource->getComparisonFormatted(),
        ];
    }
}
