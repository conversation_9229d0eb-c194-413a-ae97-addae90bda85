<?php

namespace App\Http\API\Recipient\Dashboard\Resources;

use App\Domain\Dashboard\Models\Dashboard;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DashboardResource extends JsonResource
{
    public const RESPONSE_SLUG = 'slug';
    public const RESPONSE_USER = 'user';
    public const RESPONSE_PAGES = 'pages';
    public const RESPONSE_RECIPIENT = 'recipient';

    /** @var Dashboard $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_SLUG => $this->resource->getSlug(),
            self::RESPONSE_PAGES => PageResource::collection($this->whenLoaded(Dashboard::RELATION_PAGES)),
            self::RESPONSE_RECIPIENT => RecipientResource::make($this->whenLoaded(Dashboard::RELATION_RECIPIENT)),
        ];
    }
}
