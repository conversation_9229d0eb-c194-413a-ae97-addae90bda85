<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\FlowDiagramEntryDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FlowDiagramEntryResource extends JsonResource
{
    public const RESPONSE_VALUE = 'value';
    public const RESPONSE_VALUE_FORMATTED = 'value_formatted';
    public const RESPONSE_INPUT_ID = 'input_id';
    public const RESPONSE_OUTPUT_ID = 'output_id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_POSITION = 'position';
    public const RESPONSE_GROUP_ID = 'group_id';
    public const RESPONSE_COMPARISON_VALUE = 'comparison_value';
    public const RESPONSE_COMPARISON_FORMATTED = 'comparison_formatted';
    public const RESPONSE_PREFIX = 'prefix';
    public const RESPONSE_SUFFIX = 'suffix';

    /** @var FlowDiagramEntryDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_VALUE => $this->resource->getValue(),
            self::RESPONSE_VALUE_FORMATTED => $this->resource->getValueFormatted(),
            self::RESPONSE_INPUT_ID => $this->resource->getInputId(),
            self::RESPONSE_OUTPUT_ID => $this->resource->getOutputId(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_POSITION => FlowDiagramEntryPositionResource::make($this->resource->getPosition()),
            self::RESPONSE_GROUP_ID => $this->resource->getGroupId(),
            self::RESPONSE_COMPARISON_VALUE => $this->resource->getComparisonValue(),
            self::RESPONSE_COMPARISON_FORMATTED => $this->resource->getComparisonFormatted(),
            self::RESPONSE_PREFIX => $this->resource->getPrefix(),
            self::RESPONSE_SUFFIX => $this->resource->getSuffix(),
        ];
    }
}
