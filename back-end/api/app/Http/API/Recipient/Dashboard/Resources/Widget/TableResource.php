<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\TableDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TableResource extends JsonResource
{
    public const RESPONSE_HEADERS = 'headers';
    public const RESPONSE_ROWS = 'rows';

    /** @var TableDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_HEADERS => $this->resource->getHeaders(),
            self::RESPONSE_ROWS => $this->resource->getRows(),
        ];
    }
}
