<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\RadialChartDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RadialChartResource extends JsonResource
{
    public const RESPONSE_MAX = 'max';
    public const RESPONSE_MIN = 'min';
    public const RESPONSE_PREFIX = 'prefix';
    public const RESPONSE_SUFFIX = 'suffix';
    public const RESPONSE_SERIES = 'series';

    /** @var RadialChartDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_MAX => $this->resource->getMax(),
            self::RESPONSE_MIN => $this->resource->getMin(),
            self::RESPONSE_PREFIX => $this->resource->getPrefix(),
            self::RESPONSE_SUFFIX => $this->resource->getSuffix(),
            self::RESPONSE_SERIES => RadialChartSeriesResource::collection($this->resource->getSeries()),
        ];
    }
}
