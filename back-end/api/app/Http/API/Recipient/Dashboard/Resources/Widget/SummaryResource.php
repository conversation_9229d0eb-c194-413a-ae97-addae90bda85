<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\SummaryDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SummaryResource extends JsonResource
{
    public const RESPONSE_TEXT = 'text';
    public const RESPONSE_FIELDS = 'fields';

    /** @var SummaryDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_TEXT => $this->resource->getText(),
            self::RESPONSE_FIELDS => SummaryFieldResource::collection($this->resource->getFields()),
        ];
    }
}
