<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\PeriodAnalysisDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PeriodAnalysisResource extends JsonResource
{
    /** @var PeriodAnalysisDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'headers' => $this->resource->getHeaders(),
            'categories' => PeriodAnalysisCategoryResource::collection($this->resource->getCategories()),
        ];
    }
}
