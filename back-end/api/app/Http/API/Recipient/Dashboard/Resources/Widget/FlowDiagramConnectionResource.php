<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\FlowDiagramConnectionDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FlowDiagramConnectionResource extends JsonResource
{
    public const RESPONSE_OUTPUT_ID = 'output_id';
    public const RESPONSE_INPUT_ID = 'input_id';

    /** @var FlowDiagramConnectionDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_OUTPUT_ID => $this->resource->getOutputId(),
            self::RESPONSE_INPUT_ID => $this->resource->getInputId(),
        ];
    }
}
