<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\PieChartDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PieChartResource extends JsonResource
{
    public const RESPONSE_SERIES = 'series';

    /** @var PieChartDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_SERIES => PieChartSeriesResource::collection($this->resource->getSeries()),
        ];
    }
}
