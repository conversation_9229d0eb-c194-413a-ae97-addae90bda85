<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\ChartDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChartResource extends JsonResource
{
    public const RESPONSE_ACCURACY = 'accuracy';
    public const RESPONSE_SERIES = 'series';
    public const RESPONSE_ANOMALIES = 'anomalies';
    public const RESPONSE_NAME = 'name';

    /** @var ChartDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ACCURACY => $this->resource->getAccuracy(),
            self::RESPONSE_NAME => $this->resource->getName(),
            self::RESPONSE_SERIES => ChartSeriesResource::collection($this->resource->getSeries()),
            self::RESPONSE_ANOMALIES => ChartAnomalyResource::collection($this->resource->getAnomalies()),
        ];
    }
}
