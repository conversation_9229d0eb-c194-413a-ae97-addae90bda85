<?php

namespace App\Http\API\Recipient\Dashboard\Resources\Widget;

use App\Domain\Dashboard\Dto\Widget\PeriodAnalysisRowDto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PeriodAnalysisRowResource extends JsonResource
{
    /** @var PeriodAnalysisRowDto $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            'columns' => PeriodAnalysisColumnResource::collection($this->resource->getColumns()),
        ];
    }
}
