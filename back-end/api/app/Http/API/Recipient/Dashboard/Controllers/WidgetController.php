<?php

namespace App\Http\API\Recipient\Dashboard\Controllers;

use App\Domain\Dashboard\Actions\GetStatisticsForWidget;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Dashboard\Support\Enums\WidgetDataType;
use App\Http\API\Recipient\Dashboard\Requests\WidgetStatisticsRequest;
use App\Http\API\Recipient\Dashboard\Resources\Widget\ChartResource;
use App\Http\API\Recipient\Dashboard\Resources\Widget\DataComparisonResource;
use App\Http\API\Recipient\Dashboard\Resources\Widget\FlowDiagramResource;
use App\Http\API\Recipient\Dashboard\Resources\Widget\PeriodAnalysisResource;
use App\Http\API\Recipient\Dashboard\Resources\Widget\PieChartResource;
use App\Http\API\Recipient\Dashboard\Resources\Widget\RadialChartResource;
use App\Http\API\Recipient\Dashboard\Resources\Widget\SummaryResource;
use App\Http\API\Recipient\Dashboard\Resources\Widget\TableResource;
use App\Http\Support\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetController extends Controller
{
    public function statistics(WidgetStatisticsRequest $request, int $widgetId): JsonResource|JsonResponse
    {
        $widget = Widget::query()->findOrFail($widgetId);

        $statistics = app(GetStatisticsForWidget::class)->execute($widget, $request->toStatisticsFilter());

        if (!$statistics) {
            return response()->json();
        }

        return match ($widget->getDataType()) {
            WidgetDataType::DATA_COMPARISON => DataComparisonResource::make($statistics),
            WidgetDataType::SUMMARY => SummaryResource::make($statistics),
            WidgetDataType::LINE_CHART, WidgetDataType::COLUMN_CHART => ChartResource::make($statistics),
            WidgetDataType::TABLE => TableResource::make($statistics),
            WidgetDataType::RADIAL_CHART => RadialChartResource::make($statistics),
            WidgetDataType::PERIOD_ANALYSIS => PeriodAnalysisResource::make($statistics),
            WidgetDataType::FLOW_DIAGRAM => FlowDiagramResource::make($statistics),
            WidgetDataType::PIE_CHART => PieChartResource::make($statistics),
        };
    }
}
