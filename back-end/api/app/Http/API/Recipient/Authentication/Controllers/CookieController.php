<?php

namespace App\Http\API\Recipient\Authentication\Controllers;

use App\Http\Support\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CookieController extends Controller
{
    public function show(Request $request): JsonResponse|Response
    {
        if ($this->getUser()) {
            abort(400);
        }

        if ($request->expectsJson()) {
            return new JsonResponse(status: 204);
        }

        return new Response(status: 204);
    }
}
