<?php

namespace App\Http\API\Recipient\Authentication\Controllers;

use App\Domain\Dashboard\Models\Dashboard;
use App\Domain\Dashboard\Models\Page;
use App\Domain\Dashboard\Models\Section;
use App\Http\API\Recipient\Dashboard\Resources\DashboardResource;
use App\Http\Support\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller
{
    public function login(Request $request, string $slug): DashboardResource
    {
        $dashboard = Dashboard::query()
            ->with([
                implode('.', [Dashboard::RELATION_PAGES, Page::RELATION_SECTIONS, Section::RELATION_WIDGETS]),
                Dashboard::RELATION_RECIPIENT,
                Dashboard::RELATION_ACCOUNT,
            ])
            ->where(Dashboard::PROPERTY_SLUG, $slug)
            ->first();

        if (!$request->hasValidSignature(false) || !$dashboard || !$dashboard->getAccount()->getActiveSubscription()) {
            abort(401);
        }

        Auth::guard('dashboard')->login($dashboard);

        if ($request->hasSession()) {
            $request->session()->regenerate();
        }

        return DashboardResource::make($dashboard);
    }
}
