<?php

namespace App\Http\API\Admin\Email\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmailRequest extends FormRequest
{
    public const REQUEST_NAME = 'name';
    public const REQUEST_WHITELABEL = 'whitelabel';
    public const REQUEST_HTML = 'html';
    public const REQUEST_SUBJECT = 'subject';

    public function rules(): array
    {
        return [
            self::REQUEST_NAME => ['present', 'nullable', 'string'],
            self::REQUEST_WHITELABEL => ['present', 'bool', 'nullable'],
            self::REQUEST_HTML => ['required', 'string'],
            self::REQUEST_SUBJECT => ['present', 'nullable', 'string'],
        ];
    }

    public function getName(): ?string
    {
        return $this->input(self::REQUEST_NAME);
    }

    public function getWhitelabel(): bool
    {
        return $this->input(self::REQUEST_WHITELABEL) ?? false;
    }

    public function getHtml(): string
    {
        return $this->input(self::REQUEST_HTML);
    }

    public function getSubject(): ?string
    {
        return $this->input(self::REQUEST_SUBJECT);
    }
}
