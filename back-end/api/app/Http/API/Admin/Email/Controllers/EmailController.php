<?php

namespace App\Http\API\Admin\Email\Controllers;

use App\Domain\Email\Models\Email;
use App\Http\API\Admin\Email\Requests\EmailRequest;
use App\Http\API\Admin\Email\Resources\EmailResource;
use App\Http\Support\Controllers\Controller;
use App\Http\Support\Requests\IndexRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class EmailController extends Controller
{
    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        $emails = Email::query()
            ->with(Email::RELATION_UPDATED_BY)
            ->whereNull(Email::PROPERTY_ACCOUNT_ID)
            ->when(
                $request->getSearch(),
                fn(Builder $query) => $query->whereCustomLike(
                    [Email::PROPERTY_NAME, Email::PROPERTY_EVENT],
                    $request->getSearch(),
                ),
            )
            ->orderBy(Email::PROPERTY_NAME);

        if ($request->getPage()) {
            $emails = $emails->paginate($request->getPageSize());
        } else {
            $emails = $emails->get();
        }

        return EmailResource::collection($emails);
    }

    public function show(string $emailId): EmailResource
    {
        $email = Email::query()->findOrFail($emailId);

        return EmailResource::make($email);
    }

    public function update(EmailRequest $request, string $emailId): EmailResource
    {
        /** @var Email $email */
        $email = Email::query()->findOrFail($emailId);

        $email
            ->setName($request->getName())
            ->setWhitelabel($request->getWhitelabel())
            ->setHtml($request->getHtml())
            //            ->setUpdatedBy($this->getAdmin()->getUserId())
            ->setSubject($request->getSubject())
            ->save();

        if (!$email->getWhitelabel()) {
            Email::query()
                ->where(Email::PROPERTY_EVENT, $email->getEvent())
                ->whereNotNull(Email::PROPERTY_ACCOUNT_ID)
                ->delete();
        }

        return EmailResource::make($email);
    }
}
