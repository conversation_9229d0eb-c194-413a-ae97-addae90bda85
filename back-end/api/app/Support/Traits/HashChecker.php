<?php

namespace App\Support\Traits;

use Illuminate\Support\Facades\Hash;

trait HashChecker
{
    /**
     * @return string
     */
    protected function getSalt(): string
    {
        return config('app.key');
    }

    /**
     * @param $value
     * @return string
     */
    protected function getSaltedValue($value): string
    {
        return $value . $this->getSalt();
    }

    /**
     * @param $value
     * @return string
     */
    protected function createHash($value): string
    {
        return Hash::make($this->getSaltedValue($value));
    }

    /**
     * @param $value
     * @param  string|null  $hash
     * @return bool
     */
    protected function validHash($value, ?string $hash): bool
    {
        return Hash::check($this->getSaltedValue($value), $hash);
    }

    /**
     * @param $value
     * @param  string|null  $hash
     * @return bool
     */
    protected function invalidHash($value, ?string $hash): bool
    {
        return !$this->validHash($value, $hash);
    }
}
