<?php

namespace App\Support;

use Illuminate\Support\Facades\Notification;

class SlackNotification extends Notification
{
    public static function send(mixed $notification, ?string $channel = null): void
    {
        if (!config('services.slack.notifications.bot_user_oauth_token')) {
            return;
        }

        self::route('slack', $channel ?? config('services.slack.notifications.channel'))->notify($notification);
    }
}
