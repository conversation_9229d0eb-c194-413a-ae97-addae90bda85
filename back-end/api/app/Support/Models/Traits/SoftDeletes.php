<?php

namespace App\Support\Models\Traits;

use Illuminate\Database\Eloquent\SoftDeletes as ParentSoftDeleteS;
use Illuminate\Support\Carbon;

trait SoftDeletes
{
    use ParentSoftDeletes;

    public const PROPERTY_DELETED_AT = 'deleted_at';

    public function getDeletedAt(): ?Carbon
    {
        return $this->{static::PROPERTY_DELETED_AT};
    }

    public function setDeletedAt(?Carbon $value): self
    {
        $this->{static::PROPERTY_DELETED_AT} = $value ? new Carbon($value) : null;
        return $this;
    }
}
