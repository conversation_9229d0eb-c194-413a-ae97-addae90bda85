<?php

namespace App\Support\Models\Traits;

use Illuminate\Support\Carbon;

trait TimestampableMethods
{
    public const PROPERTY_CREATED_AT = 'created_at';
    public const PROPERTY_UPDATED_AT = 'updated_at';

    public function getCreatedAt(): Carbon
    {
        return $this->{static::PROPERTY_CREATED_AT};
    }

    public function setCreatedAt($value): self
    {
        $this->{static::PROPERTY_CREATED_AT} = $value ? new Carbon($value) : null;
        return $this;
    }

    public function getUpdatedAt(): Carbon
    {
        return $this->{static::PROPERTY_UPDATED_AT};
    }

    public function setUpdatedAt($value): self
    {
        $this->{static::PROPERTY_UPDATED_AT} = $value ? new Carbon($value) : null;
        return $this;
    }
}
