<?php

namespace App\Support\Tests;

use App\Domain\Profile\Models\User;
use Tests\CreatesApplication;
use Faker;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Http\Resources\Json\JsonResource;
use <PERSON><PERSON>\Sanctum\Sanctum;
use Mockery;

class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected Mockery\MockInterface $eventsMock;

    protected Faker\Generator $faker;

    protected const DEFAULT_LOCALE = 'nl_NL';

    public function __construct(?string $name = null, array $data = [], $dataName = '')
    {
        parent::__construct($name, $data, $dataName);

        $this->faker = Faker\Factory::create(self::DEFAULT_LOCALE);
    }

    protected function setUp(): void
    {
        parent::setUp();
    }

    //    public function actingAsLink(Link $link): void
    //    {
    //        Sanctum::actingAs($link, guard: 'links');
    //    }

    public function actingAsUser(User $user): void
    {
        Sanctum::actingAs($user, guard: 'user');
    }

    //    public function actingAsAdmin(Admin $admin): void
    //    {
    //        Sanctum::actingAs($admin, guard: 'admins');
    //    }

    protected function tearDown(): void
    {
        parent::tearDown();

        JsonResource::$wrap = 'data';
    }
}
