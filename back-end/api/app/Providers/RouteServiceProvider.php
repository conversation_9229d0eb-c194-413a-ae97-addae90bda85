<?php

namespace App\Providers;

use Illuminate\Support\Facades\Route;

class RouteServiceProvider
{
    public static function routes(): void
    {
        RouteServiceProvider::webRoutes();
        RouteServiceProvider::dashboardRoutes();
        RouteServiceProvider::recipientRoutes();
        RouteServiceProvider::webhookRoutes();
    }

    private static function webRoutes(): void
    {
        Route::name('web.')->middleware('web')->group(base_path('routes/web.v1.guest.php'));
    }

    private static function dashboardRoutes(): void
    {
        Route::name('api.')
            ->prefix('api/v1')
            ->middleware(['api'])
            ->group(base_path('routes/api.v1.dashboard-guest.php'));

        Route::name('api.')
            ->prefix('api/v1')
            ->middleware(['api', 'auth:user'])
            ->group(base_path('routes/api.v1.dashboard-auth.php'));
    }

    private static function recipientRoutes(): void
    {
        Route::name('api.recipient.')
            ->prefix('api/v1/recipient')
            ->middleware(['api'])
            ->group(base_path('routes/api.v1.recipient-guest.php'));

        Route::name('api.recipient.')
            ->prefix('api/v1/recipient')
            ->middleware(['api', 'auth:dashboard'])
            ->group(base_path('routes/api.v1.recipient-auth.php'));

        Route::name('web.recipient.')
            ->prefix('recipient')
            ->middleware('web')
            ->group(base_path('routes/web.v1.recipient-guest.php'));
    }

    private static function webhookRoutes(): void
    {
        Route::name('api.webhooks.')
            ->prefix('api/v1/webhooks')
            ->middleware(['api'])
            ->group(base_path('routes/api.v1.webhooks.php'));
    }
}
