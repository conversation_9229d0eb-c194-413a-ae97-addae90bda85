<?php

namespace App\Providers;

use App\Domain\Dashboard\Models\Dashboard;
use App\Domain\Dashboard\Support\Observers\DashboardSlugObserver;
use App\Domain\Profile\Models\Account;
use App\Domain\Profile\Support\Observers\StripeAccountObserver;
use App\Domain\Stripe\Models\Subscription;
use App\Domain\Stripe\Models\SubscriptionItem;
use Carbon\Carbon;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Laravel\Cashier\Cashier;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->registerCashierConfig();
    }

    public function boot(): void
    {
        Builder::macro('whereCustomLike', function ($attributes, string $searchTerm) {
            $this->where(static function (Builder $query) use ($attributes, $searchTerm) {
                foreach (Arr::wrap($attributes) as $attribute) {
                    $query->when(
                        Str::contains($attribute, '.'),
                        static function (Builder $query) use ($attribute, $searchTerm) {
                            [$relationName, $relationAttribute] = explode('.', $attribute);

                            if (Str::contains($relationAttribute, 'combine-')) {
                                $relationAttribute = DB::raw(
                                    sprintf(
                                        'CONCAT(%s, " ", %s)',
                                        ...explode('-', Str::afterLast($relationAttribute, 'combine-')),
                                    ),
                                );
                            }

                            $query->orWhereHas($relationName, static function (Builder $query) use (
                                $relationAttribute,
                                $searchTerm,
                            ) {
                                $query->where($relationAttribute, 'LIKE', "%{$searchTerm}%");
                            });
                        },
                        static function (Builder $query) use ($attribute, $searchTerm) {
                            $query->orWhere($attribute, 'LIKE', "%{$searchTerm}%");
                        },
                    );
                }
            });

            return $this;
        });

        Dashboard::observe([DashboardSlugObserver::class]);

        if (app()->runningUnitTests()) {
            return;
        }

        Account::observe(StripeAccountObserver::class);
    }

    private function registerCashierConfig(): void
    {
        Cashier::useCustomerModel(Account::class);
        Cashier::useSubscriptionModel(Subscription::class);
        Cashier::useSubscriptionItemModel(SubscriptionItem::class);
    }
}
