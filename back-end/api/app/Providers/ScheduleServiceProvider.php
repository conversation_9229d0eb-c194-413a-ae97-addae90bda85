<?php

namespace App\Providers;

use App\Domain\Google\Jobs\DispatchSyncGoogleAccounts;
use App\Domain\Google\Jobs\Script\DispatchSyncScripts;
use Illuminate\Console\Scheduling\Schedule;

class ScheduleServiceProvider
{
    public static function boot(Schedule $schedule): void
    {
        $schedule->job(new DispatchSyncGoogleAccounts())->dailyAt('04:00');
        $schedule->job(new DispatchSyncScripts())->dailyAt('05:00');
    }
}
