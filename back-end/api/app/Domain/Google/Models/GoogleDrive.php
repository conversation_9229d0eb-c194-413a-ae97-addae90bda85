<?php

namespace App\Domain\Google\Models;

use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GoogleDrive extends Model
{
    use TimestampableMethods;

    public const string TABLE_NAME = 'google_drives';

    public const string PROPERTY_ID = 'id';
    public const string PROPERTY_GOOGLE_ACCOUNT_ID = 'google_account_id';
    public const string PROPERTY_EXTERNAL_ID = 'external_id';
    public const string PROPERTY_NAME = 'name';

    public const string RELATION_GOOGLE_ACCOUNT = 'googleAccount';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    public function googleAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAccount::class, self::PROPERTY_GOOGLE_ACCOUNT_ID);
    }

    public function getGoogleAccount(): GoogleAccount
    {
        return $this->{self::RELATION_GOOGLE_ACCOUNT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getGoogleAccountId(): int
    {
        return $this->{self::PROPERTY_GOOGLE_ACCOUNT_ID};
    }

    public function setGoogleAccountId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_ACCOUNT_ID} = $value;

        return $this;
    }

    public function getExternalId(): string
    {
        return $this->{self::PROPERTY_EXTERNAL_ID};
    }

    public function setExternalId(string $value): self
    {
        $this->{self::PROPERTY_EXTERNAL_ID} = $value;

        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;

        return $this;
    }
}
