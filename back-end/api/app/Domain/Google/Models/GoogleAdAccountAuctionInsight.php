<?php

namespace App\Domain\Google\Models;

use App\Domain\Dashboard\Support\Traits\HasDashboardSource;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class GoogleAdAccountAuctionInsight extends Model
{
    use TimestampableMethods;
    use HasDashboardSource;

    public const TABLE_NAME = 'google_ad_account_auction_insights';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_GOOGLE_AD_ACCOUNT_ID = 'google_ad_account_id';
    public const PROPERTY_DOMAIN = 'domain';
    public const PROPERTY_DATE = 'date';
    public const PROPERTY_IMPRESSION_SHARE = 'impression_share';
    public const PROPERTY_OVERLAP_RATE = 'overlap_rate';
    public const PROPERTY_POSITION_ABOVE_RATE = 'position_above_rate';
    public const PROPERTY_TOP_OF_PAGE_RATE = 'top_of_page_rate';
    public const PROPERTY_ABSOLUTE_TOP_OF_PAGE_RATE = 'absolute_top_of_page_rate';
    public const PROPERTY_OUTRANKING_SHARE = 'outranking_share';

    public const RELATION_GOOGLE_AD_ACCOUNT = 'googleAdAccount';

    public const DASHBOARD_SOURCE_LOCAL_KEY = self::PROPERTY_GOOGLE_AD_ACCOUNT_ID;
    public const DASHBOARD_SOURCE_TYPE = GoogleAdAccount::class;

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    protected $casts = [
        self::PROPERTY_DATE => 'date',
    ];

    public function googleAdAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAdAccount::class, self::PROPERTY_GOOGLE_AD_ACCOUNT_ID);
    }

    public function getGoogleAdAccount(): GoogleAdAccount
    {
        return $this->{self::RELATION_GOOGLE_AD_ACCOUNT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getGoogleAdAccountId(): ?int
    {
        return $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID};
    }

    public function setGoogleAdAccountId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID} = $value;
        return $this;
    }

    public function getImpressionShare(): ?float
    {
        return $this->{self::PROPERTY_IMPRESSION_SHARE};
    }

    public function setImpressionShare(?float $value): self
    {
        $this->{self::PROPERTY_IMPRESSION_SHARE} = $value;
        return $this;
    }

    public function getOverlapRate(): ?float
    {
        return $this->{self::PROPERTY_OVERLAP_RATE};
    }

    public function setOverlapRate(?float $value): self
    {
        $this->{self::PROPERTY_OVERLAP_RATE} = $value;
        return $this;
    }

    public function getPositionAboveRate(): ?float
    {
        return $this->{self::PROPERTY_POSITION_ABOVE_RATE};
    }

    public function setPositionAboveRate(?float $value): self
    {
        $this->{self::PROPERTY_POSITION_ABOVE_RATE} = $value;
        return $this;
    }

    public function getTopOfPageRate(): ?float
    {
        return $this->{self::PROPERTY_TOP_OF_PAGE_RATE};
    }

    public function setTopOfPageRate(?float $value): self
    {
        $this->{self::PROPERTY_TOP_OF_PAGE_RATE} = $value;
        return $this;
    }

    public function getAbsoluteTopOfPageRate(): ?float
    {
        return $this->{self::PROPERTY_ABSOLUTE_TOP_OF_PAGE_RATE};
    }

    public function setAbsoluteTopOfPageRate(?float $value): self
    {
        $this->{self::PROPERTY_ABSOLUTE_TOP_OF_PAGE_RATE} = $value;
        return $this;
    }

    public function getOutrankingShare(): ?float
    {
        return $this->{self::PROPERTY_OUTRANKING_SHARE};
    }

    public function setOutrankingShare(?float $value): self
    {
        $this->{self::PROPERTY_OUTRANKING_SHARE} = $value;
        return $this;
    }

    public function getDomain(): string
    {
        return $this->{self::PROPERTY_DOMAIN};
    }

    public function setDomain(string $value): self
    {
        $this->{self::PROPERTY_DOMAIN} = $value;

        return $this;
    }

    public function getDate(): ?Carbon
    {
        return $this->{self::PROPERTY_DATE};
    }

    public function setDate(?Carbon $value): self
    {
        $this->{self::PROPERTY_DATE} = $value;

        return $this;
    }
}
