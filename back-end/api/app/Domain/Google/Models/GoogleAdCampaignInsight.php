<?php

namespace App\Domain\Google\Models;

use App\Domain\Dashboard\Support\Traits\HasDashboardSourceThrough;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Tests\Domain\Google\Factories\GoogleAdCampaignInsightFactory;

class GoogleAdCampaignInsight extends Model
{
    use TimestampableMethods;
    use HasFactory;
    use HasDashboardSourceThrough;

    public const TABLE_NAME = 'google_ad_campaign_insights';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_GOOGLE_AD_CAMPAIGN_ID = 'google_ad_campaign_id';
    public const PROPERTY_DATE = 'date';
    public const PROPERTY_CLICKS = 'clicks';
    public const PROPERTY_VIDEO_VIEWS = 'video_views';
    public const PROPERTY_CTR = 'ctr';
    public const PROPERTY_IMPRESSIONS = 'impressions';
    public const PROPERTY_AVERAGE_CPC = 'average_cpc';
    public const PROPERTY_AVERAGE_CPM = 'average_cpm';
    public const PROPERTY_SPEND = 'spend';
    public const PROPERTY_CONVERSIONS = 'conversions';
    public const PROPERTY_CONVERSION_RATE = 'conversion_rate';
    public const PROPERTY_REVENUE = 'revenue';
    public const PROPERTY_ROAS = 'roas';
    public const PROPERTY_PROFIT = 'profit';
    public const PROPERTY_SEARCH_IMPRESSION_SHARE = 'search_impression_share';
    public const PROPERTY_SEARCH_TOP_IMPRESSION_SHARE = 'search_top_impression_share';
    public const PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE = 'search_absolute_top_impression_share';
    public const PROPERTY_CONTENT_IMPRESSION_SHARE = 'content_impression_share';
    public const PROPERTY_CONTENT_RANK_LOST_IMPRESSION_SHARE = 'content_rank_lost_impression_share';
    public const PROPERTY_CONTENT_BUDGET_LOST_IMPRESSION_SHARE = 'content_budget_lost_impression_share';

    public const RELATION_GOOGLE_AD_CAMPAIGN = 'googleAdCampaign';

    public const DASHBOARD_SOURCE_CLASS = GoogleAdCampaign::class;
    public const DASHBOOARD_SOURCE_LOCAL_KEY = self::PROPERTY_GOOGLE_AD_CAMPAIGN_ID;
    public const DASHBOARD_SOURCE_FOREIGN_KEY = GoogleAdCampaign::PROPERTY_GOOGLE_AD_ACCOUNT_ID;
    public const DASHBOARD_SOURCE_TYPE = GoogleAdAccount::class;

    public const FACTORY_CLASS = GoogleAdCampaignInsightFactory::class;

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    public function googleAdCampaign(): BelongsTo
    {
        return $this->belongsTo(GoogleAdCampaign::class, self::PROPERTY_GOOGLE_AD_CAMPAIGN_ID);
    }

    public function getGoogleAdCampaign(): GoogleAdCampaign
    {
        return $this->{self::RELATION_GOOGLE_AD_CAMPAIGN};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getGoogleAdCampaignId(): int
    {
        return $this->{self::PROPERTY_GOOGLE_AD_CAMPAIGN_ID};
    }

    public function setGoogleAdCampaignId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_AD_CAMPAIGN_ID} = $value;
        return $this;
    }

    public function getDate(): Carbon
    {
        return Carbon::parse($this->{self::PROPERTY_DATE});
    }

    public function setDate(Carbon|string $value): self
    {
        $this->{self::PROPERTY_DATE} = $value instanceof Carbon ? $value->toDateString() : $value;
        return $this;
    }

    public function getClicks(): int
    {
        return $this->{self::PROPERTY_CLICKS};
    }

    public function setClicks(int $value): self
    {
        $this->{self::PROPERTY_CLICKS} = $value;
        return $this;
    }

    public function getVideoViews(): int
    {
        return $this->{self::PROPERTY_VIDEO_VIEWS};
    }

    public function setVideoViews(int $value): self
    {
        $this->{self::PROPERTY_VIDEO_VIEWS} = $value;
        return $this;
    }

    public function getCtr(): int
    {
        return $this->{self::PROPERTY_CTR};
    }

    public function setCtr(int $value): self
    {
        $this->{self::PROPERTY_CTR} = $value;
        return $this;
    }

    public function getImpressions(): int
    {
        return $this->{self::PROPERTY_IMPRESSIONS};
    }

    public function setImpressions(int $value): self
    {
        $this->{self::PROPERTY_IMPRESSIONS} = $value;
        return $this;
    }

    public function getAverageCpc(): string
    {
        return $this->{self::PROPERTY_AVERAGE_CPC};
    }

    public function setAverageCpc(string $value): self
    {
        $this->{self::PROPERTY_AVERAGE_CPC} = $value;
        return $this;
    }

    public function getAverageCpm(): string
    {
        return $this->{self::PROPERTY_AVERAGE_CPM};
    }

    public function setAverageCpm(string $value): self
    {
        $this->{self::PROPERTY_AVERAGE_CPM} = $value;
        return $this;
    }

    public function getSpend(): string
    {
        return $this->{self::PROPERTY_SPEND};
    }

    public function setSpend(string $value): self
    {
        $this->{self::PROPERTY_SPEND} = $value;
        return $this;
    }

    public function getConversions(): float
    {
        return $this->{self::PROPERTY_CONVERSIONS};
    }

    public function setConversions(float $value): self
    {
        $this->{self::PROPERTY_CONVERSIONS} = $value;

        return $this;
    }

    public function getConversionRate(): float
    {
        return $this->{self::PROPERTY_CONVERSION_RATE};
    }

    public function setConversionRate(float $value): self
    {
        $this->{self::PROPERTY_CONVERSION_RATE} = $value;

        return $this;
    }

    public function getRevenue(): float
    {
        return $this->{self::PROPERTY_REVENUE};
    }

    public function setRevenue(float $value): self
    {
        $this->{self::PROPERTY_REVENUE} = $value;

        return $this;
    }

    public function getRoas(): float
    {
        return $this->{self::PROPERTY_ROAS};
    }

    public function setRoas(float $value): self
    {
        $this->{self::PROPERTY_ROAS} = $value;

        return $this;
    }

    public function getProfit(): float
    {
        return $this->{self::PROPERTY_PROFIT};
    }

    public function setProfit(float $value): self
    {
        $this->{self::PROPERTY_PROFIT} = $value;

        return $this;
    }

    public function getSearchImpressionShare(): float
    {
        return $this->{self::PROPERTY_SEARCH_IMPRESSION_SHARE};
    }

    public function setSearchImpressionShare(float $value): self
    {
        $this->{self::PROPERTY_SEARCH_IMPRESSION_SHARE} = $value;

        return $this;
    }

    public function getSearchTopImpressionShare(): float
    {
        return $this->{self::PROPERTY_SEARCH_TOP_IMPRESSION_SHARE};
    }

    public function setSearchTopImpressionShare(float $value): self
    {
        $this->{self::PROPERTY_SEARCH_TOP_IMPRESSION_SHARE} = $value;

        return $this;
    }

    public function getSearchAbsoluteTopImpressionShare(): float
    {
        return $this->{self::PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE};
    }

    public function setSearchAbsoluteTopImpressionShare(float $value): self
    {
        $this->{self::PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE} = $value;

        return $this;
    }

    public function getContentImpressionShare(): float
    {
        return $this->{self::PROPERTY_CONTENT_IMPRESSION_SHARE};
    }

    public function setContentImpressionShare(float $value): self
    {
        $this->{self::PROPERTY_CONTENT_IMPRESSION_SHARE} = $value;

        return $this;
    }

    public function getContentRankLostImpressionShare(): float
    {
        return $this->{self::PROPERTY_CONTENT_RANK_LOST_IMPRESSION_SHARE};
    }

    public function setContentRankLostImpressionShare(float $value): self
    {
        $this->{self::PROPERTY_CONTENT_RANK_LOST_IMPRESSION_SHARE} = $value;

        return $this;
    }

    public function getContentBudgetLostImpressionShare(): float
    {
        return $this->{self::PROPERTY_CONTENT_BUDGET_LOST_IMPRESSION_SHARE};
    }

    public function setContentBudgetLostImpressionShare(float $value): self
    {
        $this->{self::PROPERTY_CONTENT_BUDGET_LOST_IMPRESSION_SHARE} = $value;

        return $this;
    }
}
