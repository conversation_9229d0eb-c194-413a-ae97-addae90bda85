<?php

namespace App\Domain\Google\Models;

use App\Domain\Dashboard\Support\Traits\HasDashboardSource;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class GoogleAdAccountKeywordQualityScore extends Model
{
    use TimestampableMethods;
    use HasDashboardSource;

    public const TABLE_NAME = 'google_ad_account_keyword_quality_scores';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_GOOGLE_AD_ACCOUNT_ID = 'google_ad_account_id';
    public const PROPERTY_DATE = 'date';
    public const PROPERTY_KEYWORD = 'keyword';
    public const PROPERTY_SCORE = 'score';
    public const PROPERTY_IMPRESSIONS = 'impressions';
    public const PROPERTY_CLICKS = 'clicks';
    public const PROPERTY_COST = 'cost';
    public const PROPERTY_CONVERSIONS = 'conversions';

    public const RELATION_GOOGLE_AD_ACCOUNT = 'googleAdAccount';

    public const DASHBOARD_SOURCE_LOCAL_KEY = self::PROPERTY_GOOGLE_AD_ACCOUNT_ID;
    public const DASHBOARD_SOURCE_TYPE = GoogleAdAccount::class;

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_DATE => 'date',
        self::PROPERTY_SCORE => 'float',
    ];

    protected $guarded = [self::PROPERTY_ID];

    public function googleAdAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAdAccount::class, self::PROPERTY_GOOGLE_AD_ACCOUNT_ID);
    }

    public function getGoogleAdAccount(): GoogleAdAccount
    {
        return $this->{self::RELATION_GOOGLE_AD_ACCOUNT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getGoogleAdAccountId(): int
    {
        return $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID};
    }

    public function setGoogleAdAccountId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID} = $value;
        return $this;
    }

    public function getDate(): Carbon
    {
        return $this->{self::PROPERTY_DATE};
    }

    public function setDate(Carbon $value): self
    {
        $this->{self::PROPERTY_DATE} = $value;
        return $this;
    }

    public function getKeyword(): string
    {
        return $this->{self::PROPERTY_KEYWORD};
    }

    public function setKeyword(string $value): self
    {
        $this->{self::PROPERTY_KEYWORD} = $value;
        return $this;
    }

    public function getScore(): ?float
    {
        return $this->{self::PROPERTY_SCORE};
    }

    public function setScore(?float $value): self
    {
        $this->{self::PROPERTY_SCORE} = $value;
        return $this;
    }

    public function getImpressions(): int
    {
        return $this->{self::PROPERTY_IMPRESSIONS};
    }

    public function setImpressions(int $value): self
    {
        $this->{self::PROPERTY_IMPRESSIONS} = $value;
        return $this;
    }

    public function getClicks(): int
    {
        return $this->{self::PROPERTY_CLICKS};
    }

    public function setClicks(int $value): self
    {
        $this->{self::PROPERTY_CLICKS} = $value;

        return $this;
    }

    public function getCost(): float
    {
        return $this->{self::PROPERTY_COST};
    }

    public function setCost(float $value): self
    {
        $this->{self::PROPERTY_COST} = $value;

        return $this;
    }

    public function getConversions(): float
    {
        return $this->{self::PROPERTY_CONVERSIONS};
    }

    public function setConversions(float $value): self
    {
        $this->{self::PROPERTY_CONVERSIONS} = $value;

        return $this;
    }
}
