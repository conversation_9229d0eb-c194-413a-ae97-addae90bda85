<?php

namespace App\Domain\Google\Models;

use App\Domain\Dashboard\Support\Traits\HasDashboardSource;
use App\Domain\Google\Support\Enums\Ads\GoogleQualityScoreRating;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class GoogleAdAccountQualityScore extends Model
{
    use TimestampableMethods;
    use HasDashboardSource;

    public const TABLE_NAME = 'google_ad_account_quality_scores';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_GOOGLE_AD_ACCOUNT_ID = 'google_ad_account_id';
    public const PROPERTY_DATE = 'date';
    public const PROPERTY_FACTOR = 'factor';
    public const PROPERTY_METRIC = 'metric';
    public const PROPERTY_RATING = 'rating';
    public const PROPERTY_VALUE = 'value';

    public const RELATION_GOOGLE_AD_ACCOUNT = 'googleAdAccount';

    public const DASHBOARD_SOURCE_LOCAL_KEY = self::PROPERTY_GOOGLE_AD_ACCOUNT_ID;
    public const DASHBOARD_SOURCE_TYPE = GoogleAdAccount::class;

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    protected $casts = [
        self::PROPERTY_DATE => 'date',
        self::PROPERTY_RATING => GoogleQualityScoreRating::class,
    ];

    public function googleAdAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAdAccount::class, self::PROPERTY_GOOGLE_AD_ACCOUNT_ID);
    }

    public function getGoogleAdAccount(): GoogleAdAccount
    {
        return $this->{self::RELATION_GOOGLE_AD_ACCOUNT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getGoogleAdAccountId(): int
    {
        return $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID};
    }

    public function setGoogleAdAccountId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID} = $value;

        return $this;
    }

    public function getDate(): Carbon
    {
        return $this->{self::PROPERTY_DATE};
    }

    public function setDate(Carbon $value): self
    {
        $this->{self::PROPERTY_DATE} = $value;

        return $this;
    }

    public function getFactor(): string
    {
        return $this->{self::PROPERTY_FACTOR};
    }

    public function setFactor(string $value): self
    {
        $this->{self::PROPERTY_FACTOR} = $value;

        return $this;
    }

    public function getMetric(): string
    {
        return $this->{self::PROPERTY_METRIC};
    }

    public function setMetric(string $value): self
    {
        $this->{self::PROPERTY_METRIC} = $value;

        return $this;
    }

    public function getRating(): GoogleQualityScoreRating
    {
        return $this->{self::PROPERTY_RATING};
    }

    public function setRating(GoogleQualityScoreRating $value): self
    {
        $this->{self::PROPERTY_RATING} = $value;

        return $this;
    }

    public function getValue(): float
    {
        return $this->{self::PROPERTY_VALUE};
    }

    public function setValue(float $value): self
    {
        $this->{self::PROPERTY_VALUE} = $value;

        return $this;
    }
}
