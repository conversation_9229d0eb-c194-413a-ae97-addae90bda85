<?php

namespace App\Domain\Google\Models;

use App\Domain\Google\Support\Enums\Ads\AdvertisingChannelType;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Tests\Domain\Google\Factories\GoogleAdCampaignFactory;

class GoogleAdCampaign extends Model
{
    use TimestampableMethods;
    use HasFactory;

    public const TABLE_NAME = 'google_ad_campaigns';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_GOOGLE_AD_ACCOUNT_ID = 'google_ad_account_id';
    public const PROPERTY_EXTERNAL_ID = 'external_id';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_ADVERTISING_CHANNEL_TYPE = 'advertising_channel_type';

    public const RELATION_GOOGLE_AD_ACCOUNT = 'googleAdAccount';
    public const RELATION_GOOGLE_AD_CAMPAIGN_INSIGHTS = 'googleAdCampaignInsights';

    public const FACTORY_CLASS = GoogleAdCampaignFactory::class;

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    protected $casts = [
        self::PROPERTY_ADVERTISING_CHANNEL_TYPE => AdvertisingChannelType::class,
    ];

    public function googleAdAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAdAccount::class, self::PROPERTY_GOOGLE_AD_ACCOUNT_ID);
    }

    public function getGoogleAdAccount(): GoogleAdAccount
    {
        return $this->{self::RELATION_GOOGLE_AD_ACCOUNT};
    }

    public function googleAdCampaignInsights(): HasMany
    {
        return $this->hasMany(GoogleAdCampaignInsight::class, GoogleAdCampaignInsight::PROPERTY_GOOGLE_AD_CAMPAIGN_ID);
    }

    public function getGoogleAdCampaignInsights(): Collection
    {
        return $this->{self::RELATION_GOOGLE_AD_CAMPAIGN_INSIGHTS};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getGoogleAdAccountId(): int
    {
        return $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID};
    }

    public function setGoogleAdAccountId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID} = $value;
        return $this;
    }

    public function getExternalId(): int
    {
        return $this->{self::PROPERTY_EXTERNAL_ID};
    }

    public function setExternalId(int $value): self
    {
        $this->{self::PROPERTY_EXTERNAL_ID} = $value;
        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;
        return $this;
    }

    public function getAdvertisingChannelType(): AdvertisingChannelType
    {
        return $this->{self::PROPERTY_ADVERTISING_CHANNEL_TYPE};
    }

    public function setAdvertisingChannelType(AdvertisingChannelType $value): self
    {
        $this->{self::PROPERTY_ADVERTISING_CHANNEL_TYPE} = $value;
        return $this;
    }
}
