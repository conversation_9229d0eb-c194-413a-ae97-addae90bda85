<?php

namespace App\Domain\Google\Models;

use App\Domain\Google\Support\Enums\Ads\GoogleScriptError;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptStatus;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptType;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GoogleScript extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'google_scripts';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_GOOGLE_AD_ACCOUNT_ID = 'google_ad_account_id';
    public const PROPERTY_GOOGLE_FILE_ID = 'google_file_id';
    public const PROPERTY_GOOGLE_DRIVE_ID = 'google_drive_id';
    public const PROPERTY_TYPE = 'type';
    public const PROPERTY_STATUS = 'status';
    public const PROPERTY_ERROR = 'error';
    public const PROPERTY_ERROR_DETAILS = 'error_details';

    public const RELATION_GOOGLE_AD_ACCOUNT = 'googleAdAccount';
    public const RELATION_GOOGLE_FILE = 'googleFile';
    public const RELATION_GOOGLE_DRIVE = 'googleDrive';

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_TYPE => GoogleScriptType::class,
        self::PROPERTY_STATUS => GoogleScriptStatus::class,
        self::PROPERTY_ERROR => GoogleScriptError::class,
        self::PROPERTY_ERROR_DETAILS => 'array',
    ];

    protected $guarded = [self::PROPERTY_ID];

    public function googleAdAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAdAccount::class, self::PROPERTY_GOOGLE_AD_ACCOUNT_ID);
    }

    public function getGoogleAdAccount(): GoogleAdAccount
    {
        return $this->{self::RELATION_GOOGLE_AD_ACCOUNT};
    }

    public function googleFile(): BelongsTo
    {
        return $this->belongsTo(GoogleFile::class, self::PROPERTY_GOOGLE_FILE_ID);
    }

    public function getGoogleFile(): ?GoogleFile
    {
        return $this->{self::RELATION_GOOGLE_FILE};
    }

    public function googleDrive(): BelongsTo
    {
        return $this->belongsTo(GoogleDrive::class, self::PROPERTY_GOOGLE_DRIVE_ID);
    }

    public function getGoogleDrive(): ?GoogleDrive
    {
        return $this->{self::RELATION_GOOGLE_DRIVE};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getGoogleAdAccountId(): int
    {
        return $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID};
    }

    public function setGoogleAdAccountId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_AD_ACCOUNT_ID} = $value;

        return $this;
    }

    public function getGoogleFileId(): int
    {
        return $this->{self::PROPERTY_GOOGLE_FILE_ID};
    }

    public function setGoogleFileId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_FILE_ID} = $value;

        return $this;
    }

    public function getType(): GoogleScriptType
    {
        return $this->{self::PROPERTY_TYPE};
    }

    public function setType(GoogleScriptType $value): self
    {
        $this->{self::PROPERTY_TYPE} = $value;

        return $this;
    }

    public function getStatus(): ?GoogleScriptStatus
    {
        return $this->{self::PROPERTY_STATUS};
    }

    public function setStatus(?GoogleScriptStatus $value): self
    {
        $this->{self::PROPERTY_STATUS} = $value;

        return $this;
    }

    public function getError(): ?GoogleScriptError
    {
        return $this->{self::PROPERTY_ERROR};
    }

    public function setError(?GoogleScriptError $value): self
    {
        $this->{self::PROPERTY_ERROR} = $value;

        return $this;
    }

    public function getErrorDetails(): ?array
    {
        return $this->{self::PROPERTY_ERROR_DETAILS};
    }

    public function setErrorDetails(?array $value): self
    {
        $this->{self::PROPERTY_ERROR_DETAILS} = $value;

        return $this;
    }

    public function getGoogleDriveId(): ?int
    {
        return $this->{self::PROPERTY_GOOGLE_DRIVE_ID};
    }

    public function setGoogleDriveId(?int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_DRIVE_ID} = $value;

        return $this;
    }
}
