<?php

namespace App\Domain\Google\Models;

use App\Domain\Google\Support\Scopes\GoogleAccountGlobalAccessScope;
use App\Domain\Profile\Models\Account;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Tests\Domain\Google\Factories\GoogleAccountFactory;

class GoogleAccount extends Model
{
    use TimestampableMethods;
    use HasFactory;

    public const TABLE_NAME = 'google_accounts';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_ACCOUNT_ID = 'account_id';
    public const PROPERTY_EXTERNAL_ID = 'external_id';
    public const PROPERTY_EMAIL = 'email';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_STATUS = 'status';
    public const PROPERTY_IMAGE_URL = 'image_url';
    public const PROPERTY_REFRESH_TOKEN = 'refresh_token';
    public const PROPERTY_TOKEN = 'token';
    public const PROPERTY_SCOPES = 'scopes';
    public const PROPERTY_TOKEN_CREATED_AT = 'token_created_at';

    public const RELATION_ACCOUNT = 'account';
    public const RELATION_GOOGLE_AD_ACCOUNTS = 'googleAdAccounts';

    public const FACTORY_CLASS = GoogleAccountFactory::class;

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_SCOPES => 'array',
        self::PROPERTY_TOKEN_CREATED_AT => 'datetime',
    ];

    protected $guarded = [self::PROPERTY_ID];

    public static function booted(): void
    {
        static::addGlobalScope(new GoogleAccountGlobalAccessScope());
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class, self::PROPERTY_ACCOUNT_ID);
    }

    public function getAccount(): Account
    {
        return $this->{self::RELATION_ACCOUNT};
    }

    public function googleAdAccounts(): HasMany
    {
        return $this->hasMany(GoogleAdAccount::class, GoogleAdAccount::PROPERTY_GOOGLE_ACCOUNT_ID);
    }

    public function getGoogleAdAccounts(): Collection
    {
        return $this->{self::RELATION_GOOGLE_AD_ACCOUNTS};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getAccountId(): int
    {
        return $this->{self::PROPERTY_ACCOUNT_ID};
    }

    public function setAccountId(int $value): self
    {
        $this->{self::PROPERTY_ACCOUNT_ID} = $value;
        return $this;
    }

    public function getExternalId(): string
    {
        return $this->{self::PROPERTY_EXTERNAL_ID};
    }

    public function setExternalId(string $value): self
    {
        $this->{self::PROPERTY_EXTERNAL_ID} = $value;
        return $this;
    }

    public function getEmail(): string
    {
        return $this->{self::PROPERTY_EMAIL};
    }

    public function setEmail(string $value): self
    {
        $this->{self::PROPERTY_EMAIL} = $value;
        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->{self::PROPERTY_STATUS};
    }

    public function setStatus(?string $value): self
    {
        $this->{self::PROPERTY_STATUS} = $value;
        return $this;
    }

    public function getImageUrl(): ?string
    {
        return $this->{self::PROPERTY_IMAGE_URL};
    }

    public function setImageUrl(?string $value): self
    {
        $this->{self::PROPERTY_IMAGE_URL} = $value;
        return $this;
    }

    public function getRefreshToken(): ?string
    {
        return $this->{self::PROPERTY_REFRESH_TOKEN};
    }

    public function setRefreshToken(?string $value): self
    {
        $this->{self::PROPERTY_REFRESH_TOKEN} = $value;
        return $this;
    }

    public function getToken(): ?string
    {
        return $this->{self::PROPERTY_TOKEN};
    }

    public function setToken(?string $value): self
    {
        $this->{self::PROPERTY_TOKEN} = $value;
        return $this;
    }

    public function getScopes(): ?array
    {
        return $this->{self::PROPERTY_SCOPES};
    }

    public function setScopes(?array $value): self
    {
        $this->{self::PROPERTY_SCOPES} = $value;
        return $this;
    }

    public function getTokenCreatedAt(): ?Carbon
    {
        return $this->{self::PROPERTY_TOKEN_CREATED_AT};
    }

    public function setTokenCreatedAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_TOKEN_CREATED_AT} = $value;
        return $this;
    }
}
