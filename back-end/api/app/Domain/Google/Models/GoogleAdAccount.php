<?php

namespace App\Domain\Google\Models;

use App\Domain\Dashboard\Models\DashboardSource;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptType;
use App\Domain\Google\Support\Scopes\GoogleAdAccountGlobalAccessScope;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Tests\Domain\Google\Factories\GoogleAdAccountFactory;

class GoogleAdAccount extends Model
{
    use TimestampableMethods;
    use HasFactory;

    public const TABLE_NAME = 'google_ad_accounts';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_GOOGLE_ACCOUNT_ID = 'google_account_id';
    public const PROPERTY_CUSTOMER_ID = 'customer_id';
    public const PROPERTY_EXTERNAL_ID = 'external_id';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_STATUS = 'status';
    public const PROPERTY_TIME_ZONE = 'time_zone';
    public const PROPERTY_CURRENCY = 'currency';
    public const PROPERTY_LEVEL = 'level';
    public const PROPERTY_IS_HIDDEN = 'is_hidden';
    public const PROPERTY_IS_MANAGER = 'is_manager';
    public const PROPERTY_IS_TEST_ACCOUNT = 'is_test_account';
    public const PROPERTY_LAST_SYNCED_AT = 'last_synced_at';

    public const RELATION_GOOGLE_ACCOUNT = 'googleAccount';
    public const RELATION_GOOGLE_AD_CAMPAIGNS = 'googleAdCampaigns';
    public const RELATION_DASHBOARD_SOURCES = 'dashboardSources';
    public const RELATION_QUALITY_SCORE_SCRIPT = 'qualityScoreScript';
    public const RELATION_AUCTION_INSIGHTS_SCRIPT = 'auctionInsightsScript';

    public const FACTORY_CLASS = GoogleAdAccountFactory::class;

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_LAST_SYNCED_AT => 'datetime',
    ];

    protected $guarded = [self::PROPERTY_ID];

    public static function booted(): void
    {
        static::addGlobalScope(new GoogleAdAccountGlobalAccessScope());
    }

    public function googleAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAccount::class, self::PROPERTY_GOOGLE_ACCOUNT_ID);
    }

    public function getGoogleAccount(): GoogleAccount
    {
        return $this->{self::RELATION_GOOGLE_ACCOUNT};
    }

    public function googleAdCampaigns(): HasMany
    {
        return $this->hasMany(GoogleAdCampaign::class, GoogleAdCampaign::PROPERTY_GOOGLE_AD_ACCOUNT_ID);
    }

    public function getGoogleAdCampaigns(): Collection
    {
        return $this->{self::RELATION_GOOGLE_AD_CAMPAIGNS};
    }

    public function dashboardSources(): HasMany
    {
        return $this->hasMany(DashboardSource::class, self::PROPERTY_ID, DashboardSource::PROPERTY_SOURCE_ID)->where(
            DashboardSource::PROPERTY_SOURCE_TYPE,
            self::class,
        );
    }

    public function getDashboardSources(): Collection
    {
        return $this->{self::RELATION_DASHBOARD_SOURCES};
    }

    public function qualityScoreScript(): HasOne
    {
        return $this->hasOne(GoogleScript::class)->where(
            GoogleScript::PROPERTY_TYPE,
            GoogleScriptType::QUALITY_SCORE_CHECKER,
        );
    }

    public function getQualityScoreScript(): ?GoogleScript
    {
        return $this->{self::RELATION_QUALITY_SCORE_SCRIPT};
    }

    public function auctionInsightsScript(): HasOne
    {
        return $this->hasOne(GoogleScript::class)->where(
            GoogleScript::PROPERTY_TYPE,
            GoogleScriptType::AUCTION_INSIGHTS,
        );
    }

    public function getAuctionInsightsScript(): ?GoogleScript
    {
        return $this->{self::RELATION_AUCTION_INSIGHTS_SCRIPT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getGoogleAccountId(): int
    {
        return $this->{self::PROPERTY_GOOGLE_ACCOUNT_ID};
    }

    public function setGoogleAccountId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_ACCOUNT_ID} = $value;
        return $this;
    }

    public function getCustomerId(): ?int
    {
        return $this->{self::PROPERTY_CUSTOMER_ID};
    }

    public function setCustomerId(?int $value): self
    {
        $this->{self::PROPERTY_CUSTOMER_ID} = $value;
        return $this;
    }

    public function getExternalId(): int
    {
        return $this->{self::PROPERTY_EXTERNAL_ID};
    }

    public function setExternalId(int $value): self
    {
        $this->{self::PROPERTY_EXTERNAL_ID} = $value;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(?string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->{self::PROPERTY_STATUS};
    }

    public function setStatus(string $value): self
    {
        $this->{self::PROPERTY_STATUS} = $value;
        return $this;
    }

    public function getTimeZone(): string
    {
        return $this->{self::PROPERTY_TIME_ZONE};
    }

    public function setTimeZone(string $value): self
    {
        $this->{self::PROPERTY_TIME_ZONE} = $value;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->{self::PROPERTY_CURRENCY};
    }

    public function setCurrency(string $value): self
    {
        $this->{self::PROPERTY_CURRENCY} = $value;
        return $this;
    }

    public function getLevel(): int
    {
        return $this->{self::PROPERTY_LEVEL};
    }

    public function setLevel(int $value): self
    {
        $this->{self::PROPERTY_LEVEL} = $value;
        return $this;
    }

    public function getIsHidden(): bool
    {
        return $this->{self::PROPERTY_IS_HIDDEN};
    }

    public function setIsHidden(bool $value): self
    {
        $this->{self::PROPERTY_IS_HIDDEN} = $value;
        return $this;
    }

    public function getIsManager(): bool
    {
        return $this->{self::PROPERTY_IS_MANAGER};
    }

    public function setIsManager(bool $value): self
    {
        $this->{self::PROPERTY_IS_MANAGER} = $value;
        return $this;
    }

    public function getIsTestAccount(): bool
    {
        return $this->{self::PROPERTY_IS_TEST_ACCOUNT};
    }

    public function setIsTestAccount(bool $value): self
    {
        $this->{self::PROPERTY_IS_TEST_ACCOUNT} = $value;
        return $this;
    }

    public function getLastSyncedAt(): ?Carbon
    {
        return $this->{self::PROPERTY_LAST_SYNCED_AT};
    }

    public function setLastSyncedAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_LAST_SYNCED_AT} = $value;
        return $this;
    }
}
