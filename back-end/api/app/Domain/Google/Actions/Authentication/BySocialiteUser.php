<?php

namespace App\Domain\Google\Actions\Authentication;

use App\Domain\Google\Jobs\Ads\ListAccessibleAccountsJob;
use App\Domain\Google\Jobs\Drive\DispatchSyncGoogleFilesJob;
use App\Domain\Google\Jobs\Drive\SyncGoogleDrivesJob;
use App\Domain\Google\Jobs\SyncGoogleAccountJob;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Profile\Models\Account;
use Illuminate\Support\Facades\Bus;
use Laravel\Socialite\Contracts\User;

class BySocialiteUser
{
    public function execute(Account $account, User $user, array $scopes): GoogleAccount
    {
        $googleAccount = GoogleAccount::query()->updateOrCreate(
            [
                GoogleAccount::PROPERTY_EXTERNAL_ID => $user->getId(),
                GoogleAccount::PROPERTY_ACCOUNT_ID => $account->getId(),
            ],
            [
                GoogleAccount::PROPERTY_EMAIL => $user->getEmail(),
                GoogleAccount::PROPERTY_NAME => $user->getName(),
                GoogleAccount::PROPERTY_IMAGE_URL => $user->getAvatar(),
                GoogleAccount::PROPERTY_REFRESH_TOKEN => $user->refreshToken,
                GoogleAccount::PROPERTY_TOKEN => $user->token,
                GoogleAccount::PROPERTY_TOKEN_CREATED_AT => now(),
                GoogleAccount::PROPERTY_SCOPES => $scopes,
            ],
        );

        dispatch(new SyncGoogleAccountJob($googleAccount));

        return $googleAccount;
    }
}
