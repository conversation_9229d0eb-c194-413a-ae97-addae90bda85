<?php

namespace App\Domain\Google\Actions\Authentication;

use App\Domain\Google\Models\GoogleAccount;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class GoogleRefreshToken
{
    /**
     * @throws ConnectionException
     */
    public function execute(GoogleAccount $googleAccount): ?string
    {
        if (!$this->shouldRefreshToken($googleAccount)) {
            return $googleAccount->getToken();
        }

        $token = $this->refreshToken($googleAccount);

        $googleAccount->seTtoken($token);
        $googleAccount->setTokenCreatedAt($token ? now() : null);

        $googleAccount->save();

        return $token;
    }

    protected function shouldRefreshToken(GoogleAccount $googleAccount): bool
    {
        if (!$googleAccount->getTokenCreatedAt() || !$googleAccount->getToken()) {
            return true;
        }

        $expiryThreshold = now()->subMinutes(55);

        return $googleAccount->getTokenCreatedAt()->isBefore($expiryThreshold);
    }

    /**
     * @throws ConnectionException
     */
    protected function refreshToken(GoogleAccount $googleAccount): ?string
    {
        $response = Http::asForm()->connectTimeout(10)->post(
            url: 'https://oauth2.googleapis.com/token',
            data: [
                'client_id' => config('services.google.client_id'),
                'client_secret' => config('services.google.client_secret'),
                'grant_type' => 'refresh_token',
                'refresh_token' => $googleAccount->getRefreshToken(),
            ],
        );

        if (!$response->successful()) {
            return null;
        }

        return $response->json('access_token');
    }
}
