<?php

namespace App\Domain\Google\Actions\Script;

use App\Domain\Google\Models\GoogleScript;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptType;
use Illuminate\Support\Facades\Blade;

class RenderScript
{
    private GoogleScript $script;

    public function execute(GoogleScript $script): ?string
    {
        $this->script = $script;

        return Blade::render($this->getTemplate(), $this->getVariables());
    }

    private function getVariables(): array
    {
        return match ($this->script->getType()) {
            GoogleScriptType::QUALITY_SCORE_CHECKER => [
                'url' => $this->script->getGoogleFile()->getUrl(),
                'date' => now()->addDay()->format('Y-m-d'),
            ],
            default => [],
        };
    }

    private function getTemplate(): ?string
    {
        return match ($this->script->getType()) {
            GoogleScriptType::QUALITY_SCORE_CHECKER => 'scripts.quality-checker-script',
            default => null,
        };
    }
}
