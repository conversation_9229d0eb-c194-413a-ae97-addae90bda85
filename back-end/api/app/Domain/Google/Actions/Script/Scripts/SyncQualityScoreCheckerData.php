<?php

namespace App\Domain\Google\Actions\Script\Scripts;

use App\Domain\Google\Actions\Sheet\GetSheetData;
use App\Domain\Google\Dto\Sheet\SheetTabDto;
use App\Domain\Google\Models\GoogleAdAccountKeywordQualityScore;
use App\Domain\Google\Models\GoogleAdAccountQualityScore;
use App\Domain\Google\Models\GoogleScript;
use App\Domain\Google\Support\Enums\Ads\GoogleQualityScoreRating;
use App\Domain\Google\Support\Enums\Scripts\SyncQualityScoreCheckerException;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use NumberFormatter;

class SyncQualityScoreCheckerData
{
    private GoogleScript $script;

    public function execute(GoogleScript $script): void
    {
        $this->script = $script;

        $sheet = app(GetSheetData::class)->execute($script->getGoogleFile());

        /** @var SheetTabDto|null $dataTab */
        $dataTab = Arr::first(array_filter($sheet->getTabs(), fn(SheetTabDto $tab) => $tab->getName() === 'Data'));

        /** @var SheetTabDto|null $keywordTab */
        $keywordTab = Arr::first(
            array_filter($sheet->getTabs(), fn(SheetTabDto $tab) => $tab->getName() === 'Keyword Quality Score'),
        );

        if (!$dataTab || !$keywordTab) {
            throw SyncQualityScoreCheckerException::becauseOfMissingTab($script->getGoogleFile());
        }

        $this->processData(array_slice($dataTab->getRows(), 1));
        $this->processKeywords(array_slice($keywordTab->getRows(), 1));
    }

    private function processData(array $rows): void
    {
        $formatter = new NumberFormatter('nl_NL', NumberFormatter::DECIMAL);

        foreach ($rows as $row) {
            GoogleAdAccountQualityScore::query()->updateOrCreate(
                [
                    GoogleAdAccountQualityScore::PROPERTY_DATE => Carbon::parse($row[0]),
                    GoogleAdAccountQualityScore::PROPERTY_GOOGLE_AD_ACCOUNT_ID => $this->script->getGoogleAdAccountId(),
                    GoogleAdAccountQualityScore::PROPERTY_FACTOR => $row[1],
                    GoogleAdAccountQualityScore::PROPERTY_METRIC => $row[2],
                ],
                [
                    GoogleAdAccountQualityScore::PROPERTY_RATING => GoogleQualityScoreRating::fromResponse($row[3]),
                    GoogleAdAccountQualityScore::PROPERTY_VALUE => $formatter->parse($row[4]),
                ],
            );
        }
    }

    private function processKeywords(array $rows): void
    {
        $formatter = new NumberFormatter('nl_NL', NumberFormatter::DECIMAL);

        foreach ($rows as $row) {
            GoogleAdAccountKeywordQualityScore::query()->updateOrCreate(
                [
                    GoogleAdAccountKeywordQualityScore::PROPERTY_DATE => Carbon::parse($row[0]),
                    GoogleAdAccountKeywordQualityScore::PROPERTY_GOOGLE_AD_ACCOUNT_ID => $this->script->getGoogleAdAccountId(),
                    GoogleAdAccountKeywordQualityScore::PROPERTY_KEYWORD => $row[1],
                ],
                [
                    GoogleAdAccountKeywordQualityScore::PROPERTY_SCORE => Str::contains($row[2], '--') ? null : $row[2],
                    GoogleAdAccountKeywordQualityScore::PROPERTY_IMPRESSIONS => $row[3],
                    GoogleAdAccountKeywordQualityScore::PROPERTY_CLICKS => $row[4],
                    GoogleAdAccountKeywordQualityScore::PROPERTY_COST => $formatter->parse($row[5]),
                    GoogleAdAccountKeywordQualityScore::PROPERTY_CONVERSIONS => $formatter->parse($row[6]),
                ],
            );
        }
    }
}
