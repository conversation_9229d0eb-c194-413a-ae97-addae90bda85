<?php

namespace App\Domain\Google\Actions\Script\Scripts;

use App\Domain\Google\Actions\Sheet\GetSheetData;
use App\Domain\Google\Dto\Sheet\SheetTabDto;
use App\Domain\Google\Models\GoogleAdAccountAuctionInsight;
use App\Domain\Google\Models\GoogleFile;
use App\Domain\Google\Models\GoogleScript;
use App\Domain\Google\Support\Enums\Drive\GoogleFileType;
use App\Domain\Google\Support\Enums\Scripts\SyncAuctionInsightsException;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class SyncAuctionInsightsData
{
    private GoogleScript $script;

    public function execute(GoogleScript $script): void
    {
        $this->script = $script;

        $file = $this->getMostRecentGoogleSheet();

        if (!$file) {
            throw SyncAuctionInsightsException::becauseOfMissingFile($script);
        }

        $sheet = app(GetSheetData::class)->execute($file);

        /** @var SheetTabDto|null $tab */
        $tab = Arr::first(array_filter($sheet->getTabs(), fn(SheetTabDto $tab) => $tab->getIndex() === 0));

        if (!$tab) {
            throw SyncAuctionInsightsException::becauseOfMissingFile($file);
        }

        foreach (array_slice($tab->getRows(), 1) as $row) {
            GoogleAdAccountAuctionInsight::query()->updateOrCreate(
                [
                    GoogleAdAccountAuctionInsight::PROPERTY_GOOGLE_AD_ACCOUNT_ID => $script->getGoogleAdAccountId(),
                    GoogleAdAccountAuctionInsight::PROPERTY_DATE => Carbon::parse($row[0]),
                    GoogleAdAccountAuctionInsight::PROPERTY_DOMAIN => Str::contains($row[1], '.') ? $row[1] : 'You',
                ],
                [
                    GoogleAdAccountAuctionInsight::PROPERTY_IMPRESSION_SHARE => $this->processStatistic($row[2]),
                    GoogleAdAccountAuctionInsight::PROPERTY_OVERLAP_RATE => $this->processStatistic($row[3]),
                    GoogleAdAccountAuctionInsight::PROPERTY_POSITION_ABOVE_RATE => $this->processStatistic($row[4]),
                    GoogleAdAccountAuctionInsight::PROPERTY_TOP_OF_PAGE_RATE => $this->processStatistic($row[5]),
                    GoogleAdAccountAuctionInsight::PROPERTY_ABSOLUTE_TOP_OF_PAGE_RATE => $this->processStatistic(
                        $row[6],
                    ),
                    GoogleAdAccountAuctionInsight::PROPERTY_OUTRANKING_SHARE => $this->processStatistic($row[7]),
                ],
            );
        }
    }

    private function getMostRecentGoogleSheet(): ?GoogleFile
    {
        return GoogleFile::query()
            ->where(GoogleFile::PROPERTY_GOOGLE_DRIVE_ID, $this->script->getGoogleDriveId())
            ->where(GoogleFile::PROPERTY_TYPE, GoogleFileType::SHEETS)
            ->orderBy(GoogleFile::PROPERTY_EXTERNAL_CREATED_AT, 'DESC')
            ->first();
    }

    private function processStatistic(string $statistic): ?float
    {
        if (Str::contains($statistic, '--')) {
            return null;
        }

        if (Str::contains($statistic, '<')) {
            return 0;
        }

        return (float) Str::before($statistic, '%');
    }
}
