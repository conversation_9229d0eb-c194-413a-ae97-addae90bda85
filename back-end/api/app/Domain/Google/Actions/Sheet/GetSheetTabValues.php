<?php

namespace App\Domain\Google\Actions\Sheet;

use App\Domain\Google\Dto\Sheet\SheetTabDto;
use App\Domain\Google\Models\GoogleFile;
use App\Domain\Google\Support\Exceptions\GoogleRequestException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;
use Illuminate\Support\Arr;

class GetSheetTabValues extends GoogleRequest
{
    public const ENDPOINT = 'https://sheets.googleapis.com/v4/spreadsheets/%s/values/%s!A:Z';

    public const RESPONSE_VALUES = 'values';

    private GoogleFile $file;
    private SheetTabDto $tab;

    public function execute(GoogleFile $file, SheetTabDto $tab): SheetTabDto
    {
        $this->file = $file;
        $this->tab = $tab;

        $response = $this->getResponse(RequestType::GET, $file->getGoogleAccount());

        if (!$response->isSuccessful()) {
            throw GoogleRequestException::becauseOfHttpErrorWithStatusCode(
                $file->getGoogleAccount(),
                $response->getStatus(),
                $response->getBody(),
            );
        }

        $tab->setRows(Arr::get($response->getBody(), self::RESPONSE_VALUES, []));

        return $tab;
    }

    protected function getUrl(): string
    {
        return sprintf(self::ENDPOINT, $this->file->getExternalId(), $this->tab->getName());
    }
}
