<?php

namespace App\Domain\Google\Actions\Sheet;

use App\Domain\Google\Dto\Sheet\SheetDto;
use App\Domain\Google\Models\GoogleFile;

class GetSheetData
{
    public function execute(GoogleFile $file): SheetDto
    {
        $dto = app(GetSheetTabs::class)->execute($file);

        foreach ($dto->getTabs() as $tab) {
            $tab = app(GetSheetTabValues::class)->execute($file, $tab);
        }

        return $dto;
    }
}
