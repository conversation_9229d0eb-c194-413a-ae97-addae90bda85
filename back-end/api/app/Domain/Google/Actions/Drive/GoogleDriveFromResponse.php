<?php

namespace App\Domain\Google\Actions\Drive;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleDrive;
use Illuminate\Support\Arr;

class GoogleDriveFromResponse
{
    public const string RESPONSE_ID = 'id';
    public const string RESPONSE_NAME = 'name';

    public function execute(GoogleAccount $account, array $data): GoogleDrive
    {
        return GoogleDrive::query()->updateOrCreate(
            [
                GoogleDrive::PROPERTY_EXTERNAL_ID => Arr::get($data, self::RESPONSE_ID),
                GoogleDrive::PROPERTY_GOOGLE_ACCOUNT_ID => $account->getId(),
            ],
            [
                GoogleDrive::PROPERTY_NAME => Arr::get($data, self::RESPONSE_NAME),
            ],
        );
    }
}
