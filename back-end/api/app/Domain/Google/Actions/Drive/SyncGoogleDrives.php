<?php

namespace App\Domain\Google\Actions\Drive;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleDrive;
use App\Domain\Google\Support\Exceptions\GoogleRequestException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;
use Illuminate\Support\Arr;

class SyncGoogleDrives extends GoogleRequest
{
    public const string ENDPOINT = 'https://www.googleapis.com/drive/v3/drives';

    public const string RESPONSE_ID = 'id';
    public const string RESPONSE_NAME = 'name';
    public const string RESPONSE_DRIVES = 'drives';
    public const string RESPONSE_NEXT_PAGE_TOKEN = 'nextPageToken';

    private ?string $pageToken = null;
    private GoogleAccount $account;

    public function execute(GoogleAccount $account, ?string $pageToken = null): ?string
    {
        $this->pageToken = $pageToken;
        $this->account = $account;

        $response = $this->getResponse(RequestType::GET, $account);

        if (!$response->isSuccessful()) {
            GoogleRequestException::becauseOfHttpErrorWithStatusCode(
                $account,
                $response->getStatus(),
                $response->getBody(),
            );
        }

        $this->processDrives(Arr::get($response->getBody(), self::RESPONSE_DRIVES, []));

        return Arr::get($response->getBody(), self::RESPONSE_NEXT_PAGE_TOKEN);
    }

    protected function getUrl(): string
    {
        return self::ENDPOINT;
    }

    protected function getQueryParameters(): ?array
    {
        return [
            'pageToken' => $this->pageToken,
        ];
    }

    private function processDrives(array $drives): void
    {
        foreach ($drives as $drive) {
            app(GoogleDriveFromResponse::class)->execute($this->account, $drive);
        }
    }
}
