<?php

namespace App\Domain\Google\Actions\Drive;

use App\Domain\Google\Dto\Drive\CreateDriveDto;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleDrive;
use App\Domain\Google\Support\Exceptions\GoogleRequestException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;
use Illuminate\Support\Str;

class CreateDrive extends GoogleRequest
{
    public const ENDPOINT = 'https://www.googleapis.com/drive/v3/drives';

    private CreateDriveDto $dto;

    public function execute(GoogleAccount $googleAccount, CreateDriveDto $dto): GoogleDrive
    {
        $this->dto = $dto;

        $response = $this->getResponse(RequestType::POST, $googleAccount);

        if (!$response->isSuccessful()) {
            throw GoogleRequestException::becauseOfHttpErrorWithStatusCode(
                $googleAccount,
                $response->getStatus(),
                $response->getBody(),
            );
        }

        return app(GoogleDriveFromResponse::class)->execute($googleAccount, $response->getBody());
    }

    protected function getUrl(): string
    {
        return self::ENDPOINT;
    }

    protected function getBody(): ?array
    {
        return [
            'name' => $this->dto->getName(),
        ];
    }

    protected function getQueryParameters(): ?array
    {
        return [
            'requestId' => Str::random(),
        ];
    }
}
