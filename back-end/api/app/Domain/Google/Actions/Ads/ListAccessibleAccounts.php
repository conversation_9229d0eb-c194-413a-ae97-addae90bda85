<?php

namespace App\Domain\Google\Actions\Ads;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Support\Exceptions\ListAccessibleAccountsException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;
use Illuminate\Support\Arr;

class ListAccessibleAccounts extends GoogleRequest
{
    public const ENDPOINT = 'https://googleads.googleapis.com/v19/customers:listAccessibleCustomers';

    public function execute(GoogleAccount $googleAccount): array
    {
        $response = $this->getResponse(RequestType::GET, $googleAccount);

        if (!$response->isSuccessful()) {
            throw ListAccessibleAccountsException::becauseOfHttpErrorWithStatusCode(
                $googleAccount,
                $response->getStatus(),
                $response->getBody(),
            );
        }

        return Arr::get($response->getBody(), 'resourceNames');
    }

    protected function getUrl(): string
    {
        return self::ENDPOINT;
    }
}
