<?php

namespace App\Domain\Google\Actions\Ads;

use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Google\Models\GoogleAdCampaign;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Google\Support\Exceptions\SyncCampaignsWithMetricsException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;
use Carbon\CarbonInterface;
use Illuminate\Support\Arr;

class SyncCampaignsWithMetrics extends GoogleRequest
{
    public const ENDPOINT = 'https://googleads.googleapis.com/v19/customers/%s/googleAds:searchStream';

    public const RESPONSE_RESULTS = 'results';

    public const RESPONSE_CAMPAIGN = 'campaign';
    public const RESPONSE_RESOURCE_NAME = 'resourceName';
    public const RESPONSE_ADVERTISING_CHANNEL_TYPE = 'advertisingChannelType';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_ID = 'id';

    public const RESPONSE_METRICS = 'metrics';
    public const RESPONSE_CLICKS = 'clicks';
    public const RESPONSE_VIDEO_VIEWS = 'videoViews';
    public const RESPONSE_COST_MICROS = 'costMicros';
    public const RESPONSE_CTR = 'ctr';
    public const RESPONSE_AVERAGE_CPC = 'averageCpc';
    public const RESPONSE_AVERAGE_CPM = 'averageCpm';
    public const RESPONSE_IMPRESSIONS = 'impressions';
    public const RESPONSE_ALL_CONVERSIONS = 'allConversions';
    public const RESPONSE_REVENUE_MICROS = 'revenueMicros';
    public const RESPONSE_GROSS_PROFIT_MICROS = 'grossProfitMicros';
    public const RESPONSE_AVERAGE_TARGET_ROAS = 'averageTargetRoas';
    public const RESPONSE_ALL_CONVERSIONS_FROM_INTERACTIONS_RATE = 'allConversionsFormInteractionsRate';
    public const RESPONSE_CONTENT_BUDGET_LOST_IMPRESSION_SHARE = 'contentBudgetLostImpressionShare';
    public const RESPONSE_CONTENT_IMPRESSION_SHARE = 'contentImpressionShare';
    public const RESPONSE_CONTENT_RANK_LOST_IMPRESSION_SHARE = 'contentRankLostImpressionShare';
    public const RESPONSE_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE = 'searchAbsoluteTopImpressionShare';
    public const RESPONSE_SEARCH_IMPRESSION_SHARE = 'searchImpressionShare';
    public const RESPONSE_SEARCH_TOP_IMPRESSION_SHARE = 'searchTopImpressionShare';

    public const RESPONSE_SEGMENTS = 'segments';
    public const RESPONSE_DATE = 'date';

    private GoogleAdAccount $googleAdAccount;
    private ?CarbonInterface $startDate;
    private ?CarbonInterface $endDate;

    public function execute(
        GoogleAdAccount $googleAdAccount,
        ?CarbonInterface $startDate = null,
        ?CarbonInterface $endDate = null,
    ): void {
        $this->googleAdAccount = $googleAdAccount;
        $this->startDate = $startDate;
        $this->endDate = $endDate;

        $response = $this->getResponse(RequestType::POST, $googleAdAccount->getGoogleAccount());

        if (!$response->isSuccessful()) {
            throw SyncCampaignsWithMetricsException::becauseOfHttpErrorWithStatusCode(
                $googleAdAccount,
                $response->getStatus(),
                $response->getBody(),
            );
        }

        $this->processCampaigns(Arr::get($response->getBody(), implode('.', [0, self::RESPONSE_RESULTS])));
    }

    protected function getUrl(): string
    {
        return sprintf(self::ENDPOINT, $this->googleAdAccount->getExternalId());
    }

    protected function getBody(): ?array
    {
        return [
            'query' => sprintf(
                <<<'SQL'
                    SELECT
                        campaign.id,
                        campaign.name,
                        campaign.advertising_channel_type,
                        metrics.clicks,
                        metrics.ctr,
                        metrics.impressions,
                        metrics.average_cpc,
                        metrics.average_cpm,
                        metrics.video_views,
                        metrics.cost_micros,
                        metrics.all_conversions,
                        metrics.revenue_micros,
                        metrics.gross_profit_micros,
                        metrics.average_target_roas,
                        metrics.all_conversions_from_interactions_rate,
                        metrics.content_budget_lost_impression_share,
                        metrics.content_impression_share,
                        metrics.content_rank_lost_impression_share,
                        metrics.search_absolute_top_impression_share,
                        metrics.search_impression_share,
                        metrics.search_top_impression_share,
                        segments.date
                    FROM
                        campaign
                    WHERE
                        segments.date BETWEEN '%s' AND '%s'
                    ORDER BY
                        segments.date
                SQL
                ,
                ($this->startDate ?? now()->subDays(4))->format('Y-m-d'),
                ($this->endDate ?? now()->subDay())->format('Y-m-d'),
            ),
        ];
    }

    protected function getHeaders(): ?array
    {
        return $this->googleAdAccount->getCustomerId()
            ? ['login-customer-id' => $this->googleAdAccount->getCustomerId()]
            : null;
    }

    private function processCampaigns(array $results): void
    {
        foreach ($results as $result) {
            $googleAdCampaign = GoogleAdCampaign::query()->updateOrCreate(
                [
                    GoogleAdCampaign::PROPERTY_GOOGLE_AD_ACCOUNT_ID => $this->googleAdAccount->getId(),
                    GoogleAdCampaign::PROPERTY_EXTERNAL_ID => (int) basename(
                        Arr::get($result, implode('.', [self::RESPONSE_CAMPAIGN, self::RESPONSE_RESOURCE_NAME])),
                    ),
                ],
                [
                    GoogleAdCampaign::PROPERTY_NAME => Arr::get(
                        $result,
                        implode('.', [self::RESPONSE_CAMPAIGN, self::RESPONSE_NAME]),
                    ),
                    GoogleAdCampaign::PROPERTY_ADVERTISING_CHANNEL_TYPE => Arr::get(
                        $result,
                        implode('.', [self::RESPONSE_CAMPAIGN, self::RESPONSE_ADVERTISING_CHANNEL_TYPE]),
                    ),
                ],
            );

            GoogleAdCampaignInsight::query()->updateOrCreate(
                [
                    GoogleAdCampaignInsight::PROPERTY_GOOGLE_AD_CAMPAIGN_ID => $googleAdCampaign->getId(),
                    GoogleAdCampaignInsight::PROPERTY_DATE => Arr::get(
                        $result,
                        implode('.', [self::RESPONSE_SEGMENTS, self::RESPONSE_DATE]),
                    ),
                ],
                [
                    GoogleAdCampaignInsight::PROPERTY_CLICKS =>
                        Arr::get($result, implode('.', [self::RESPONSE_METRICS, self::RESPONSE_CLICKS])) ?? 0,
                    GoogleAdCampaignInsight::PROPERTY_VIDEO_VIEWS =>
                        Arr::get($result, implode('.', [self::RESPONSE_METRICS, self::RESPONSE_VIDEO_VIEWS])) ?? 0,
                    GoogleAdCampaignInsight::PROPERTY_CTR =>
                        Arr::get($result, implode('.', [self::RESPONSE_METRICS, self::RESPONSE_CTR])) ?? 0,
                    GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS =>
                        Arr::get($result, implode('.', [self::RESPONSE_METRICS, self::RESPONSE_IMPRESSIONS])) ?? 0,
                    GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPC => (string) round(
                        (Arr::get($result, implode('.', [self::RESPONSE_METRICS, self::RESPONSE_AVERAGE_CPC])) ?? 0) /
                            1000000,
                        4,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPM => (string) round(
                        (Arr::get($result, implode('.', [self::RESPONSE_METRICS, self::RESPONSE_AVERAGE_CPM])) ?? 0) /
                            1000000,
                        4,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_SPEND => (string) round(
                        (Arr::get($result, implode('.', [self::RESPONSE_METRICS, self::RESPONSE_COST_MICROS])) ?? 0) /
                            1000000,
                        4,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_CONVERSIONS =>
                        Arr::get($result, implode('.', [self::RESPONSE_METRICS, self::RESPONSE_ALL_CONVERSIONS])) ?? 0,
                    GoogleAdCampaignInsight::PROPERTY_CONVERSION_RATE => Arr::get(
                        $result,
                        implode('.', [self::RESPONSE_METRICS, self::RESPONSE_ALL_CONVERSIONS_FROM_INTERACTIONS_RATE]),
                        0,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_REVENUE => Arr::get(
                        $result,
                        implode('.', [self::RESPONSE_METRICS, self::RESPONSE_REVENUE_MICROS]),
                        0,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_ROAS => Arr::get(
                        $result,
                        implode('.', [self::RESPONSE_METRICS, self::RESPONSE_AVERAGE_TARGET_ROAS]),
                        0,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_PROFIT => Arr::get(
                        $result,
                        implode('.', [self::RESPONSE_METRICS, self::RESPONSE_GROSS_PROFIT_MICROS]),
                        0,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_CONTENT_BUDGET_LOST_IMPRESSION_SHARE => round(
                        Arr::get(
                            $result,
                            implode('.', [self::RESPONSE_METRICS, self::RESPONSE_CONTENT_IMPRESSION_SHARE]),
                            0,
                        ) * 100,
                        2,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_CONTENT_IMPRESSION_SHARE => round(
                        Arr::get(
                            $result,
                            implode('.', [self::RESPONSE_METRICS, self::RESPONSE_CONTENT_IMPRESSION_SHARE]),
                            0,
                        ) * 100,
                        2,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_CONTENT_RANK_LOST_IMPRESSION_SHARE => round(
                        Arr::get(
                            $result,
                            implode('.', [self::RESPONSE_METRICS, self::RESPONSE_CONTENT_RANK_LOST_IMPRESSION_SHARE]),
                            0,
                        ) * 100,
                        2,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE => round(
                        Arr::get(
                            $result,
                            implode('.', [self::RESPONSE_METRICS, self::RESPONSE_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE]),
                            0,
                        ) * 100,
                        2,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_SEARCH_IMPRESSION_SHARE => round(
                        Arr::get(
                            $result,
                            implode('.', [self::RESPONSE_METRICS, self::RESPONSE_SEARCH_IMPRESSION_SHARE]),
                            0,
                        ) * 100,
                        2,
                    ),
                    GoogleAdCampaignInsight::PROPERTY_SEARCH_TOP_IMPRESSION_SHARE => round(
                        Arr::get(
                            $result,
                            implode('.', [self::RESPONSE_METRICS, self::RESPONSE_SEARCH_TOP_IMPRESSION_SHARE]),
                            0,
                        ) * 100,
                        2,
                    ),
                ],
            );
        }
    }
}
