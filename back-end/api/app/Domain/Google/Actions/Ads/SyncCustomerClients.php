<?php

namespace App\Domain\Google\Actions\Ads;

use App\Domain\Google\Jobs\Ads\SyncCampaignsWithMetricsJob;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Google\Support\Exceptions\SyncCustomerClientsException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;
use Illuminate\Support\Arr;

class SyncCustomerClients extends GoogleRequest
{
    public const ENDPOINT = 'https://googleads.googleapis.com/v19/customers/%s/googleAds:searchStream';

    public const RESPONSE_RESULTS = 'results';
    public const RESPONSE_CUSTOMER_CLIENT = 'customerClient';
    public const RESPONSE_RESOURCE_NAME = 'resourceName';
    public const RESPONSE_DESCRIPTIVE_NAME = 'descriptiveName';
    public const RESPONSE_HIDDEN = 'hidden';
    public const RESPONSE_LEVEL = 'level';
    public const RESPONSE_TIME_ZONE = 'timeZone';
    public const RESPONSE_TEST_ACCOUNT = 'testAccount';
    public const RESPONSE_MANAGER = 'manager';
    public const RESPONSE_CURRENCY_CODE = 'currencyCode';
    public const RESPONSE_STATUS = 'status';

    private int $customerId;
    private ?int $loginCustomerId;
    private GoogleAccount $googleAccount;

    /**
     * @return GoogleAdAccount[]
     */
    public function execute(GoogleAccount $googleAccount, int $customerId, ?int $loginCustomerId = null): array
    {
        $this->customerId = $customerId;
        $this->loginCustomerId = $loginCustomerId;
        $this->googleAccount = $googleAccount;

        $response = $this->getResponse(RequestType::POST, $googleAccount);

        dd($response);

        if (!$response->isSuccessful()) {
            throw SyncCustomerClientsException::becauseOfHttpErrorWithStatusCode(
                $googleAccount,
                $response->getStatus(),
                $response->getBody(),
            );
        }

        return $this->processCustomerClients(Arr::get($response->getBody(), implode('.', [0, self::RESPONSE_RESULTS])));
    }

    protected function getUrl(): string
    {
        return sprintf(self::ENDPOINT, $this->customerId);
    }

    protected function getBody(): ?array
    {
        return [
            'query' => <<<'SQL'
                SELECT
                    customer_client.descriptive_name,
                    customer_client.status,
                    customer_client.hidden,
                    customer_client.level,
                    customer_client.time_zone,
                    customer_client.test_account,
                    customer_client.manager,
                    customer_client.currency_code,
                    customer_client.resource_name,
                    customer_client.level,
                    customer_client.client_customer
                FROM
                    customer_client
            SQL
        ,
        ];
    }

    protected function getHeaders(): ?array
    {
        return $this->loginCustomerId ? ['login-customer-id' => $this->loginCustomerId] : null;
    }

    private function processCustomerClients(array $customerClients): array
    {
        $accounts = [];

        foreach ($customerClients as $customerClient) {
            $customerClient = Arr::get($customerClient, self::RESPONSE_CUSTOMER_CLIENT);

            $googleAdAccount = GoogleAdAccount::query()->updateOrCreate(
                [
                    GoogleAdAccount::PROPERTY_GOOGLE_ACCOUNT_ID => $this->googleAccount->getId(),
                    GoogleAdAccount::PROPERTY_CUSTOMER_ID => $this->loginCustomerId ?? $this->customerId,
                    GoogleAdAccount::PROPERTY_EXTERNAL_ID => (int) basename(
                        Arr::get($customerClient, self::RESPONSE_RESOURCE_NAME),
                    ),
                ],
                [
                    GoogleAdAccount::PROPERTY_NAME => Arr::get($customerClient, self::RESPONSE_DESCRIPTIVE_NAME),
                    GoogleAdAccount::PROPERTY_STATUS => Arr::get($customerClient, self::RESPONSE_STATUS),
                    GoogleAdAccount::PROPERTY_LEVEL => Arr::get($customerClient, self::RESPONSE_LEVEL),
                    GoogleAdAccount::PROPERTY_IS_MANAGER => Arr::get($customerClient, self::RESPONSE_MANAGER),
                    GoogleAdAccount::PROPERTY_IS_HIDDEN => Arr::get($customerClient, self::RESPONSE_HIDDEN),
                    GoogleAdAccount::PROPERTY_IS_TEST_ACCOUNT => Arr::get($customerClient, self::RESPONSE_TEST_ACCOUNT),
                    GoogleAdAccount::PROPERTY_TIME_ZONE => Arr::get($customerClient, self::RESPONSE_TIME_ZONE),
                    GoogleAdAccount::PROPERTY_CURRENCY => Arr::get($customerClient, self::RESPONSE_CURRENCY_CODE),
                    GoogleAdAccount::PROPERTY_LAST_SYNCED_AT => now(),
                ],
            );

            if ($googleAdAccount->wasRecentlyCreated) {
                dispatch(new SyncCampaignsWithMetricsJob($googleAdAccount, now()->startOfYear()));
            }

            $accounts[] = $googleAdAccount;
        }

        return $accounts;
    }
}
