<?php

namespace App\Domain\Google\Support\Exceptions;

use App\Domain\Google\Models\GoogleAdAccount;
use Exception;

class SyncCampaignsWithMetricsException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(
        GoogleAdAccount $googleAdAccount,
        int $statusCode,
        array $body,
    ): self {
        return new self(
            sprintf(
                'Cannot sync campaigns. Received HTTP error with status code %s for Google Ad Account #%s. Response body:\n\n%s',
                $statusCode,
                $googleAdAccount->getId(),
                json_encode($body),
            ),
        );
    }
}
