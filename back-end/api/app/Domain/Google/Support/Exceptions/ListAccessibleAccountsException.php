<?php

namespace App\Domain\Google\Support\Exceptions;

use App\Domain\Google\Models\GoogleAccount;
use Exception;

class ListAccessibleAccountsException extends Exception
{
    public static function becauseOfHttpErrorWithStatusCode(
        GoogleAccount $googleAccount,
        int $statusCode,
        array $body,
    ): self {
        return new self(
            sprintf(
                'Cannot list accessible ads accounts. Received HTTP error with status code %s for Google Account #%s. Response body:\n\n%s',
                $statusCode,
                $googleAccount->getId(),
                json_encode($body),
            ),
        );
    }
}
