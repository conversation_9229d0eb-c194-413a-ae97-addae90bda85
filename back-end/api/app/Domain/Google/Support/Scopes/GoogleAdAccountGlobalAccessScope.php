<?php

namespace App\Domain\Google\Support\Scopes;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleAdAccount;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class GoogleAdAccountGlobalAccessScope implements Scope
{
    public function apply(Builder $builder, Model $model): void
    {
        $user = Auth::guard('user')->user();

        if (!$user) {
            return;
        }

        $builder->whereHas(
            GoogleAdAccount::RELATION_GOOGLE_ACCOUNT,
            fn(Builder $query) => $query->where(GoogleAccount::PROPERTY_ACCOUNT_ID, $user->getAccountId()),
        );
    }
}
