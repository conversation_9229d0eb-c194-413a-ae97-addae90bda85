<?php

namespace App\Domain\Google\Support\Enums\Ads;

enum GoogleQualityScoreRating: string
{
    case BELOW_AVERAGE = 'BELOW_AVERAGE';
    case AVERAGE = 'AVERAGE';
    case ABOVE_AVERAGE = 'ABOVE_AVERAGE';

    public static function fromResponse(string $response): self
    {
        return match ($response) {
            'Below average' => self::BELOW_AVERAGE,
            'Average' => self::AVERAGE,
            'Above average' => self::ABOVE_AVERAGE,
        };
    }

    public function toReadableString(): string
    {
        return match ($this) {
            self::BELOW_AVERAGE => 'Below average',
            self::AVERAGE => 'Average',
            self::ABOVE_AVERAGE => 'Above average',
        };
    }

    public function toColor(): string
    {
        return match ($this) {
            self::BELOW_AVERAGE => '#dc2626',
            self::AVERAGE => '#eab308',
            self::ABOVE_AVERAGE => '#22c55e',
        };
    }
}
