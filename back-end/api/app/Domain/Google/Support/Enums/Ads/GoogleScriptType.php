<?php

namespace App\Domain\Google\Support\Enums\Ads;

use App\Domain\Google\Models\GoogleAdAccount;

enum GoogleScriptType: string
{
    case QUALITY_SCORE_CHECKER = 'QUALITY_SCORE_CHECKER';
    case AUCTION_INSIGHTS = 'AUCTION_INSIGHTS';

    public function relation(): string
    {
        return match ($this) {
            self::QUALITY_SCORE_CHECKER => GoogleAdAccount::RELATION_QUALITY_SCORE_SCRIPT,
            self::AUCTION_INSIGHTS => GoogleAdAccount::RELATION_AUCTION_INSIGHTS_SCRIPT,
        };
    }
}
