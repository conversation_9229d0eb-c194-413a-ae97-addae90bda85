<?php

namespace App\Domain\Google\Support\Enums\Scripts;

use App\Domain\Google\Models\GoogleFile;
use App\Domain\Google\Models\GoogleScript;
use Exception;

class SyncQualityScoreCheckerException extends Exception
{
    public static function becauseOfMissingTab(GoogleFile $file): self
    {
        return new self(sprintf('Sync failed because of missing tab in file. file Id: %s', $file->getId()));
    }
}
