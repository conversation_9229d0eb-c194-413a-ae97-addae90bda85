<?php

namespace App\Domain\Google\Support\Enums\Drive;

enum GoogleFileType: string
{
    case AUDIO = 'AUDIO';
    case DOC = 'DOC';
    case DRAWING = 'DRAWING';
    case FILE = 'FILE';
    case FOLDER = 'FOLDER';
    case FORM = 'FORM';
    case FUSION_TABLE = 'FUSION_TABLE';
    case JAM = 'JAM';
    case EMAIL_LAYOUT = 'EMAIL_LAYOUT';
    case MAP = 'MAP';
    case PHOTO = 'PHOTO';
    case SLIDE = 'SLIDE';
    case SCRIPT = 'SCRIPT';
    case SITE = 'SITE';
    case SHEETS = 'SHEETS';
    case VID = 'VID';

    public function mimeType(): string
    {
        return match ($this) {
            self::AUDIO => 'application/vnd.google-apps.audio',
            self::DOC => 'application/vnd.google-apps.document',
            self::DRAWING => 'application/vnd.google-apps.drawing',
            self::FILE => 'application/vnd.google-apps.file',
            self::FOLDER => 'application/vnd.google-apps.folder',
            self::FORM => 'application/vnd.google-apps.form',
            self::FUSION_TABLE => 'application/vnd.google-apps.fusiontable',
            self::JAM => 'application/vnd.google-apps.jam',
            self::EMAIL_LAYOUT => 'application/vnd.google-apps.mail-layout',
            self::MAP => 'application/vnd.google-apps.map',
            self::PHOTO => 'application/vnd.google-apps.photo',
            self::SLIDE => 'application/vnd.google-apps.presentation',
            self::SCRIPT => 'application/vnd.google-apps.script',
            self::SITE => 'application/vnd.google-apps.site',
            self::SHEETS => 'application/vnd.google-apps.spreadsheet',
            self::VID => 'application/vnd.google-apps.vid',
        };
    }
}
