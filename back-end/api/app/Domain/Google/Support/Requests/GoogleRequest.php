<?php

namespace App\Domain\Google\Support\Requests;

use App\Domain\Google\Actions\Authentication\GoogleRefreshToken;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Support\Exceptions\GoogleRefreshTokenException;
use App\Domain\Support\Dto\HttpResponseDto;
use App\Support\Enums\RequestType;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use PhpParser\Node\Expr\Throw_;

abstract class GoogleRequest
{
    private GoogleAccount $googleAccount;

    public function getResponse(RequestType $type, GoogleAccount $googleAccount): HttpResponseDto
    {
        $dto = new HttpResponseDto();

        $this->googleAccount = $googleAccount;

        $headers = [
            'Developer-token' => config('services.google.ads_developer_token'),
        ];

        if ($this->getHeaders()) {
            $headers = array_merge($headers, $this->getHeaders());
        }

        $request = Http::timeout(60)->withToken($this->getToken())->withHeaders($headers);

        if ($this->getQueryParameters()) {
            $request = $request->withQueryParameters($this->getQueryParameters());
        }

        if ($this->getBody()) {
            $request = $request->withBody(json_encode($this->getBody()));
        }

        $response = $request->send($type->value, $this->getUrl());

        $dto->setStatus($response->status())->setSuccessful($response->successful())->setBody($response->json());

        return $dto;
    }

    protected function getQueryParameters(): ?array
    {
        return null;
    }

    protected function getBody(): ?array
    {
        return null;
    }

    protected function getHeaders(): ?array
    {
        return null;
    }

    abstract protected function getUrl(): string;

    private function getToken(): ?string
    {
        $token = app(GoogleRefreshToken::class)->execute($this->googleAccount);

        if (!$token) {
            throw GoogleRefreshTokenException::missingToken();
        }

        return $token;
    }
}
