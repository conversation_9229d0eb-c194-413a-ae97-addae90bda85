<?php

namespace App\Domain\Google\Dto\Drive;

use App\Domain\Google\Models\GoogleDrive;
use App\Domain\Google\Support\Enums\Drive\GoogleFileType;

class CreateFileDto
{
    private string $name;
    private GoogleFileType $type;
    private ?GoogleDrive $drive;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): CreateFileDto
    {
        $this->name = $name;
        return $this;
    }

    public function getType(): GoogleFileType
    {
        return $this->type;
    }

    public function setType(GoogleFileType $type): CreateFileDto
    {
        $this->type = $type;
        return $this;
    }

    public function getDrive(): ?GoogleDrive
    {
        return $this->drive;
    }

    public function setDrive(?GoogleDrive $drive): CreateFileDto
    {
        $this->drive = $drive;
        return $this;
    }
}
