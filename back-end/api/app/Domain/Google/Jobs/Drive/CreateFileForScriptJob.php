<?php

namespace App\Domain\Google\Jobs\Drive;

use App\Domain\Google\Actions\Drive\CreateFile;
use App\Domain\Google\Dto\Drive\CreateFileDto;
use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Google\Models\GoogleScript;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptError;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptStatus;
use App\Domain\Google\Support\Enums\Drive\GoogleFileType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class CreateFileForScriptJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private int $scriptId;

    public function __construct(GoogleScript $script, private readonly GoogleFileType $type)
    {
        $this->scriptId = $script->getId();
    }

    public function handle(): void
    {
        /** @var GoogleScript $script */
        $script = GoogleScript::query()
            ->with([
                implode('.', [GoogleScript::RELATION_GOOGLE_AD_ACCOUNT, GoogleAdAccount::RELATION_GOOGLE_ACCOUNT]),
                GoogleScript::RELATION_GOOGLE_DRIVE,
            ])
            ->findOrFail($this->scriptId);

        $dto = (new CreateFileDto())
            ->setDrive($script->getGoogleDrive())
            ->setType($this->type)
            ->setName(
                sprintf(
                    '%s (%s) Quality Score Checker Sheet',
                    $script->getGoogleAdAccount()->getName(),
                    $script->getGoogleAdAccount()->getExternalId(),
                ),
            );

        $file = app(CreateFile::class)->execute($script->getGoogleAdAccount()->getGoogleAccount(), $dto);

        $script->setStatus(GoogleScriptStatus::SUCCESSFUL)->setGoogleFileId($file->getId())->save();
    }

    public function failed(\Exception $exception): void
    {
        GoogleScript::query()
            ->where(GoogleScript::PROPERTY_ID, $this->scriptId)
            ->update([
                GoogleScript::PROPERTY_ERROR => GoogleScriptError::UNKNOWN,
                GoogleScript::PROPERTY_ERROR_DETAILS => [$exception->getMessage()],
                GoogleScript::PROPERTY_STATUS => GoogleScriptStatus::ERROR_CREATING_FILE,
            ]);
    }
}
