<?php

namespace App\Domain\Google\Jobs\Drive;

use App\Domain\Google\Actions\Drive\SyncGoogleFiles;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleDrive;
use App\Domain\Google\Support\Enums\Drive\GoogleFileType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class SyncGoogleFilesJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private int $googleAccountId;
    private int $driveId;

    public function __construct(
        GoogleAccount $account,
        private readonly GoogleFileType $type = GoogleFileType::SHEETS,
        private readonly ?string $pageToken = null,
        ?GoogleDrive $drive = null,
    ) {
        $this->googleAccountId = $account->getId();
        $this->driveId = $drive->getId();
    }

    public function handle(): void
    {
        $account = GoogleAccount::query()->findOrFail($this->googleAccountId);
        $drive = GoogleDrive::query()->find($this->driveId);

        $token = app(SyncGoogleFiles::class)->execute($account, $this->type, $this->pageToken, $drive);

        if (!$token) {
            return;
        }

        $job = new self($account, $this->type, $token);

        if ($this->chained) {
            $this->prependToChain($job);
        } else {
            dispatch($job);
        }
    }
}
