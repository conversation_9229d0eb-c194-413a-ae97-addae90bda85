<?php

namespace App\Domain\Google\Jobs\Drive;

use App\Domain\Google\Actions\Drive\CreateDrive;
use App\Domain\Google\Dto\Drive\CreateDriveDto;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Google\Models\GoogleScript;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptError;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class CreateDriveForScriptJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private int $scriptId;

    public function __construct(GoogleScript $script, private readonly CreateDriveDto $dto)
    {
        $this->scriptId = $script->getId();
    }

    public function handle(): void
    {
        /** @var GoogleScript $script */
        $script = GoogleScript::query()
            ->with(implode('.', [GoogleScript::RELATION_GOOGLE_AD_ACCOUNT, GoogleAdAccount::RELATION_GOOGLE_ACCOUNT]))
            ->findOrFail($this->scriptId);

        $drive = app(CreateDrive::class)->execute($script->getGoogleAdAccount()->getGoogleAccount(), $this->dto);

        $script->setStatus(GoogleScriptStatus::CREATING_FILE)->setGoogleDriveId($drive->getId())->save();
    }

    public function failed(\Exception $exception): void
    {
        GoogleScript::query()
            ->where(GoogleScript::PROPERTY_ID, $this->scriptId)
            ->update([
                GoogleScript::PROPERTY_ERROR => GoogleScriptError::UNKNOWN,
                GoogleScript::PROPERTY_ERROR_DETAILS => [$exception->getMessage()],
                GoogleScript::PROPERTY_STATUS => GoogleScriptStatus::ERROR_CREATING_DRIVE,
            ]);
    }
}
