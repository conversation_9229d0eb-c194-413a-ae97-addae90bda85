<?php

namespace App\Domain\Google\Jobs\Drive;

use App\Domain\Google\Actions\Drive\SyncGoogleDrives;
use App\Domain\Google\Models\GoogleAccount;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class SyncGoogleDrivesJ<PERSON> implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private int $googleAccountId;

    public function __construct(GoogleAccount $account, private readonly ?string $pageToken = null)
    {
        $this->googleAccountId = $account->getId();
    }

    public function handle(): void
    {
        $account = GoogleAccount::query()->findOrFail($this->googleAccountId);

        $token = app(SyncGoogleDrives::class)->execute($account);

        if (!$token) {
            return;
        }

        dispatch(new self($account, $token));
    }
}
