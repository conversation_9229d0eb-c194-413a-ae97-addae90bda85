<?php

namespace App\Domain\Google\Jobs\Drive;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Support\Enums\Drive\GoogleFileType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Bus;

class DispatchSyncGoogleFilesJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    public const array TYPES_TO_SYNC = [GoogleFileType::SHEETS];

    private int $accountId;

    public function __construct(GoogleAccount $account)
    {
        $this->accountId = $account->getId();
    }

    public function handle(): void
    {
        $account = GoogleAccount::query()->findOrFail($this->accountId);

        $jobs = [];

        foreach (self::TYPES_TO_SYNC as $type) {
            $jobs[] = new SyncGoogleFilesJob($account, $type);
        }

        Bus::dispatch<PERSON>hain($jobs);
    }
}
