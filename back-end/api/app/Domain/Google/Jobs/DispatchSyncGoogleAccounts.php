<?php

namespace App\Domain\Google\Jobs;

use App\Domain\Google\Models\GoogleAccount;
use Illuminate\Support\Collection;

class DispatchSyncGoogleAccounts
{
    public function handle(): void
    {
        GoogleAccount::query()->chunk(500, function (Collection $accounts) {
            foreach ($accounts as $account) {
                dispatch(new SyncGoogleAccountJob($account));
            }
        });
    }
}
