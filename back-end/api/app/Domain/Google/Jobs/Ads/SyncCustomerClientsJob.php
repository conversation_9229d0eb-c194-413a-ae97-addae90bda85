<?php

namespace App\Domain\Google\Jobs\Ads;

use App\Domain\Google\Actions\Ads\SyncCustomerClients;
use App\Domain\Google\Models\GoogleAccount;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class SyncCustomerClientsJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private int $googleAccountId;

    public function __construct(
        GoogleAccount $googleAccount,
        private readonly int $customerId,
        private readonly ?int $loginCustomerId = null,
    ) {
        $this->googleAccountId = $googleAccount->getId();
    }

    public function handle(): void
    {
        $googleAccount = GoogleAccount::query()->findOrFail($this->googleAccountId);

        $adAccounts = app(SyncCustomerClients::class)->execute(
            $googleAccount,
            $this->customerId,
            $this->loginCustomerId,
        );

        foreach ($adAccounts as $adAccount) {
            if ($adAccount->getExternalId() === $this->customerId) {
                continue;
            }

            if (!$adAccount->getIsManager()) {
                continue;
            }

            $job = new self($googleAccount, $adAccount->getExternalId(), $adAccount->getCustomerId());
            dispatch($job);
        }
    }
}
