<?php

namespace App\Domain\Google\Jobs\Ads;

use App\Domain\Google\Actions\Ads\SyncCampaignsWithMetrics;
use App\Domain\Google\Models\GoogleAdAccount;
use Carbon\CarbonInterface;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class SyncCampaignsWithMetricsJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private int $googleAdAccountId;

    public function __construct(
        GoogleAdAccount $googleAdAccount,
        private readonly ?CarbonInterface $startDate = null,
        private readonly ?CarbonInterface $endDate = null,
    ) {
        $this->googleAdAccountId = $googleAdAccount->getId();
    }

    public function handle(): void
    {
        $googleAdAccount = GoogleAdAccount::query()->findOrFail($this->googleAdAccountId);

        app(SyncCampaignsWithMetrics::class)->execute($googleAdAccount, $this->startDate, $this->endDate);
    }
}
