<?php

namespace App\Domain\Google\Jobs\Ads;

use App\Domain\Google\Actions\Ads\ListAccessibleAccounts;
use App\Domain\Google\Models\GoogleAccount;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class ListAccessibleAccountsJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private int $googleAccountId;

    public function __construct(GoogleAccount $googleAccount)
    {
        $this->googleAccountId = $googleAccount->getId();
    }

    public function handle(): void
    {
        $googleAccount = GoogleAccount::query()->findOrFail($this->googleAccountId);

        $accessibleAccounts = app(ListAccessibleAccounts::class)->execute($googleAccount);

        foreach ($accessibleAccounts as $account) {
            dispatch(new SyncCustomerClientsJob($googleAccount, (int) basename($account)));
        }
    }
}
