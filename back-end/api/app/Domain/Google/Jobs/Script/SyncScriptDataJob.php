<?php

namespace App\Domain\Google\Jobs\Script;

use App\Domain\Google\Actions\Script\Scripts\SyncAuctionInsightsData;
use App\Domain\Google\Actions\Script\Scripts\SyncQualityScoreCheckerData;
use App\Domain\Google\Models\GoogleScript;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class SyncScriptDataJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private int $scriptId;

    public function __construct(GoogleScript $script)
    {
        $this->scriptId = $script->getId();
    }

    public function handle(): void
    {
        /** @var GoogleScript $script */
        $script = GoogleScript::query()
            ->with([
                GoogleScript::RELATION_GOOGLE_AD_ACCOUNT,
                GoogleScript::RELATION_GOOGLE_FILE,
                GoogleScript::RELATION_GOOGLE_DRIVE,
            ])
            ->findOrFail($this->scriptId);

        $executable = match ($script->getType()) {
            GoogleScriptType::AUCTION_INSIGHTS => app(SyncAuctionInsightsData::class),
            GoogleScriptType::QUALITY_SCORE_CHECKER => app(SyncQualityScoreCheckerData::class),
        };

        $executable->execute($script);
    }
}
