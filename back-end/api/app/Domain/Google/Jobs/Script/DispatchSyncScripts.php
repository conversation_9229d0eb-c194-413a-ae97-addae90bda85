<?php

namespace App\Domain\Google\Jobs\Script;

use App\Domain\Google\Jobs\Drive\SyncGoogleFilesJob;
use App\Domain\Google\Models\GoogleScript;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptType;
use App\Domain\Google\Support\Enums\Drive\GoogleFileType;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;

class DispatchSyncScripts
{
    public function handle(): void
    {
        GoogleScript::query()->chunk(500, function (Collection $scripts) {
            foreach ($scripts as $script) {
                /** @var GoogleScript $script */

                $jobs = [new SyncScriptDataJob($script)];

                if ($script->getType() === GoogleScriptType::AUCTION_INSIGHTS) {
                    array_unshift(
                        $jobs,
                        new SyncGoogleFilesJob(
                            $script->getGoogleDrive()->getGoogleAccount(),
                            GoogleFileType::SHEETS,
                            drive: $script->getGoogleDrive(),
                        ),
                    );
                }

                Bus::dispatch<PERSON>hain($jobs);
            }
        });
    }
}
