<?php

namespace App\Domain\Google\Jobs;

use App\Domain\Google\Jobs\Ads\ListAccessibleAccountsJob;
use App\Domain\Google\Jobs\Drive\DispatchSyncGoogleFilesJob;
use App\Domain\Google\Jobs\Drive\SyncGoogleDrivesJob;
use App\Domain\Google\Models\GoogleAccount;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Bus;

class SyncGoogleAccountJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private int $accountId;

    public function __construct(GoogleAccount $account)
    {
        $this->accountId = $account->getId();
    }

    public function handle(): void
    {
        $account = GoogleAccount::query()->findOrFail($this->accountId);

        dispatch(new ListAccessibleAccountsJob($account));

        Bus::dispatchChain([new SyncGoogleDrivesJob($account), new DispatchSyncGoogleFilesJob($account)]);
    }
}
