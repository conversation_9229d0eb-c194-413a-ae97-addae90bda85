<?php

namespace App\Domain\Profile\Actions;

use App\Domain\Profile\Dto\UpdateOrCreateUserDto;
use App\Domain\Profile\Models\Account;
use App\Domain\Profile\Models\User;
use App\Domain\Support\Actions\StoreMedia;
use Illuminate\Support\Facades\DB;

class UpdateOrCreateUser
{
    public function execute(Account $account, UpdateOrCreateUserDto $dto): User
    {
        return DB::transaction(function () use ($account, $dto) {
            $imagePath = null;

            if ($dto->getImage()) {
                $imagePath = app(StoreMedia::class)->execute(
                    $dto->getImage(),
                    sprintf('accounts/%s/users', $account->getId()),
                );
            }

            $user = User::query()->updateOrCreate(
                [
                    User::PROPERTY_ACCOUNT_ID => $account->getId(),
                    User::PROPERTY_EMAIL => $dto->getEmail(),
                ],
                [
                    User::PROPERTY_FIRSTNAME => $dto->getFirstname(),
                    User::PROPERTY_LASTNAME => $dto->getLastname(),
                    User::PROPERTY_IMAGE_PATH => $imagePath,
                    User::PROPERTY_ROLE => $dto->getRole(),
                ],
            );

            /** @todo: add email */

            return $user;
        });
    }
}
