<?php

namespace App\Domain\Profile\Actions;

use App\Domain\Profile\Dto\RegisterAccountDto;
use App\Domain\Profile\Models\Account;
use App\Domain\Profile\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class RegisterAccount
{
    public function execute(RegisterAccountDto $dto): Account
    {
        return DB::transaction(function () use ($dto) {
            $account = Account::query()->create([
                Account::PROPERTY_NAME => $dto->getName(),
                Account::PROPERTY_VERIFIED_AT => now(),
            ]);

            User::query()->create([
                User::PROPERTY_ACCOUNT_ID => $account->getId(),
                User::PROPERTY_FIRSTNAME => $dto->getFirstname(),
                User::PROPERTY_LASTNAME => $dto->getLastname(),
                User::PROPERTY_EMAIL => $dto->getEmail(),
                User::PROPERTY_PASSWORD => $dto->getPassword() ? Hash::make($dto->getPassword()) : null,
                User::PROPERTY_IMAGE_PATH => $dto->getImagePath(),
                User::PROPERTY_VERIFIED_AT => now(),
            ]);

            return $account;
        });
    }
}
