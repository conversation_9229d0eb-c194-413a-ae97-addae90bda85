<?php

namespace App\Domain\Profile\Support\Scopes;

use App\Domain\Profile\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class AccountUserGlobalAccessScope implements Scope
{
    public function apply(Builder $builder, Model $model): void
    {
        if (!Auth::guard('user')->hasUser()) {
            return;
        }

        $user = Auth::guard('user')->user();

        $builder->where(User::PROPERTY_ACCOUNT_ID, $user->getAccountId());
    }
}
