<?php

namespace App\Domain\Profile\Support\Observers;

use App\Domain\Profile\Models\Account;
use App\Domain\Stripe\Models\StripeInformation;
use App\Domain\Stripe\Support\Jobs\UpdateOrCreateCustomerJob;

class StripeAccountObserver
{
    public function created(Account $account): void
    {
        StripeInformation::query()->updateOrCreate([
            StripeInformation::PROPERTY_ACCOUNT_ID => $account->getId(),
        ]);

        dispatch(new UpdateOrCreateCustomerJob($account));
    }

    public function updated(Account $account): void
    {
        dispatch(new UpdateOrCreateCustomerJob($account));
    }
}
