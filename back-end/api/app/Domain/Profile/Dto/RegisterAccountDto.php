<?php

namespace App\Domain\Profile\Dto;

class RegisterAccountDto
{
    private string $name;
    private string $firstname;
    private string $lastname;
    private string $email;
    private ?string $password = null;
    private ?string $imagePath = null;

    public function getFirstname(): string
    {
        return $this->firstname;
    }

    public function setFirstname(string $firstname): RegisterAccountDto
    {
        $this->firstname = $firstname;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): RegisterAccountDto
    {
        $this->name = $name;
        return $this;
    }

    public function getLastname(): string
    {
        return $this->lastname;
    }

    public function setLastname(string $lastname): RegisterAccountDto
    {
        $this->lastname = $lastname;
        return $this;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): RegisterAccountDto
    {
        $this->email = $email;
        return $this;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(?string $password): RegisterAccountDto
    {
        $this->password = $password;
        return $this;
    }

    public function getImagePath(): ?string
    {
        return $this->imagePath;
    }

    public function setImagePath(?string $imagePath): RegisterAccountDto
    {
        $this->imagePath = $imagePath;
        return $this;
    }
}
