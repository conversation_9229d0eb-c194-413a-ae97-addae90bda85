<?php

namespace App\Domain\Profile\Dto;

use App\Domain\Profile\Support\Enums\UserRole;
use Illuminate\Http\UploadedFile;

class UpdateOrCreateUserDto
{
    private string $firstname;
    private string $lastname;
    private string $email;
    private UserRole $role;
    private ?UploadedFile $image = null;

    public function getFirstname(): string
    {
        return $this->firstname;
    }

    public function setFirstname(string $firstname): UpdateOrCreateUserDto
    {
        $this->firstname = $firstname;
        return $this;
    }

    public function getLastname(): string
    {
        return $this->lastname;
    }

    public function setLastname(string $lastname): UpdateOrCreateUserDto
    {
        $this->lastname = $lastname;
        return $this;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): UpdateOrCreateUserDto
    {
        $this->email = $email;
        return $this;
    }

    public function getImage(): ?UploadedFile
    {
        return $this->image;
    }

    public function setImage(?UploadedFile $image): UpdateOrCreateUserDto
    {
        $this->image = $image;
        return $this;
    }

    public function getRole(): UserRole
    {
        return $this->role;
    }

    public function setRole(UserRole $role): UpdateOrCreateUserDto
    {
        $this->role = $role;
        return $this;
    }
}
