<?php

namespace App\Domain\Profile\Models;

use App\Domain\Profile\Support\Enums\UserRole;
use App\Domain\Profile\Support\Scopes\AccountUserGlobalAccessScope;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Carbon;
use Laravel\Sanctum\HasApiTokens;
use Tests\Domain\Profile\Factories\UserFactory;

class User extends Authenticatable
{
    use TimestampableMethods;
    use HasFactory;
    use HasApiTokens;

    public const TABLE_NAME = 'users';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_ACCOUNT_ID = 'account_id';
    public const PROPERTY_FIRSTNAME = 'firstname';
    public const PROPERTY_LASTNAME = 'lastname';
    public const PROPERTY_EMAIL = 'email';
    public const PROPERTY_PASSWORD = 'password';
    public const PROPERTY_IMAGE_PATH = 'image_path';
    public const PROPERTY_VERIFIED_AT = 'verified_at';
    public const PROPERTY_LAST_LOGIN_AT = 'last_login_at';
    public const PROPERTY_ROLE = 'role';

    public const FACTORY_CLASS = UserFactory::class;

    public const RELATION_ACCOUNT = 'account';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    protected $casts = [
        self::PROPERTY_VERIFIED_AT => 'datetime',
        self::PROPERTY_LAST_LOGIN_AT => 'datetime',
        self::PROPERTY_ROLE => UserRole::class,
    ];

    protected $with = [self::RELATION_ACCOUNT];

    public static function booted(): void
    {
        static::addGlobalScope(new AccountUserGlobalAccessScope());
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class, self::PROPERTY_ACCOUNT_ID);
    }

    public function getAccount(): Account
    {
        return $this->{self::RELATION_ACCOUNT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getAccountId(): int
    {
        return $this->{self::PROPERTY_ACCOUNT_ID};
    }

    public function setAccountId(int $value): self
    {
        $this->{self::PROPERTY_ACCOUNT_ID} = $value;

        return $this;
    }

    public function getFirstname(): string
    {
        return $this->{self::PROPERTY_FIRSTNAME};
    }

    public function setFirstname(string $value): self
    {
        $this->{self::PROPERTY_FIRSTNAME} = $value;

        return $this;
    }

    public function getLastname(): string
    {
        return $this->{self::PROPERTY_LASTNAME};
    }

    public function setLastname(string $value): self
    {
        $this->{self::PROPERTY_LASTNAME} = $value;

        return $this;
    }

    public function getEmail(): string
    {
        return $this->{self::PROPERTY_EMAIL};
    }

    public function setEmail(string $value): self
    {
        $this->{self::PROPERTY_EMAIL} = $value;

        return $this;
    }

    public function getPassword(): ?string
    {
        return $this->{self::PROPERTY_PASSWORD};
    }

    public function setPassword(?string $value): self
    {
        $this->{self::PROPERTY_PASSWORD} = $value;

        return $this;
    }

    public function getImagePath(): ?string
    {
        return $this->{self::PROPERTY_IMAGE_PATH};
    }

    public function setImagePath(?string $value): self
    {
        $this->{self::PROPERTY_IMAGE_PATH} = $value;

        return $this;
    }

    public function getVerifiedAt(): ?Carbon
    {
        return $this->{self::PROPERTY_VERIFIED_AT};
    }

    public function setVerifiedAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_VERIFIED_AT} = $value;

        return $this;
    }

    public function getLastLoginAt(): ?Carbon
    {
        return $this->{self::PROPERTY_LAST_LOGIN_AT};
    }

    public function setLastLoginAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_LAST_LOGIN_AT} = $value;

        return $this;
    }

    public function getRole(): UserRole
    {
        return $this->{self::PROPERTY_ROLE};
    }

    public function setRole(UserRole $value): self
    {
        $this->{self::PROPERTY_ROLE} = $value;

        return $this;
    }

    public function getFullName(): string
    {
        return $this->getFirstName() . ' ' . $this->getLastName();
    }
}
