<?php

namespace App\Domain\Profile\Models;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Stripe\Models\StripeInformation;
use App\Domain\Stripe\Models\Subscription;
use App\Domain\Stripe\Support\Traits\StripeCustomer;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Tests\Domain\Profile\Factories\AccountFactory;

class Account extends Model
{
    use TimestampableMethods;
    use HasFactory;
    use StripeCustomer;

    public const TABLE_NAME = 'accounts';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_STRIPE_ID = 'stripe_id';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_VERIFIED_AT = 'verified_at';

    public const RELATION_USERS = 'users';
    public const RELATION_GOOGLE_ACCOUNTS = 'googleAccounts';
    public const RELATION_STRIPE_INFORMATION = 'stripeInformation';
    public const RELATION_ACTIVE_SUBSCRIPTION = 'activeSubscription';

    public const FACTORY_CLASS = AccountFactory::class;

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_VERIFIED_AT => 'datetime',
    ];

    protected $guarded = [self::PROPERTY_ID];

    protected $with = [self::RELATION_ACTIVE_SUBSCRIPTION, self::RELATION_STRIPE_INFORMATION];

    public function users(): HasMany
    {
        return $this->hasMany(User::class, User::PROPERTY_ACCOUNT_ID);
    }

    public function getUsers(): Collection
    {
        return $this->{self::RELATION_USERS};
    }

    public function googleAccounts(): HasMany
    {
        return $this->hasMany(GoogleAccount::class, GoogleAccount::PROPERTY_ACCOUNT_ID);
    }

    public function getGoogleAccounts(): Collection
    {
        return $this->{self::RELATION_GOOGLE_ACCOUNTS};
    }

    public function stripeInformation(): HasOne
    {
        return $this->hasOne(StripeInformation::class, StripeInformation::PROPERTY_ACCOUNT_ID);
    }

    public function getStripeInformation(): ?StripeInformation
    {
        return $this->{self::RELATION_STRIPE_INFORMATION};
    }

    public function activeSubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)
            ->where(
                fn(Builder $query) => $query
                    ->active()
                    ->orWhere(fn(Builder $query) => $query->onTrial()->notCanceled())
                    ->orWhere(fn(Builder $query) => $query->onGracePeriod()),
            )
            ->orderBy(Subscription::PROPERTY_PERIOD_STARTS_AT, 'ASC');
    }

    public function getActiveSubscription(): ?Subscription
    {
        return $this->{self::RELATION_ACTIVE_SUBSCRIPTION};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getStripeId(): ?string
    {
        return $this->{self::PROPERTY_STRIPE_ID};
    }

    public function setStripeId(?string $value): self
    {
        $this->{self::PROPERTY_STRIPE_ID} = $value;

        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;

        return $this;
    }

    public function getVerifiedAt(): ?Carbon
    {
        return $this->{self::PROPERTY_VERIFIED_AT};
    }

    public function setVerifiedAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_VERIFIED_AT} = $value;

        return $this;
    }
}
