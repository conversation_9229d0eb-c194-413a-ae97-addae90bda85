<?php

namespace App\Domain\Stripe\Models;

use App\Domain\Stripe\Support\Enums\PriceType;
use App\Domain\Stripe\Support\Enums\RecurringInterval;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Price extends Model
{
    use TimestampableMethods;
    use HasFactory;

    public const TABLE_NAME = 'stripe_prices';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_PRODUCT_ID = 'product_id';
    public const PROPERTY_STRIPE_ID = 'stripe_id';
    public const PROPERTY_PRICE = 'price';
    public const PROPERTY_DEFAULT = 'default';
    public const PROPERTY_IS_HIDDEN = 'is_hidden';
    public const PROPERTY_TYPE = 'type';
    public const PROPERTY_RECURRING_INTERVAL = 'recurring_interval';
    public const PROPERTY_RECURRING_INTERVAL_COUNT = 'recurring_interval_count';
    public const PROPERTY_CURRENCY = 'currency';

    public const RELATION_PRODUCT = 'product';

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_TYPE => PriceType::class,
        self::PROPERTY_RECURRING_INTERVAL => RecurringInterval::class,
        self::PROPERTY_DEFAULT => 'bool',
        self::PROPERTY_IS_HIDDEN => 'bool',
    ];

    protected $guarded = [self::PROPERTY_ID];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function getProduct(): Product
    {
        return $this->{self::RELATION_PRODUCT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getProductId(): string
    {
        return $this->{self::PROPERTY_PRODUCT_ID};
    }

    public function setProductId(string $value): self
    {
        $this->{self::PROPERTY_PRODUCT_ID} = $value;

        return $this;
    }

    public function getStripeId(): string
    {
        return $this->{self::PROPERTY_STRIPE_ID};
    }

    public function setStripeId(string $value): self
    {
        $this->{self::PROPERTY_STRIPE_ID} = $value;

        return $this;
    }

    public function getPrice(): ?int
    {
        return $this->{self::PROPERTY_PRICE};
    }

    public function setPrice(?int $value): self
    {
        $this->{self::PROPERTY_PRICE} = $value;

        return $this;
    }

    public function getDefault(): bool
    {
        return $this->{self::PROPERTY_DEFAULT};
    }

    public function setDefault(bool $value): self
    {
        $this->{self::PROPERTY_DEFAULT} = $value;

        return $this;
    }

    public function getType(): PriceType
    {
        return $this->{self::PROPERTY_TYPE};
    }

    public function setType(PriceType $value): self
    {
        $this->{self::PROPERTY_TYPE} = $value;

        return $this;
    }

    public function getRecurringInterval(): ?RecurringInterval
    {
        return $this->{self::PROPERTY_RECURRING_INTERVAL};
    }

    public function setRecurringInterval(?RecurringInterval $value): self
    {
        $this->{self::PROPERTY_RECURRING_INTERVAL} = $value;

        return $this;
    }

    public function getRecurringIntervalCount(): ?int
    {
        return $this->{self::PROPERTY_RECURRING_INTERVAL_COUNT};
    }

    public function setRecurringIntervalCount(?int $value): self
    {
        $this->{self::PROPERTY_RECURRING_INTERVAL_COUNT} = $value;

        return $this;
    }

    public function getIsHidden(): bool
    {
        return $this->{self::PROPERTY_IS_HIDDEN};
    }

    public function setIsHidden(bool $value): self
    {
        $this->{self::PROPERTY_IS_HIDDEN} = $value;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->{self::PROPERTY_CURRENCY};
    }

    public function setCurrency(?string $value): self
    {
        $this->{self::PROPERTY_CURRENCY} = $value;

        return $this;
    }
}
