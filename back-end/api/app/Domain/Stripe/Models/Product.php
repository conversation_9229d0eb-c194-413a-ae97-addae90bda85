<?php

namespace App\Domain\Stripe\Models;

use App\Domain\Stripe\Support\Enums\RecurringInterval;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;

class Product extends Model
{
    use TimestampableMethods;
    use HasFactory;

    public const TABLE_NAME = 'stripe_products';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_STRIPE_ID = 'stripe_id';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_DESCRIPTION = 'description';
    public const PROPERTY_FEATURES = 'features';
    public const PROPERTY_LIMIT = 'limit';
    public const PROPERTY_IS_HIDDEN = 'is_hidden';
    public const PROPERTY_HAS_TRIAL = 'has_trial';
    public const PROPERTY_WHITELABEL = 'whitelabel';

    public const RELATION_PRICES = 'prices';
    public const RELATION_MONTHLY_PRICE = 'monthlyPrice';
    public const RELATION_YEARLY_PRICE = 'yearlyPrice';
    public const RELATION_SUBSCRIPTIONS = 'subscriptions';

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_IS_HIDDEN => 'boolean',
        self::PROPERTY_FEATURES => 'array',
    ];

    protected $guarded = [self::PROPERTY_ID];

    public function prices(): HasMany
    {
        return $this->hasMany(Price::class);
    }

    public function getPrices(): Collection
    {
        return $this->{self::RELATION_PRICES};
    }

    public function monthlyPrice(): HasOne
    {
        return $this->hasOne(Price::class)
            ->where(Price::PROPERTY_IS_HIDDEN, false)
            ->where(Price::PROPERTY_RECURRING_INTERVAL, RecurringInterval::MONTH);
    }

    public function getMonthlyPrice(): ?Price
    {
        return $this->{self::RELATION_MONTHLY_PRICE};
    }

    public function yearlyPrice(): HasOne
    {
        return $this->hasOne(Price::class)
            ->where(Price::PROPERTY_IS_HIDDEN, false)
            ->where(Price::PROPERTY_RECURRING_INTERVAL, RecurringInterval::YEAR);
    }

    public function getYearlyPrice(): ?Price
    {
        return $this->{self::RELATION_YEARLY_PRICE};
    }

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function getSubscriptions(): Collection
    {
        return $this->{self::RELATION_SUBSCRIPTIONS};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getStripeId(): string
    {
        return $this->{self::PROPERTY_STRIPE_ID};
    }

    public function setStripeId(string $value): self
    {
        $this->{self::PROPERTY_STRIPE_ID} = $value;

        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;

        return $this;
    }

    public function getLimit(): ?int
    {
        return $this->{self::PROPERTY_LIMIT};
    }

    public function setLimit(?int $value): self
    {
        $this->{self::PROPERTY_LIMIT} = $value;

        return $this;
    }

    public function getIsHidden(): bool
    {
        return $this->{self::PROPERTY_IS_HIDDEN};
    }

    public function setIsHidden(bool $value): self
    {
        $this->{self::PROPERTY_IS_HIDDEN} = $value;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->{self::PROPERTY_DESCRIPTION};
    }

    public function setDescription(?string $value): self
    {
        $this->{self::PROPERTY_DESCRIPTION} = $value;

        return $this;
    }

    public function getFeatures(): ?array
    {
        return $this->{self::PROPERTY_FEATURES};
    }

    public function setFeatures(?array $value): self
    {
        $this->{self::PROPERTY_FEATURES} = $value;

        return $this;
    }

    public function getHasTrial(): bool
    {
        return $this->{self::PROPERTY_HAS_TRIAL};
    }

    public function setHasTrial(bool $value): self
    {
        $this->{self::PROPERTY_HAS_TRIAL} = $value;

        return $this;
    }

    public function getWhitelabel(): bool
    {
        return $this->{self::PROPERTY_WHITELABEL};
    }

    public function setWhitelabel(bool $value): self
    {
        $this->{self::PROPERTY_WHITELABEL} = $value;

        return $this;
    }
}
