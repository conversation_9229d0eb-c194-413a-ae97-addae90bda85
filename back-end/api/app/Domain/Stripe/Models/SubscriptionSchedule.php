<?php

namespace App\Domain\Stripe\Models;

use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class SubscriptionSchedule extends Model
{
    use TimestampableMethods;
    use HasFactory;

    public const TABLE_NAME = 'stripe_subscription_schedules';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_SUBSCRIPTION_ID = 'subscription_id';
    public const PROPERTY_PRODUCT_ID = 'product_id';
    public const PROPERTY_PRICE_ID = 'price_id';
    public const PROPERTY_STRIPE_ID = 'stripe_id';
    public const PROPERTY_START_DATE = 'start_date';
    public const PROPERTY_CANCELED_AT = 'canceled_at';
    public const PROPERTY_COMPLETED_AT = 'completed_at';
    public const PROPERTY_RELEASED_AT = 'released_at';

    public const RELATION_SUBSCRIPTION = 'subscription';
    public const RELATION_PRICE = 'price';
    public const RELATION_PRODUCT = 'product';

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_START_DATE => 'date',
        self::PROPERTY_CANCELED_AT => 'date',
        self::PROPERTY_COMPLETED_AT => 'date',
        self::PROPERTY_RELEASED_AT => 'date',
    ];

    protected $guarded = [self::PROPERTY_ID];

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function getSubscription(): Subscription
    {
        return $this->{self::RELATION_SUBSCRIPTION};
    }

    public function price(): BelongsTo
    {
        return $this->belongsTo(Price::class);
    }

    public function getPrice(): ?Price
    {
        return $this->{self::RELATION_PRICE};
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function getProduct(): ?Product
    {
        return $this->{self::RELATION_PRODUCT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getSubscriptionId(): int
    {
        return $this->{self::PROPERTY_SUBSCRIPTION_ID};
    }

    public function setSubscriptionId(int $value): self
    {
        $this->{self::PROPERTY_SUBSCRIPTION_ID} = $value;

        return $this;
    }

    public function getPriceId(): ?int
    {
        return $this->{self::PROPERTY_PRICE_ID};
    }

    public function setPriceId(?int $value): self
    {
        $this->{self::PROPERTY_PRICE_ID} = $value;

        return $this;
    }

    public function getStripeId(): string
    {
        return $this->{self::PROPERTY_STRIPE_ID};
    }

    public function setStripeId(string $value): self
    {
        $this->{self::PROPERTY_STRIPE_ID} = $value;

        return $this;
    }

    public function getStartDate(): ?Carbon
    {
        return $this->{self::PROPERTY_START_DATE};
    }

    public function setStartDate(?Carbon $value): self
    {
        $this->{self::PROPERTY_START_DATE} = $value;

        return $this;
    }

    public function getCanceledAt(): ?Carbon
    {
        return $this->{self::PROPERTY_CANCELED_AT};
    }

    public function setCanceledAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_CANCELED_AT} = $value;

        return $this;
    }

    public function getCompletedAt(): ?Carbon
    {
        return $this->{self::PROPERTY_COMPLETED_AT};
    }

    public function setCompletedAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_COMPLETED_AT} = $value;

        return $this;
    }

    public function getReleasedAt(): ?Carbon
    {
        return $this->{self::PROPERTY_RELEASED_AT};
    }

    public function setReleasedAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_RELEASED_AT} = $value;

        return $this;
    }

    public function getProductId(): ?int
    {
        return $this->{self::PROPERTY_PRODUCT_ID};
    }

    public function setProductId(?int $value): self
    {
        $this->{self::PROPERTY_PRODUCT_ID} = $value;

        return $this;
    }
}
