<?php

namespace App\Domain\Stripe\Models;

use App\Domain\Profile\Models\Account;
use App\Domain\Stripe\Support\Enums\AccountStripeStatus;
use App\Domain\Stripe\Support\Enums\CollectionType;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StripeInformation extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'stripe_information';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_ACCOUNT_ID = 'account_id';
    public const PROPERTY_EMAIL = 'email';
    public const PROPERTY_COLLECTION_TYPE = 'collection_type';
    public const PROPERTY_STATUS = 'status';
    public const PROPERTY_PM_TYPE = 'pm_type';
    public const PROPERTY_PM_LAST_FOUR = 'pm_last_four';
    public const PROPERTY_STREET = 'street';
    public const PROPERTY_HOUSE_NUMBER = 'house_number';
    public const PROPERTY_HOUSE_NUMBER_ADDITION = 'house_number_addition';
    public const PROPERTY_ZIPCODE = 'zipcode';
    public const PROPERTY_CITY = 'city';
    public const PROPERTY_COUNTRY = 'country';

    public const RELATION_ACCOUNT = 'account';

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_COLLECTION_TYPE => CollectionType::class,
        self::PROPERTY_STATUS => AccountStripeStatus::class,
    ];

    protected $guarded = [self::PROPERTY_ID];

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function getAccount(): Account
    {
        return $this->{self::RELATION_ACCOUNT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getAccountId(): string
    {
        return $this->{self::PROPERTY_ACCOUNT_ID};
    }

    public function setAccountId(string $value): self
    {
        $this->{self::PROPERTY_ACCOUNT_ID} = $value;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->{self::PROPERTY_EMAIL};
    }

    public function setEmail(?string $value): self
    {
        $this->{self::PROPERTY_EMAIL} = $value;

        return $this;
    }

    public function getStreet(): ?string
    {
        return $this->{self::PROPERTY_STREET};
    }

    public function setStreet(?string $value): self
    {
        $this->{self::PROPERTY_STREET} = $value;

        return $this;
    }

    public function getHouseNumber(): ?int
    {
        return $this->{self::PROPERTY_HOUSE_NUMBER};
    }

    public function setHouseNumber(?int $value): self
    {
        $this->{self::PROPERTY_HOUSE_NUMBER} = $value;

        return $this;
    }

    public function getHouseNumberAddition(): ?string
    {
        return $this->{self::PROPERTY_HOUSE_NUMBER_ADDITION};
    }

    public function setHouseNumberAddition(?string $value): self
    {
        $this->{self::PROPERTY_HOUSE_NUMBER_ADDITION} = $value;

        return $this;
    }

    public function getZipcode(): ?string
    {
        return $this->{self::PROPERTY_ZIPCODE};
    }

    public function setZipcode(?string $value): self
    {
        $this->{self::PROPERTY_ZIPCODE} = $value;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->{self::PROPERTY_COUNTRY};
    }

    public function setCountry(?string $value): self
    {
        $this->{self::PROPERTY_COUNTRY} = $value;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->{self::PROPERTY_CITY};
    }

    public function setCity(?string $value): self
    {
        $this->{self::PROPERTY_CITY} = $value;

        return $this;
    }

    public function getCollectionType(): CollectionType
    {
        return $this->{self::PROPERTY_COLLECTION_TYPE} ?? CollectionType::AUTOMATICALLY;
    }

    public function setCollectionType(CollectionType $value): self
    {
        $this->{self::PROPERTY_COLLECTION_TYPE} = $value;

        return $this;
    }

    public function getStatus(): AccountStripeStatus
    {
        return $this->{self::PROPERTY_STATUS} ?? AccountStripeStatus::TRAIL;
    }

    public function setStatus(AccountStripeStatus $value): self
    {
        $this->{self::PROPERTY_STATUS} = $value;

        return $this;
    }

    public function getPmType(): ?string
    {
        return $this->{self::PROPERTY_PM_TYPE};
    }

    public function setPmType(?string $value): self
    {
        $this->{self::PROPERTY_PM_TYPE} = $value;

        return $this;
    }

    public function getPmLastFour(): ?int
    {
        return $this->{self::PROPERTY_PM_LAST_FOUR};
    }

    public function setPmLastFour(?int $value): self
    {
        $this->{self::PROPERTY_PM_LAST_FOUR} = $value;

        return $this;
    }
}
