<?php

namespace App\Domain\Stripe\Models;

use App\Domain\Profile\Models\Account;
use App\Domain\Stripe\Support\Enums\InvoiceStatus;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class Invoice extends Model
{
    use TimestampableMethods;
    use HasFactory;

    public const TABLE_NAME = 'stripe_invoices';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_ACCOUNT_ID = 'account_id';
    public const PROPERTY_STRIPE_ID = 'stripe_id';
    public const PROPERTY_NUMBER = 'number';
    public const PROPERTY_STATUS = 'status';
    public const PROPERTY_TOTAL_INCLUDING_VAT = 'total_including_vat';
    public const PROPERTY_TOTAL_EXCLUDING_VAT = 'total_excluding_vat';
    public const PROPERTY_AMOUNT_TOTAL = 'amount_total';
    public const PROPERTY_AMOUNT_REMAINING = 'amount_remaining';
    public const PROPERTY_AMOUNT_PAID = 'amount_paid';
    public const PROPERTY_PERIOD_START = 'period_start';
    public const PROPERTY_PERIOD_END = 'period_end';
    public const PROPERTY_DUE_DATE = 'due_date';
    public const PROPERTY_INVOICED_DATE = 'invoiced_date';
    public const PROPERTY_PAID_AT = 'paid_at';
    public const PROPERTY_CREATED_AT = 'created_at';
    public const PROPERTY_UPDATED_AT = 'updated_at';

    public const RELATION_ACCOUNT = 'account';

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_STATUS => InvoiceStatus::class,
        self::PROPERTY_PERIOD_START => 'date',
        self::PROPERTY_PERIOD_END => 'date',
        self::PROPERTY_DUE_DATE => 'date',
        self::PROPERTY_PAID_AT => 'date',
        self::PROPERTY_INVOICED_DATE => 'date',
    ];

    protected $guarded = [self::PROPERTY_ID];

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function getAccount(): Account
    {
        return $this->{self::RELATION_ACCOUNT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getAccountId(): string
    {
        return $this->{self::PROPERTY_ACCOUNT_ID};
    }

    public function setAccountId(string $value): self
    {
        $this->{self::PROPERTY_ACCOUNT_ID} = $value;

        return $this;
    }

    public function getStripeId(): string
    {
        return $this->{self::PROPERTY_STRIPE_ID};
    }

    public function setStripeId(string $value): self
    {
        $this->{self::PROPERTY_STRIPE_ID} = $value;

        return $this;
    }

    public function getNumber(): ?string
    {
        return $this->{self::PROPERTY_NUMBER};
    }

    public function setNumber(?string $value): self
    {
        $this->{self::PROPERTY_NUMBER} = $value;

        return $this;
    }

    public function getStatus(): null|InvoiceStatus
    {
        return $this->{self::PROPERTY_STATUS};
    }

    public function setStatus(null|InvoiceStatus $value): self
    {
        $this->{self::PROPERTY_STATUS} = $value;

        return $this;
    }

    public function getTotalIncludingVat(): int
    {
        return $this->{self::PROPERTY_TOTAL_INCLUDING_VAT};
    }

    public function setTotalIncludingVat(int $value): self
    {
        $this->{self::PROPERTY_TOTAL_INCLUDING_VAT} = $value;

        return $this;
    }

    public function getTotalExcludingVat(): int
    {
        return $this->{self::PROPERTY_TOTAL_EXCLUDING_VAT};
    }

    public function setTotalExcludingVat(int $value): self
    {
        $this->{self::PROPERTY_TOTAL_EXCLUDING_VAT} = $value;

        return $this;
    }

    public function getAmountTotal(): int
    {
        return $this->{self::PROPERTY_AMOUNT_TOTAL};
    }

    public function setAmountTotal(int $value): self
    {
        $this->{self::PROPERTY_AMOUNT_TOTAL} = $value;

        return $this;
    }

    public function getAmountRemaining(): null|int
    {
        return $this->{self::PROPERTY_AMOUNT_REMAINING};
    }

    public function setAmountRemaining(null|int $value): self
    {
        $this->{self::PROPERTY_AMOUNT_REMAINING} = $value;

        return $this;
    }

    public function getAmountPaid(): null|int
    {
        return $this->{self::PROPERTY_AMOUNT_PAID};
    }

    public function setAmountPaid(null|int $value): self
    {
        $this->{self::PROPERTY_AMOUNT_PAID} = $value;

        return $this;
    }

    public function getPeriodStart(): null|Carbon
    {
        return $this->{self::PROPERTY_PERIOD_START} ? Carbon::parse($this->{self::PROPERTY_PERIOD_START}) : null;
    }

    public function setPeriodStart(null|Carbon $value): self
    {
        $this->{self::PROPERTY_PERIOD_START} = $value;

        return $this;
    }

    public function getPeriodEnd(): null|Carbon
    {
        return $this->{self::PROPERTY_PERIOD_END} ? Carbon::parse($this->{self::PROPERTY_PERIOD_END}) : null;
    }

    public function setPeriodEnd(null|Carbon $value): self
    {
        $this->{self::PROPERTY_PERIOD_END} = $value;

        return $this;
    }

    public function getDueDate(): null|Carbon
    {
        return $this->{self::PROPERTY_DUE_DATE} ? Carbon::parse($this->{self::PROPERTY_DUE_DATE}) : null;
    }

    public function setDueDate(null|Carbon $value): self
    {
        $this->{self::PROPERTY_DUE_DATE} = $value;

        return $this;
    }

    public function getPaidAt(): null|Carbon
    {
        return $this->{self::PROPERTY_PAID_AT} ? Carbon::parse($this->{self::PROPERTY_PAID_AT}) : null;
    }

    public function setPaidAt(null|Carbon $value): self
    {
        $this->{self::PROPERTY_PAID_AT} = $value;

        return $this;
    }

    public function getInvoicedDate(): ?Carbon
    {
        return $this->{self::PROPERTY_INVOICED_DATE} ? Carbon::parse($this->{self::PROPERTY_INVOICED_DATE}) : null;
    }

    public function setInvoicedDate(?Carbon $value): self
    {
        $this->{self::PROPERTY_INVOICED_DATE} = $value;

        return $this;
    }
}
