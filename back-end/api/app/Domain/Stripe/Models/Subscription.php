<?php

namespace App\Domain\Stripe\Models;

use App\Domain\Profile\Models\Account;
use App\Domain\Stripe\Support\Enums\SubscriptionStripeStatus;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Laravel\Cashier\Subscription as CashierSubscription;

class Subscription extends CashierSubscription
{
    use TimestampableMethods;
    use HasFactory;

    public const TABLE_NAME = 'stripe_subscriptions';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_ACCOUNT_ID = 'account_id';
    public const PROPERTY_PRODUCT_ID = 'product_id';
    public const PROPERTY_STRIPE_ID = 'stripe_id';
    public const PROPERTY_STRIPE_STATUS = 'stripe_status';
    public const PROPERTY_STRIPE_PRICE = 'stripe_price';
    public const PROPERTY_QUANTITY = 'quantity';
    public const PROPERTY_TRIAL_ENDS_AT = 'trial_ends_at';
    public const PROPERTY_ENDS_AT = 'ends_at';
    public const PROPERTY_PERIOD_STARTS_AT = 'period_starts_at';
    public const PROPERTY_PERIOD_ENDS_AT = 'period_ends_at';
    public const PROPERTY_TYPE = 'type';

    public const RELATION_ACCOUNT = 'account';
    public const RELATION_PRODUCT = 'product';
    public const RELATION_PRICE = 'price';
    public const RELATION_SCHEDULES = 'schedules';
    public const RELATION_ACTIVE_SCHEDULE = 'activeSchedule';

    protected $table = self::TABLE_NAME;

    protected $with = [self::RELATION_ACTIVE_SCHEDULE, self::RELATION_PRODUCT];

    protected $guarded = [self::PROPERTY_ID];

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function getAccount(): Account
    {
        return $this->{self::RELATION_ACCOUNT};
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function getProduct(): ?Product
    {
        return $this->{self::RELATION_PRODUCT};
    }

    public function price(): BelongsTo
    {
        return $this->belongsTo(Price::class, self::PROPERTY_STRIPE_PRICE, Price::PROPERTY_STRIPE_ID);
    }

    public function getPrice(): ?Price
    {
        return $this->{self::RELATION_PRICE};
    }

    public function schedules(): HasMany
    {
        return $this->hasMany(SubscriptionSchedule::class);
    }

    public function getSchedules(): Collection
    {
        return $this->{self::RELATION_SCHEDULES};
    }

    public function activeSchedule(): HasOne
    {
        return $this->hasOne(SubscriptionSchedule::class)
            ->whereNull(SubscriptionSchedule::PROPERTY_CANCELED_AT)
            ->whereNull(SubscriptionSchedule::PROPERTY_COMPLETED_AT)
            ->whereNull(SubscriptionSchedule::PROPERTY_RELEASED_AT);
    }

    public function getActiveSchedule(): ?SubscriptionSchedule
    {
        return $this->{self::RELATION_ACTIVE_SCHEDULE};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getAccountId(): string
    {
        return $this->{self::PROPERTY_ACCOUNT_ID};
    }

    public function setAccountId(string $value): self
    {
        $this->{self::PROPERTY_ACCOUNT_ID} = $value;

        return $this;
    }

    public function getProductId(): ?int
    {
        return $this->{self::PROPERTY_PRODUCT_ID};
    }

    public function setProductId(?int $value): self
    {
        $this->{self::PROPERTY_PRODUCT_ID} = $value;

        return $this;
    }

    public function getQuantity(): int
    {
        return $this->{self::PROPERTY_QUANTITY};
    }

    public function setQuantity(int $value): self
    {
        $this->{self::PROPERTY_QUANTITY} = $value;

        return $this;
    }

    public function getStripeId(): string
    {
        return $this->{self::PROPERTY_STRIPE_ID};
    }

    public function setStripeId(string $value): self
    {
        $this->{self::PROPERTY_STRIPE_ID} = $value;

        return $this;
    }

    public function getStripeStatus(): ?SubscriptionStripeStatus
    {
        return $this->{self::PROPERTY_STRIPE_STATUS}
            ? SubscriptionStripeStatus::from($this->{self::PROPERTY_STRIPE_STATUS})
            : null;
    }

    public function setStripeStatus(?SubscriptionStripeStatus $value): self
    {
        $this->{self::PROPERTY_STRIPE_STATUS} = $value?->value;

        return $this;
    }

    public function getStripePrice(): string|null
    {
        return $this->{self::PROPERTY_STRIPE_PRICE};
    }

    public function setStripePrice(string|null $value): self
    {
        $this->{self::PROPERTY_STRIPE_PRICE} = $value;

        return $this;
    }

    public function getTrialEndsAt(): ?Carbon
    {
        return $this->{self::PROPERTY_TRIAL_ENDS_AT} ? Carbon::parse($this->{self::PROPERTY_TRIAL_ENDS_AT}) : null;
    }

    public function setTrialEndsAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_TRIAL_ENDS_AT} = $value;

        return $this;
    }

    public function getEndsAt(): ?Carbon
    {
        return $this->{self::PROPERTY_ENDS_AT} ? Carbon::parse($this->{self::PROPERTY_ENDS_AT}) : null;
    }

    public function setEndsAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_ENDS_AT} = $value;

        return $this;
    }

    public function getPeriodStartsAt(): ?Carbon
    {
        return $this->{self::PROPERTY_PERIOD_STARTS_AT}
            ? Carbon::parse($this->{self::PROPERTY_PERIOD_STARTS_AT})
            : null;
    }

    public function setPeriodStartsAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_PERIOD_STARTS_AT} = $value;

        return $this;
    }

    public function getPeriodEndsAt(): ?Carbon
    {
        return $this->{self::PROPERTY_PERIOD_ENDS_AT} ? Carbon::parse($this->{self::PROPERTY_PERIOD_ENDS_AT}) : null;
    }

    public function setPeriodEndsAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_PERIOD_ENDS_AT} = $value;

        return $this;
    }

    public function getType(): string
    {
        return $this->{self::PROPERTY_TYPE};
    }

    public function setType(string $value): self
    {
        $this->{self::PROPERTY_TYPE} = $value;

        return $this;
    }
}
