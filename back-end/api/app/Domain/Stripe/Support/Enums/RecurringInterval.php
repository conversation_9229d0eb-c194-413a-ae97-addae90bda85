<?php

namespace App\Domain\Stripe\Support\Enums;

use App\Domain\Stripe\Models\Price;
use App\Domain\Stripe\Models\Product;
use App\Support\Traits\EnumToArray;

enum RecurringInterval: string
{
    use EnumToArray;

    case MONTH = 'month';
    case YEAR = 'year';
    case WEEK = 'week';
    case DAY = 'day';

    public function getPriceFromProduct(Product $product): ?Price
    {
        return match ($this) {
            self::MONTH => $product->getMonthlyPrice(),
            self::YEAR => $product->getYearlyPrice(),
            default => null,
        };
    }
}
