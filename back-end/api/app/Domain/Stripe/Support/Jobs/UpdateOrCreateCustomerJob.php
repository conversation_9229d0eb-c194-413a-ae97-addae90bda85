<?php

namespace App\Domain\Stripe\Support\Jobs;

use App\Domain\Profile\Models\Account;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class UpdateOrCreateCustomerJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    private string $accountId;

    public function __construct(Account $user)
    {
        $this->accountId = $user->getId();
    }

    public function handle(): void
    {
        /** @var Account $account */
        $account = Account::query()->findOrFail($this->accountId);

        if (!$account->getStripeId()) {
            $account->createAsStripeCustomer();
            return;
        }

        $account->updateStripeCustomer();
    }
}
