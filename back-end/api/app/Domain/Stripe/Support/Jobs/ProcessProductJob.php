<?php

namespace App\Domain\Stripe\Support\Jobs;

use App\Domain\Stripe\Actions\SetDefaultPrice;
use App\Domain\Stripe\Models\Price;
use App\Domain\Stripe\Models\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Stripe\Event;

class ProcessProductJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    public const ARRAY_ID = 'id';
    public const ARRAY_NAME = 'name';
    public const ARRAY_METADATA = 'metadata';
    public const ARRAY_MONTHLY_LIMIT = 'monthly_limit';
    public const ARRAY_HIDDEN = 'hidden';
    public const ARRAY_DEFAULT_PRICE = 'default_price';
    public const ARRAY_FEATURES = 'marketing_features';
    public const ARRAY_DESCRIPTION = 'description';
    public const ARRAY_HAS_TRIAL = 'has_trial';
    public const ARRAY_WHITELABEL = 'whitelabel';

    public function __construct(private readonly string $type, private readonly array $data) {}

    public function handle(): void
    {
        if ($this->type === Event::PRODUCT_DELETED) {
            $this->deleteProduct();
            return;
        }

        $this->updateOrCreateProduct();
    }

    private function updateOrCreateProduct(): void
    {
        $stripeId = Arr::get($this->data, self::ARRAY_ID);

        $product = Product::query()->updateOrCreate(
            [
                Product::PROPERTY_STRIPE_ID => $stripeId,
            ],
            [
                Product::PROPERTY_STRIPE_ID => $stripeId,
                Product::PROPERTY_NAME => Arr::get($this->data, self::ARRAY_NAME),
                Product::PROPERTY_LIMIT => Arr::get(
                    $this->data,
                    implode('.', [self::ARRAY_METADATA, self::ARRAY_MONTHLY_LIMIT]),
                ),
                Product::PROPERTY_IS_HIDDEN => Arr::get(
                    $this->data,
                    implode('.', [self::ARRAY_METADATA, self::ARRAY_HIDDEN]),
                    false,
                ),
                Product::PROPERTY_DESCRIPTION => Arr::get($this->data, self::ARRAY_DESCRIPTION),
                Product::PROPERTY_FEATURES => array_map(function ($item) {
                    return Arr::get($item, self::ARRAY_NAME);
                }, Arr::get($this->data, self::ARRAY_FEATURES, [])),
                Product::PROPERTY_HAS_TRIAL => filter_var(
                    Arr::get($this->data, implode('.', [self::ARRAY_METADATA, self::ARRAY_HAS_TRIAL]), false),
                    FILTER_VALIDATE_BOOLEAN,
                ),
                Product::PROPERTY_WHITELABEL => filter_var(
                    Arr::get($this->data, implode('.', [self::ARRAY_METADATA, self::ARRAY_WHITELABEL]), false),
                    FILTER_VALIDATE_BOOLEAN,
                ),
            ],
        );

        $defaultPriceId = Arr::get($this->data, self::ARRAY_DEFAULT_PRICE);

        if (!$defaultPriceId) {
            return;
        }

        app(SetDefaultPrice::class)->execute($product, $defaultPriceId, Price::PROPERTY_STRIPE_ID);
    }

    private function deleteProduct(): void
    {
        Product::query()
            ->where(Product::PROPERTY_STRIPE_ID, Arr::get($this->data, self::ARRAY_ID))
            ->delete();
    }
}
