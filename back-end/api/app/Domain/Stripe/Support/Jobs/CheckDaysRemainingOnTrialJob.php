<?php

namespace App\Domain\Stripe\Support\Jobs;

use App\Domain\Slack\Messages\AmountOfDaysRemainingOnTrialMessage;
use App\Domain\Stripe\Models\Subscription;
use App\Domain\Stripe\Support\Enums\SubscriptionStripeStatus;
use App\Support\SlackNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class CheckDaysRemainingOnTrialJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    public const REMAINING_DAYS = 7;

    public function handle(): void
    {
        Subscription::query()
            ->where(Subscription::PROPERTY_STRIPE_STATUS, SubscriptionStripeStatus::TRIALING)
            ->whereDate(Subscription::PROPERTY_TRIAL_ENDS_AT, now()->addDays(self::REMAINING_DAYS))
            ->each(function (Subscription $subscription) {
                //                SlackNotification::send(new AmountOfDaysRemainingOnTrialMessage($subscription));
            });
    }
}
