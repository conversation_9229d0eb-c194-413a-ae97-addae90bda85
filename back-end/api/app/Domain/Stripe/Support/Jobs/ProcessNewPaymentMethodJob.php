<?php

namespace App\Domain\Stripe\Support\Jobs;

use App\Domain\Profile\Models\Account;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Lara<PERSON>\Cashier\PaymentMethod;

class ProcessNewPaymentMethodJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    public const ARRAY_ID = 'id';
    public const ARRAY_CUSTOMER = 'customer';

    public function __construct(private readonly array $data = []) {}

    public function handle(): void
    {
        /** @var Account $account */
        $account = Account::query()
            ->where(Account::PROPERTY_STRIPE_ID, Arr::get($this->data, self::ARRAY_CUSTOMER))
            ->first();

        if (!$account) {
            return;
        }

        $account->paymentMethods()->each(function (PaymentMethod $method) {
            if ($method->id === Arr::get($this->data, self::ARRAY_ID)) {
                return;
            }

            $method->delete();
        });

        $account->updateDefaultPaymentMethod(Arr::get($this->data, self::ARRAY_ID));
    }
}
