<?php

namespace App\Domain\Stripe\Support\Jobs;

use App\Domain\Profile\Models\Account;
use App\Domain\Stripe\Models\Product;
use App\Domain\Stripe\Models\Subscription;
use App\Domain\Stripe\Support\Enums\SubscriptionStripeStatus;
use App\Support\SlackNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class ProcessSubscriptionJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    public const ARRAY_ID = 'id';
    public const ARRAY_CUSTOMER = 'customer';
    public const ARRAY_STATUS = 'status';
    public const ARRAY_CANCEL_AT = 'cancel_at';
    public const ARRAY_CANCELED_AT = 'canceled_at';
    public const ARRAY_TRAIL_END = 'trial_end';
    public const ARRAY_PLAN = 'plan';
    public const ARRAY_PRODUCT = 'product';
    public const ARRAY_CURRENT_PERIOD_START = 'current_period_start';
    public const ARRAY_CURRENT_PERIOD_END = 'current_period_end';
    public const ARRAY_QUANTITY = 'quantity';
    public const ARRAY_ITEMS = 'items';
    public const ARRAY_DATA = 'data';

    public function __construct(private readonly array $data) {}

    public function handle(): void
    {
        $user = $this->getAccount();
        $product = $this->getProduct();
        $subscription = $this->getSubscription();

        if (!$user || !$product) {
            return;
        }

        $cancelAt = Arr::get($this->data, self::ARRAY_CANCEL_AT) ?? Arr::get($this->data, self::ARRAY_CANCELED_AT);
        $trailEnd = Arr::get($this->data, self::ARRAY_TRAIL_END);
        $periodStart = Arr::get(
            $this->data,
            implode('.', [self::ARRAY_ITEMS, self::ARRAY_DATA, 0, self::ARRAY_CURRENT_PERIOD_START]),
        );
        $periodEnd = Arr::get(
            $this->data,
            implode('.', [self::ARRAY_ITEMS, self::ARRAY_DATA, 0, self::ARRAY_CURRENT_PERIOD_END]),
        );
        $status = SubscriptionStripeStatus::from(Arr::get($this->data, self::ARRAY_STATUS));

        $processedSubscription = Subscription::query()->updateOrCreate(
            [
                Subscription::PROPERTY_ID => $subscription?->getId(),
            ],
            [
                Subscription::PROPERTY_STRIPE_ID => Arr::get($this->data, self::ARRAY_ID),
                Subscription::PROPERTY_ACCOUNT_ID => $user->getId(),
                Subscription::PROPERTY_PRODUCT_ID => $product->getId(),
                Subscription::PROPERTY_STRIPE_STATUS => $status,
                Subscription::PROPERTY_ENDS_AT => $cancelAt ? Carbon::parse($cancelAt) : null,
                Subscription::PROPERTY_TRIAL_ENDS_AT => $trailEnd ? Carbon::parse($trailEnd) : null,
                Subscription::PROPERTY_QUANTITY => Arr::get($this->data, self::ARRAY_QUANTITY),
                Subscription::PROPERTY_PERIOD_STARTS_AT => $periodStart ? Carbon::parse($periodStart) : null,
                Subscription::PROPERTY_PERIOD_ENDS_AT => $periodEnd ? Carbon::parse($periodEnd) : null,
                Subscription::PROPERTY_STRIPE_PRICE => Arr::get(
                    $this->data,
                    sprintf('%s.%s', self::ARRAY_PLAN, self::ARRAY_ID),
                ),
                Subscription::PROPERTY_TYPE => 'default',
            ],
        );

        if (
            (!$subscription || $subscription->getStripeStatus() === SubscriptionStripeStatus::TRIALING) &&
            $status === SubscriptionStripeStatus::ACTIVE &&
            $subscription?->getStripeStatus() !== SubscriptionStripeStatus::ACTIVE
        ) {
            //            SlackNotification::send(new PaidSubscriptionMessage($processedSubscription));
        }

        if (
            $subscription &&
            $subscription->getStripeStatus() === SubscriptionStripeStatus::TRIALING &&
            $status !== SubscriptionStripeStatus::TRIALING &&
            $status !== SubscriptionStripeStatus::ACTIVE
        ) {
            //            SlackNotification::send(new CanceledSubscriptionMessage($processedSubscription));
        }

        if ($status === SubscriptionStripeStatus::CANCELED) {
            //            SlackNotification::send(new CanceledSubscriptionMessage($processedSubscription));
        }
    }

    private function getAccount(): ?Account
    {
        return Account::query()
            ->where(Account::PROPERTY_STRIPE_ID, Arr::get($this->data, self::ARRAY_CUSTOMER))
            ->first();
    }

    private function getProduct(): ?Product
    {
        return Product::query()
            ->where(
                Product::PROPERTY_STRIPE_ID,
                Arr::get($this->data, sprintf('%s.%s', self::ARRAY_PLAN, self::ARRAY_PRODUCT)),
            )
            ->first();
    }

    private function getSubscription(): ?Subscription
    {
        return Subscription::query()
            ->where(Subscription::PROPERTY_STRIPE_ID, Arr::get($this->data, self::ARRAY_ID))
            ->first();
    }
}
