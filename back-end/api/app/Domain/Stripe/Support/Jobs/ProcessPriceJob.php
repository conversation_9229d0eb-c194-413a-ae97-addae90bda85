<?php

namespace App\Domain\Stripe\Support\Jobs;

use App\Domain\Stripe\Models\Price;
use App\Domain\Stripe\Models\Product;
use App\Domain\Stripe\Support\Enums\PriceType;
use App\Domain\Stripe\Support\Enums\RecurringInterval;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Stripe\Event;

class ProcessPriceJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    public const ARRAY_ID = 'id';
    public const ARRAY_PRODUCT = 'product';
    public const ARRAY_UNIT_AMOUNT = 'unit_amount';
    public const ARRAY_RECURRING = 'recurring';
    public const ARRAY_INTERVAL = 'interval';
    public const ARRAY_TYPE = 'type';
    public const ARRAY_INTERVAL_COUNT = 'interval_count';
    public const ARRAY_ACTIVE = 'active';
    public const ARRAY_CURRENCY = 'currency';

    public function __construct(private readonly string $type, private readonly array $data = []) {}

    public function handle(): void
    {
        $stripeId = Arr::get($this->data, self::ARRAY_ID);
        $productId = Arr::get($this->data, self::ARRAY_PRODUCT);

        if ($this->type === Event::PRICE_DELETED) {
            $this->deletePrice($stripeId);
            return;
        }

        $product = Product::query()->where(Product::PROPERTY_STRIPE_ID, $productId)->first();

        if (!$product) {
            return;
        }

        $this->processPrice($stripeId, $product->getId());
    }

    private function processPrice(string $stripeId, string $productId): Price
    {
        $interval = Arr::get($this->data, self::ARRAY_RECURRING . '.' . self::ARRAY_INTERVAL);

        return Price::query()->updateOrCreate(
            [
                Price::PROPERTY_STRIPE_ID => $stripeId,
            ],
            [
                Price::PROPERTY_PRODUCT_ID => $productId,
                Price::PROPERTY_PRICE => Arr::get($this->data, self::ARRAY_UNIT_AMOUNT),
                Price::PROPERTY_TYPE => PriceType::from(Arr::get($this->data, self::ARRAY_TYPE)),
                Price::PROPERTY_RECURRING_INTERVAL => $interval ? RecurringInterval::from($interval) : null,
                Price::PROPERTY_RECURRING_INTERVAL_COUNT => Arr::get(
                    $this->data,
                    self::ARRAY_RECURRING . '.' . self::ARRAY_INTERVAL_COUNT,
                ),
                Price::PROPERTY_IS_HIDDEN => !Arr::get($this->data, self::ARRAY_ACTIVE, true),
                Price::PROPERTY_CURRENCY => Arr::get($this->data, self::ARRAY_CURRENCY),
            ],
        );
    }

    private function deletePrice(string $priceId): void
    {
        Price::query()->where(Price::PROPERTY_STRIPE_ID, $priceId)->delete();
    }
}
