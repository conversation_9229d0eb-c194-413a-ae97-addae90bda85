<?php

namespace App\Domain\Stripe\Support\Jobs;

use App\Domain\Stripe\Models\SubscriptionSchedule;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class ProcessSubscriptionScheduleJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable;

    public const ARRAY_CANCELED_AT = 'canceled_at';
    public const ARRAY_COMPLETED_AT = 'completed_at';
    public const ARRAY_RELEASED_AT = 'released_at';
    public const ARRAY_PHASES = 'phases';
    public const ARRAY_ID = 'id';
    public const ARRAY_SUBSCRIPTION = 'subscription';
    public const ARRAY_ITEMS = 'items';
    public const ARRAY_PRICE = 'price';
    public const ARRAY_START_DATE = 'start_date';
    public const ARRAY_RELEASED_SUBSCRIPTION = 'released_subscription';

    public function __construct(private readonly array $data) {}

    public function handle(): void
    {
        $canceledAt = Arr::get($this->data, self::ARRAY_CANCELED_AT);
        $completedAt = Arr::get($this->data, self::ARRAY_COMPLETED_AT);
        $releasedAt = Arr::get($this->data, self::ARRAY_RELEASED_AT);
        $id = Arr::get($this->data, self::ARRAY_ID);

        $data = [
            SubscriptionSchedule::PROPERTY_CANCELED_AT => $canceledAt ? Carbon::parse($canceledAt) : null,
            SubscriptionSchedule::PROPERTY_COMPLETED_AT => $completedAt ? Carbon::parse($completedAt) : null,
            SubscriptionSchedule::PROPERTY_RELEASED_AT => $releasedAt ? Carbon::parse($releasedAt) : null,
            SubscriptionSchedule::PROPERTY_STRIPE_ID => $id,
        ];

        SubscriptionSchedule::query()->updateOrCreate(
            [
                SubscriptionSchedule::PROPERTY_STRIPE_ID => $id,
            ],
            $data,
        );
    }
}
