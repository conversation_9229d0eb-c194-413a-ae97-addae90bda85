<?php

namespace App\Domain\Stripe\Support\Jobs;

use App\Domain\Profile\Models\Account;
use App\Domain\Stripe\Models\Invoice;
use App\Domain\Stripe\Support\Enums\InvoiceStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Stripe\Event;

class ProcessInvoiceJob implements ShouldQueue
{
    use Queueable, Dispatchable, InteractsWithQueue;

    public const ARRAY_ID = 'id';
    public const ARRAY_CUSTOMER = 'customer';
    public const ARRAY_STATUS = 'status';
    public const ARRAY_AMOUNT_DUE = 'amount_due';
    public const ARRAY_AMOUNT_PAID = 'amount_paid';
    public const ARRAY_AMOUNT_REMAINING = 'amount_remaining';
    public const ARRAY_PERIOD_START = 'period_start';
    public const ARRAY_PERIOD_END = 'period_end';
    public const ARRAY_DUE_DATE = 'due_date';
    public const ARRAY_TOTAL = 'total';
    public const ARRAY_TOTAL_EXCLUDING_TAX = 'total_excluding_tax';
    public const ARRAY_NUMBER = 'number';
    public const ARRAY_STATUS_TRANSITIONS = 'status_transitions';
    public const ARRAY_FINALIZED_AT = 'finalized_at';
    public const ARRAY_SUBSCRIPTION = 'subscription';

    public function __construct(private readonly string $type, private readonly array $data = []) {}

    public function handle(): void
    {
        $stripeId = Arr::get($this->data, self::ARRAY_ID);
        $customerId = Arr::get($this->data, self::ARRAY_CUSTOMER);

        if (!$stripeId || !$customerId) {
            return;
        }

        if ($this->type === Event::INVOICE_DELETED) {
            $this->deleteInvoice($stripeId);
            return;
        }

        /** @var Account $account */
        $account = Account::query()->where(Account::PROPERTY_STRIPE_ID, $customerId)->first();

        if (!$account) {
            return;
        }

        $periodStart = Arr::get($this->data, self::ARRAY_PERIOD_START);
        $periodEnd = Arr::get($this->data, self::ARRAY_PERIOD_END);
        $periodEnd = $periodEnd ? Carbon::parse($periodEnd) : null;
        $dueDate = Arr::get($this->data, self::ARRAY_DUE_DATE);
        $invoicedDate = Arr::get($this->data, self::ARRAY_STATUS_TRANSITIONS . '.' . self::ARRAY_FINALIZED_AT);
        $subscriptionId = Arr::get($this->data, self::ARRAY_SUBSCRIPTION);

        $data = [
            Invoice::PROPERTY_ACCOUNT_ID => $account->getId(),
            Invoice::PROPERTY_STATUS => InvoiceStatus::from(Arr::get($this->data, self::ARRAY_STATUS)),
            Invoice::PROPERTY_AMOUNT_TOTAL => Arr::get($this->data, self::ARRAY_AMOUNT_DUE),
            Invoice::PROPERTY_AMOUNT_PAID => Arr::get($this->data, self::ARRAY_AMOUNT_PAID),
            Invoice::PROPERTY_AMOUNT_REMAINING => Arr::get($this->data, self::ARRAY_AMOUNT_REMAINING),
            Invoice::PROPERTY_PERIOD_START => $periodStart ? Carbon::parse($periodStart) : null,
            Invoice::PROPERTY_PERIOD_END => $periodEnd,
            Invoice::PROPERTY_DUE_DATE => $dueDate ? Carbon::parse($dueDate) : null,
            Invoice::PROPERTY_INVOICED_DATE => $dueDate ? Carbon::parse($invoicedDate) : null,
            Invoice::PROPERTY_TOTAL_INCLUDING_VAT => Arr::get($this->data, self::ARRAY_TOTAL),
            Invoice::PROPERTY_TOTAL_EXCLUDING_VAT => Arr::get($this->data, self::ARRAY_TOTAL_EXCLUDING_TAX),
            Invoice::PROPERTY_NUMBER => Arr::get($this->data, self::ARRAY_NUMBER),
        ];

        if ($this->type === Event::INVOICE_PAID) {
            $data = array_merge($data, [Invoice::PROPERTY_PAID_AT => now()]);
        }

        Invoice::query()->updateOrCreate(
            [
                Invoice::PROPERTY_STRIPE_ID => $stripeId,
            ],
            $data,
        );
    }

    private function deleteInvoice(string $stripeId): void
    {
        Invoice::query()->where(Invoice::PROPERTY_STRIPE_ID, $stripeId)->delete();
    }
}
