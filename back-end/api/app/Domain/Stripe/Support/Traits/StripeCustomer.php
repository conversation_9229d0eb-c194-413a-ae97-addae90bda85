<?php

namespace App\Domain\Stripe\Support\Traits;

use App\Domain\Profile\Models\User;
use <PERSON><PERSON>\Cashier\Billable;
use <PERSON><PERSON>\Cashier\PaymentMethod;
use Stripe\BankAccount as StripeBankAccount;
use Stripe\Card as StripeCard;

trait StripeCustomer
{
    use Billable;

    public function stripeEmail(): ?string
    {
        return $this->getStripeInformation()?->getEmail() ??
            $this->users()->orderBy(User::PROPERTY_CREATED_AT, 'ASC')->first()?->getEmail();
    }

    public function stripeAddress(): array
    {
        return [
            'city' => $this->getStripeInformation()->getCity(),
            'country' => null,
            'line1' =>
                $this->getStripeInformation()->getStreet() . ' ' . $this->getStripeInformation()->getHouseNumber(),
            'line2' => $this->getStripeInformation()->getHouseNumberAddition(),
            'postal_code' => $this->getStripeInformation()->getZipcode(),
        ];
    }

    public function updateStripeCustomer(array $options = []): \Stripe\Customer
    {
        return $this->stripe()->customers->update(
            $this->stripe_id,
            array_merge(
                [
                    'address' => $this->stripeAddress(),
                    'email' => $this->stripeEmail(),
                    'name' => $this->stripeName(),
                ],
                $options,
            ),
        );
    }

    public function hasDefaultPaymentMethod(): bool
    {
        return (bool) $this->getStripeInformation()?->getPmType();
    }

    protected function fillPaymentMethodDetails($paymentMethod): self
    {
        if ($paymentMethod->type === 'card') {
            $this->getStripeInformation()
                ->setPmType($paymentMethod->card->brand)
                ->setPmLastFour($paymentMethod->card->last4)
                ->save();

            return $this;
        }

        $this->getStripeInformation()
            ->setPmType($type = $paymentMethod->type)
            ->setPmLastFour($paymentMethod?->$type->last4 ?? null)
            ->save();

        return $this;
    }

    protected function fillSourceDetails($source): self
    {
        if ($source instanceof StripeCard) {
            $this->getStripeInformation()->setPmType($source->brand)->setPmLastFour($source->last4)->save();
        }

        if ($source instanceof StripeBankAccount) {
            $this->getStripeInformation()->setPmType('Bank Account')->setPmLastFour($source->last4)->save();
        }

        return $this;
    }

    public function deletePaymentMethod($paymentMethod): void
    {
        $this->assertCustomerExists();

        $stripePaymentMethod = $this->resolveStripePaymentMethod($paymentMethod);

        if ($stripePaymentMethod->customer !== $this->stripe_id) {
            return;
        }

        $customer = $this->asStripeCustomer();

        $defaultPaymentMethod = $customer->invoice_settings->default_payment_method;

        $stripePaymentMethod->detach();

        // If the payment method was the default payment method, we'll remove it manually...
        if ($stripePaymentMethod->id === $defaultPaymentMethod) {
            $this->getStripeInformation()->setPmType(null)->setPmLastFour(null)->save();
        }
    }

    public function updateDefaultPaymentMethodFromStripe(): self
    {
        $defaultPaymentMethod = $this->defaultPaymentMethod();

        if ($defaultPaymentMethod) {
            if ($defaultPaymentMethod instanceof PaymentMethod) {
                $this->fillPaymentMethodDetails($defaultPaymentMethod->asStripePaymentMethod())->save();
            } else {
                $this->fillSourceDetails($defaultPaymentMethod)->save();
            }
        } else {
            $this->getStripeInformation()->setPmType(null)->setPmLastFour(null)->save();
        }

        return $this;
    }
}
