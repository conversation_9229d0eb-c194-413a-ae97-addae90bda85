<?php

namespace App\Domain\Stripe\Support\Observers;

use App\Domain\Slack\Messages\SubscriptionProductChangeMessage;
use App\Domain\Stripe\Models\Product;
use App\Domain\Stripe\Models\Subscription;
use App\Domain\Stripe\Support\Enums\SubscriptionStripeStatus;
use App\Support\SlackNotification;

class SubscriptionProductChangeObserver
{
    public function updating(Subscription $subscription): void
    {
        if (
            !$subscription->isDirty(Subscription::PROPERTY_PRODUCT_ID) ||
            $subscription->getStripeStatus() === SubscriptionStripeStatus::TRIALING
        ) {
            return;
        }

        SlackNotification::send(new SubscriptionProductChangeMessage($subscription, $this->isDowngrade($subscription)));
    }

    private function isDowngrade(Subscription $subscription): bool
    {
        /** @var Product $newProduct */
        $newProduct = Product::query()->with(Product::RELATION_MONTHLY_PRICE)->find($subscription->getProductId());

        /** @var Product $oldProduct */
        $oldProduct = Product::query()
            ->with(Product::RELATION_MONTHLY_PRICE)
            ->find($subscription->getOriginal(Subscription::PROPERTY_PRODUCT_ID));

        return ($newProduct?->getMonthlyPrice()?->getPrice() ?? 0) <
            ($oldProduct?->getMonthlyPrice()?->getPrice() ?? 0);
    }
}
