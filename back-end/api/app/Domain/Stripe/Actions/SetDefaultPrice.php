<?php

namespace App\Domain\Stripe\Actions;

use App\Domain\Stripe\Models\Price;
use App\Domain\Stripe\Models\Product;

class SetDefaultPrice
{
    public function execute(Product $product, string $id, string $key = Price::PROPERTY_ID): void
    {
        $price = $this->getPrice($id, $key);

        if (!$price) {
            return;
        }

        $price->setDefault(true);
        $price->save();

        Price::query()
            ->where($key, '!=', $id)
            ->where(Price::PROPERTY_PRODUCT_ID, $product->getId())
            ->update([
                Price::PROPERTY_DEFAULT => false,
            ]);
    }

    private function getPrice(string $id, string $key): ?Price
    {
        /** @var Price|null $price */
        $price = Price::query()->where($key, $id)->first();

        return $price;
    }
}
