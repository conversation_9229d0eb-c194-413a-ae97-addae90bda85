<?php

namespace App\Domain\Stripe\Actions;

use App\Domain\Stripe\Models\Price;
use App\Domain\Stripe\Models\Subscription;
use App\Domain\Stripe\Models\SubscriptionSchedule;
use Laravel\Cashier\Cashier;

class ScheduleSubscriptionChange
{
    public function execute(Subscription $subscription, Price $price): ?SubscriptionSchedule
    {
        $activeSchedule = $subscription->getActiveSchedule();

        $service = Cashier::stripe()->subscriptionSchedules;

        $scheduleStripeId = $activeSchedule?->getStripeId();

        if (!$scheduleStripeId) {
            $scheduleStripeId = $service->create(['from_subscription' => $subscription->getStripeId()])->id;
        }

        $schedule = $service->update($scheduleStripeId, [
            'end_behavior' => 'release',
            'phases' => [
                [
                    'items' => [
                        [
                            'price' => $subscription->getStripePrice(),
                        ],
                    ],
                    'start_date' => $subscription->getPeriodStartsAt()?->unix(),
                    'end_date' => $subscription->getPeriodEndsAt()?->unix(),
                ],
                [
                    'items' => [
                        [
                            'price' => $price->getStripeId(),
                        ],
                    ],
                    'iterations' => 1,
                ],
            ],
        ]);

        return SubscriptionSchedule::query()->updateOrCreate(
            [
                SubscriptionSchedule::PROPERTY_STRIPE_ID => $schedule->id,
            ],
            [
                SubscriptionSchedule::PROPERTY_SUBSCRIPTION_ID => $subscription->getId(),
                SubscriptionSchedule::PROPERTY_PRICE_ID => $price->getId(),
                SubscriptionSchedule::PROPERTY_PRODUCT_ID => $price->getProductId(),
                SubscriptionSchedule::PROPERTY_STRIPE_ID => $schedule->id,
                SubscriptionSchedule::PROPERTY_START_DATE => $subscription->getPeriodEndsAt(),
            ],
        );
    }
}
