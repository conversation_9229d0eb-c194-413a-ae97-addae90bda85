<?php

namespace App\Domain\Stripe\Actions;

use App\Domain\Profile\Models\Account;
use App\Domain\Stripe\Models\Product;
use App\Domain\Stripe\Models\Subscription;

class CreateTrialSubscription
{
    public function execute(Account $account, int $days = 30): ?Subscription
    {
        $product = $this->getProduct();

        if (!$product) {
            return null;
        }
        return $account
            ->newSubscription('default', $product->getMonthlyPrice()->getStripeId())
            ->trialDays($days)
            ->create(
                subscriptionOptions: [
                    'trial_settings' => [
                        'end_behavior' => [
                            'missing_payment_method' => 'cancel',
                        ],
                    ],
                ],
            );
    }

    private function getProduct(): ?Product
    {
        return Product::query()
            ->with(Product::RELATION_MONTHLY_PRICE)
            ->where(Product::PROPERTY_HAS_TRIAL, true)
            ->whereHas(Product::RELATION_MONTHLY_PRICE)
            ->first();
    }
}
