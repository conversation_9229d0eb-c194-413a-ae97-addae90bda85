<?php

namespace App\Domain\Stripe\Actions;

use App\Domain\Stripe\Support\Jobs\ProcessInvoiceJob;
use App\Domain\Stripe\Support\Jobs\ProcessNewPaymentMethodJob;
use App\Domain\Stripe\Support\Jobs\ProcessPriceJob;
use App\Domain\Stripe\Support\Jobs\ProcessProductJob;
use App\Domain\Stripe\Support\Jobs\ProcessSubscriptionJob;
use App\Domain\Stripe\Support\Jobs\ProcessSubscriptionScheduleJob;
use Illuminate\Support\Arr;
use Stripe\Event;

class HandleStripeEvent
{
    private Event $event;

    public const ARRAY_OBJECT = 'object';

    public function execute(Event $event): void
    {
        $this->event = $event;

        $jobs = [];

        switch ($event->type) {
            case Event::PRODUCT_CREATED:
            case Event::PRODUCT_UPDATED:
            case Event::PRODUCT_DELETED:
                $jobs[] = new ProcessProductJob($event->type, $this->getData());
                break;
            case Event::PRICE_CREATED:
            case Event::PRICE_UPDATED:
            case Event::PRICE_DELETED:
                $jobs[] = new ProcessPriceJob($event->type, $this->getData());
                break;
            case Event::CUSTOMER_SUBSCRIPTION_CREATED:
            case Event::CUSTOMER_SUBSCRIPTION_DELETED:
            case Event::CUSTOMER_SUBSCRIPTION_PAUSED:
            case Event::CUSTOMER_SUBSCRIPTION_PENDING_UPDATE_APPLIED:
            case Event::CUSTOMER_SUBSCRIPTION_PENDING_UPDATE_EXPIRED:
            case Event::CUSTOMER_SUBSCRIPTION_RESUMED:
            case Event::CUSTOMER_SUBSCRIPTION_TRIAL_WILL_END:
            case Event::CUSTOMER_SUBSCRIPTION_UPDATED:
                $jobs[] = new ProcessSubscriptionJob($this->getData());
                break;
            case Event::PAYMENT_METHOD_ATTACHED:
                $jobs[] = new ProcessNewPaymentMethodJob($this->getData());
                break;
            case Event::SUBSCRIPTION_SCHEDULE_ABORTED:
            case Event::SUBSCRIPTION_SCHEDULE_CANCELED:
            case Event::SUBSCRIPTION_SCHEDULE_COMPLETED:
            case Event::SUBSCRIPTION_SCHEDULE_CREATED:
            case Event::SUBSCRIPTION_SCHEDULE_EXPIRING:
            case Event::SUBSCRIPTION_SCHEDULE_RELEASED:
            case Event::SUBSCRIPTION_SCHEDULE_UPDATED:
                $jobs[] = new ProcessSubscriptionScheduleJob($this->getData());
                break;
            case Event::INVOICE_VOIDED:
            case Event::INVOICE_UPDATED:
            case Event::INVOICE_UPCOMING:
            case Event::INVOICE_SENT:
            case Event::INVOICE_PAYMENT_SUCCEEDED:
            case Event::INVOICE_PAYMENT_FAILED:
            case Event::INVOICE_PAYMENT_ACTION_REQUIRED:
            case Event::INVOICE_MARKED_UNCOLLECTIBLE:
            case Event::INVOICE_FINALIZED:
            case Event::INVOICE_FINALIZATION_FAILED:
            case Event::INVOICE_DELETED:
            case Event::INVOICE_CREATED:
            case Event::INVOICE_PAID:
                $jobs[] = new ProcessInvoiceJob($event->type, $this->getData());
                break;
        }

        foreach ($jobs as $job) {
            dispatch($job);
        }
    }

    private function getData(): array
    {
        return Arr::get($this->event->data->toArray(), self::ARRAY_OBJECT, []);
    }
}
