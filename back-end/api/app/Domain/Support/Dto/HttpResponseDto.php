<?php

namespace App\Domain\Support\Dto;

class HttpResponseDto
{
    private bool $successful = false;
    private ?int $status = null;
    private array|null|\Exception $body = null;

    public function isSuccessful(): bool
    {
        return $this->successful;
    }

    public function setSuccessful(bool $successful): HttpResponseDto
    {
        $this->successful = $successful;
        return $this;
    }

    public function getStatus(): ?int
    {
        return $this->status;
    }

    public function setStatus(?int $status): HttpResponseDto
    {
        $this->status = $status;
        return $this;
    }

    public function getBody(): \Exception|array|null
    {
        return $this->body;
    }

    public function setBody(\Exception|array|null $body): HttpResponseDto
    {
        $this->body = $body;
        return $this;
    }
}
