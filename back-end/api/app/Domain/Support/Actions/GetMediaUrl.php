<?php

namespace App\Domain\Support\Actions;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GetMediaUrl
{
    public function execute(?string $path): ?string
    {
        if (!$path) {
            return null;
        }

        if (Str::contains($path, 'https://')) {
            return $path;
        }

        return Storage::temporaryUrl($path, now()->addMinutes(5));
    }
}
