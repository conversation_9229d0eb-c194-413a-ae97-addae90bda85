<?php

namespace App\Domain\Support\Actions;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class StoreMedia
{
    public function execute(UploadedFile|string $file, string $path, ?string $extension = null): string
    {
        $name = sprintf('%s.%s', Str::uuid(), $extension ?? $file->guessExtension());

        if ($file instanceof UploadedFile) {
            Storage::putFileAs($path, $file, $name);
        } else {
            // Handle raw string data
            Storage::put("{$path}/{$name}", $file);
        }

        return sprintf('%s/%s', $path, $name);
    }
}
