<?php

namespace App\Domain\Dashboard\Dto\Widget;

use App\Domain\Dashboard\Support\Enums\WidgetAccuracy;

class ChartDto
{
    private array $series;
    private WidgetAccuracy $accuracy;
    private array $anomalies = [];
    private ?string $name = null;

    /**
     * @return ChartSeriesDto[]
     */
    public function getSeries(): array
    {
        return $this->series;
    }

    /**
     * @param ChartSeriesDto[] $series
     * @return $this
     */
    public function setSeries(array $series): ChartDto
    {
        $this->series = $series;
        return $this;
    }

    public function getAccuracy(): WidgetAccuracy
    {
        return $this->accuracy;
    }

    public function setAccuracy(WidgetAccuracy $accuracy): ChartDto
    {
        $this->accuracy = $accuracy;
        return $this;
    }

    /**
     * @return ChartAnomalyDto[]
     */
    public function getAnomalies(): array
    {
        return $this->anomalies;
    }

    /**
     * @param ChartAnomalyDto[] $anomalies
     * @return $this
     */
    public function setAnomalies(array $anomalies): ChartDto
    {
        $this->anomalies = $anomalies;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): ChartDto
    {
        $this->name = $name;
        return $this;
    }
}
