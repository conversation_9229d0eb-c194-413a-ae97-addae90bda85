<?php

namespace App\Domain\Dashboard\Dto\Widget;

class PeriodAnalysisColumnDto
{
    private float|string $value;
    private string $formattedValue;
    private ?string $prefix = null;
    private ?string $suffix = null;
    private ?string $styling = null;

    public function getValue(): float|string
    {
        return $this->value;
    }

    public function setValue(float|string $value): PeriodAnalysisColumnDto
    {
        $this->value = $value;
        return $this;
    }

    public function getFormattedValue(): string
    {
        return $this->formattedValue;
    }

    public function setFormattedValue(string $formattedValue): PeriodAnalysisColumnDto
    {
        $this->formattedValue = $formattedValue;
        return $this;
    }

    public function getPrefix(): ?string
    {
        return $this->prefix;
    }

    public function setPrefix(?string $prefix): PeriodAnalysisColumnDto
    {
        $this->prefix = $prefix;
        return $this;
    }

    public function getSuffix(): ?string
    {
        return $this->suffix;
    }

    public function setSuffix(?string $suffix): PeriodAnalysisColumnDto
    {
        $this->suffix = $suffix;
        return $this;
    }

    public function getStyling(): ?string
    {
        return $this->styling;
    }

    public function setStyling(?string $styling): PeriodAnalysisColumnDto
    {
        $this->styling = $styling;
        return $this;
    }
}
