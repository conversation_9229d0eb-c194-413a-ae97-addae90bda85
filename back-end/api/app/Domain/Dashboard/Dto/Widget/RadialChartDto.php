<?php

namespace App\Domain\Dashboard\Dto\Widget;

class RadialChartDto
{
    private ?string $min = null;
    private ?string $max = null;
    private array $series;
    private ?string $prefix = null;
    private ?string $suffix = null;

    public function getMin(): ?string
    {
        return $this->min;
    }

    public function setMin(?string $min): RadialChartDto
    {
        $this->min = $min;
        return $this;
    }

    public function getMax(): ?string
    {
        return $this->max;
    }

    public function setMax(?string $max): RadialChartDto
    {
        $this->max = $max;
        return $this;
    }

    /**
     * @return RadialChartSeriesDto[]
     */
    public function getSeries(): array
    {
        return $this->series;
    }

    /**
     * @param RadialChartSeriesDto[] $series
     * @return $this
     */
    public function setSeries(array $series): RadialChartDto
    {
        $this->series = $series;
        return $this;
    }

    public function getPrefix(): ?string
    {
        return $this->prefix;
    }

    public function setPrefix(?string $prefix): RadialChartDto
    {
        $this->prefix = $prefix;
        return $this;
    }

    public function getSuffix(): ?string
    {
        return $this->suffix;
    }

    public function setSuffix(?string $suffix): RadialChartDto
    {
        $this->suffix = $suffix;
        return $this;
    }
}
