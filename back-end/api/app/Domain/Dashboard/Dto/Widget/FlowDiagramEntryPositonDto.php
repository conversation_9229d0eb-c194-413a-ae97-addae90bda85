<?php

namespace App\Domain\Dashboard\Dto\Widget;

class FlowDiagramEntryPositonDto
{
    private int $x;
    private int $y;

    public function getX(): int
    {
        return $this->x;
    }

    public function setX(int $x): FlowDiagramEntryPositonDto
    {
        $this->x = $x;
        return $this;
    }

    public function getY(): int
    {
        return $this->y;
    }

    public function setY(int $y): FlowDiagramEntryPositonDto
    {
        $this->y = $y;
        return $this;
    }
}
