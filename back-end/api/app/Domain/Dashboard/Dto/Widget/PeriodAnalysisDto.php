<?php

namespace App\Domain\Dashboard\Dto\Widget;

class PeriodAnalysisDto
{
    private array $headers;
    private array $categories;

    public function getHeaders(): array
    {
        return $this->headers;
    }

    public function setHeaders(array $headers): PeriodAnalysisDto
    {
        $this->headers = $headers;
        return $this;
    }

    /**
     * @return PeriodAnalysisCategoryDto[]
     */
    public function getCategories(): array
    {
        return $this->categories;
    }

    /**
     * @param PeriodAnalysisCategoryDto[] $categories
     * @return $this
     */
    public function setCategories(array $categories): PeriodAnalysisDto
    {
        $this->categories = $categories;
        return $this;
    }
}
