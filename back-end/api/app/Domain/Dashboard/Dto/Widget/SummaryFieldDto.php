<?php

namespace App\Domain\Dashboard\Dto\Widget;

class SummaryFieldDto
{
    private string $title;
    private float $value;
    private string $valueFormatted;
    private ?string $prefix = null;
    private ?string $suffix = null;

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): SummaryFieldDto
    {
        $this->title = $title;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): SummaryFieldDto
    {
        $this->value = $value;
        return $this;
    }

    public function getValueFormatted(): string
    {
        return $this->valueFormatted;
    }

    public function setValueFormatted(string $valueFormatted): SummaryFieldDto
    {
        $this->valueFormatted = $valueFormatted;
        return $this;
    }

    public function getPrefix(): ?string
    {
        return $this->prefix;
    }

    public function setPrefix(?string $prefix): SummaryFieldDto
    {
        $this->prefix = $prefix;
        return $this;
    }

    public function getSuffix(): ?string
    {
        return $this->suffix;
    }

    public function setSuffix(?string $suffix): SummaryFieldDto
    {
        $this->suffix = $suffix;
        return $this;
    }
}
