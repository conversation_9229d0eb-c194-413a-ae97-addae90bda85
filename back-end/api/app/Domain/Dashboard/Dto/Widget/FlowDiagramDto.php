<?php

namespace App\Domain\Dashboard\Dto\Widget;

class FlowDiagramDto
{
    private array $connections = [];
    private array $groups = [];
    private array $entries = [];

    /**
     * @return FlowDiagramConnectionDto[]
     */
    public function getConnections(): array
    {
        return $this->connections;
    }

    /**
     * @param FlowDiagramConnectionDto[] $connections
     * @return $this
     */
    public function setConnections(array $connections): FlowDiagramDto
    {
        $this->connections = $connections;
        return $this;
    }

    /**
     * @return FlowDiagramGroupDto[]
     */
    public function getGroups(): array
    {
        return $this->groups;
    }

    /**
     * @param FlowDiagramGroupDto[] $groups
     * @return $this
     */
    public function setGroups(array $groups): FlowDiagramDto
    {
        $this->groups = $groups;
        return $this;
    }

    /**
     * @return FlowDiagramEntryDto[]
     */
    public function getEntries(): array
    {
        return $this->entries;
    }

    /**
     * @param FlowDiagramEntryDto[] $entries
     * @return $this
     */
    public function setEntries(array $entries): FlowDiagramDto
    {
        $this->entries = $entries;
        return $this;
    }
}
