<?php

namespace App\Domain\Dashboard\Dto\Widget;

class FlowDiagramEntryDto
{
    private float $value;
    private string $valueFormatted;
    private string $inputId;
    private string $outputId;
    private string $name;
    private FlowDiagramEntryPositonDto $position;
    private ?string $groupId = null;
    private ?float $comparisonValue = null;
    private ?string $comparisonFormatted = null;
    private ?string $prefix = null;
    private ?string $suffix = null;

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): FlowDiagramEntryDto
    {
        $this->value = $value;
        return $this;
    }

    public function getValueFormatted(): string
    {
        return $this->valueFormatted;
    }

    public function setValueFormatted(string $valueFormatted): FlowDiagramEntryDto
    {
        $this->valueFormatted = $valueFormatted;
        return $this;
    }

    public function getInputId(): string
    {
        return $this->inputId;
    }

    public function setInputId(string $inputId): FlowDiagramEntryDto
    {
        $this->inputId = $inputId;
        return $this;
    }

    public function getOutputId(): string
    {
        return $this->outputId;
    }

    public function setOutputId(string $outputId): FlowDiagramEntryDto
    {
        $this->outputId = $outputId;
        return $this;
    }

    public function getGroupId(): ?string
    {
        return $this->groupId;
    }

    public function setGroupId(?string $groupId): FlowDiagramEntryDto
    {
        $this->groupId = $groupId;
        return $this;
    }

    public function getComparisonValue(): ?float
    {
        return $this->comparisonValue;
    }

    public function setComparisonValue(?float $comparisonValue): FlowDiagramEntryDto
    {
        $this->comparisonValue = $comparisonValue;
        return $this;
    }

    public function getComparisonFormatted(): ?string
    {
        return $this->comparisonFormatted;
    }

    public function setComparisonFormatted(?string $comparisonFormatted): FlowDiagramEntryDto
    {
        $this->comparisonFormatted = $comparisonFormatted;
        return $this;
    }

    public function getPrefix(): ?string
    {
        return $this->prefix;
    }

    public function setPrefix(?string $prefix): FlowDiagramEntryDto
    {
        $this->prefix = $prefix;
        return $this;
    }

    public function getSuffix(): ?string
    {
        return $this->suffix;
    }

    public function setSuffix(?string $suffix): FlowDiagramEntryDto
    {
        $this->suffix = $suffix;
        return $this;
    }

    public function getPosition(): FlowDiagramEntryPositonDto
    {
        return $this->position;
    }

    public function setPosition(FlowDiagramEntryPositonDto $position): FlowDiagramEntryDto
    {
        $this->position = $position;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): FlowDiagramEntryDto
    {
        $this->name = $name;
        return $this;
    }
}
