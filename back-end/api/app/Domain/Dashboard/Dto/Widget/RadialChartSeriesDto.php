<?php

namespace App\Domain\Dashboard\Dto\Widget;

class RadialChartSeriesDto
{
    private int $percentage;
    private string $formattedValue;
    private ?string $title = null;
    private ?string $prefix = null;
    private ?string $suffix = null;

    public function getPercentage(): int
    {
        return $this->percentage;
    }

    public function setPercentage(int $percentage): RadialChartSeriesDto
    {
        $this->percentage = $percentage;
        return $this;
    }

    public function getFormattedValue(): string
    {
        return $this->formattedValue;
    }

    public function setFormattedValue(string $formattedValue): RadialChartSeriesDto
    {
        $this->formattedValue = $formattedValue;
        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): RadialChartSeriesDto
    {
        $this->title = $title;
        return $this;
    }

    public function getPrefix(): ?string
    {
        return $this->prefix;
    }

    public function setPrefix(?string $prefix): RadialChartSeriesDto
    {
        $this->prefix = $prefix;
        return $this;
    }

    public function getSuffix(): ?string
    {
        return $this->suffix;
    }

    public function setSuffix(?string $suffix): RadialChartSeriesDto
    {
        $this->suffix = $suffix;
        return $this;
    }
}
