<?php

namespace App\Domain\Dashboard\Dto\Widget;

class TableDto
{
    private array $headers;
    private array $rows;

    public function getHeaders(): array
    {
        return $this->headers;
    }

    public function setHeaders(array $headers): TableDto
    {
        $this->headers = $headers;
        return $this;
    }

    public function getRows(): array
    {
        return $this->rows;
    }

    public function setRows(array $rows): TableDto
    {
        $this->rows = $rows;
        return $this;
    }
}
