<?php

namespace App\Domain\Dashboard\Dto\Widget;

class PieChartSeriesDto
{
    private string $label;
    private float $value;
    private string $formattedValue;
    private ?string $color = null;

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): PieChartSeriesDto
    {
        $this->label = $label;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): PieChartSeriesDto
    {
        $this->value = $value;
        return $this;
    }

    public function getFormattedValue(): string
    {
        return $this->formattedValue;
    }

    public function setFormattedValue(string $formattedValue): PieChartSeriesDto
    {
        $this->formattedValue = $formattedValue;
        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): PieChartSeriesDto
    {
        $this->color = $color;
        return $this;
    }
}
