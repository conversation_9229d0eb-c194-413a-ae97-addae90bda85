<?php

namespace App\Domain\Dashboard\Dto\Widget;

class FlowDiagramGroupDto
{
    private string $name;
    private string $id;
    private int $x = 0;
    private int $width = 0;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): FlowDiagramGroupDto
    {
        $this->name = $name;
        return $this;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): FlowDiagramGroupDto
    {
        $this->id = $id;
        return $this;
    }

    public function getX(): int
    {
        return $this->x;
    }

    public function setX(int $x): FlowDiagramGroupDto
    {
        $this->x = $x;
        return $this;
    }

    public function getWidth(): int
    {
        return $this->width;
    }

    public function setWidth(int $width): FlowDiagramGroupDto
    {
        $this->width = $width;
        return $this;
    }
}
