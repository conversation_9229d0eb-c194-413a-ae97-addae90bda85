<?php

namespace App\Domain\Dashboard\Dto\Widget;

class DataComparisonDto
{
    private float $value;
    private string $valueFormatted;
    private ?float $comparison = null;
    private ?string $comparisonFormatted = null;

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): DataComparisonDto
    {
        $this->value = $value;
        return $this;
    }

    public function getValueFormatted(): string
    {
        return $this->valueFormatted;
    }

    public function setValueFormatted(string $valueFormatted): DataComparisonDto
    {
        $this->valueFormatted = $valueFormatted;
        return $this;
    }

    public function getComparison(): ?float
    {
        return $this->comparison;
    }

    public function setComparison(?float $comparison): DataComparisonDto
    {
        $this->comparison = $comparison;
        return $this;
    }

    public function getComparisonFormatted(): ?string
    {
        return $this->comparisonFormatted;
    }

    public function setComparisonFormatted(?string $comparisonFormatted): DataComparisonDto
    {
        $this->comparisonFormatted = $comparisonFormatted;
        return $this;
    }
}
