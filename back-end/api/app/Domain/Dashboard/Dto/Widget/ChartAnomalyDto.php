<?php

namespace App\Domain\Dashboard\Dto\Widget;

use Carbon\CarbonInterface;

class ChartAnomalyDto
{
    private CarbonInterface $date;
    private float|string $value;
    private string $color;
    private int $seriesIndex;

    public function getDate(): CarbonInterface
    {
        return $this->date;
    }

    public function setDate(CarbonInterface $date): ChartAnomalyDto
    {
        $this->date = $date;
        return $this;
    }

    public function getValue(): float|string
    {
        return $this->value;
    }

    public function setValue(float|string $value): ChartAnomalyDto
    {
        $this->value = $value;
        return $this;
    }

    public function getColor(): string
    {
        return $this->color;
    }

    public function setColor(string $color): ChartAnomalyDto
    {
        $this->color = $color;
        return $this;
    }

    public function getSeriesIndex(): int
    {
        return $this->seriesIndex;
    }

    public function setSeriesIndex(int $seriesIndex): ChartAnomalyDto
    {
        $this->seriesIndex = $seriesIndex;
        return $this;
    }
}
