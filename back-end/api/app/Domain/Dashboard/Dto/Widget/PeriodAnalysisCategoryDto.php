<?php

namespace App\Domain\Dashboard\Dto\Widget;

class PeriodAnalysisCategoryDto
{
    private string $name;
    private array $rows;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): PeriodAnalysisCategoryDto
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return PeriodAnalysisRowDto[]
     */
    public function getRows(): array
    {
        return $this->rows;
    }

    /**
     * @param PeriodAnalysisRowDto[] $rows
     * @return $this
     */
    public function setRows(array $rows): PeriodAnalysisCategoryDto
    {
        $this->rows = $rows;
        return $this;
    }
}
