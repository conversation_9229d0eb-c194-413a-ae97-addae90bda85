<?php

namespace App\Domain\Dashboard\Dto\Widget;

use Carbon\CarbonInterface;

class ChartValueDto
{
    private CarbonInterface $date;
    private float|string $value;
    private string $valueFormatted;
    private ?string $prefix = null;
    private ?string $suffix = null;

    public function getDate(): CarbonInterface
    {
        return $this->date;
    }

    public function setDate(CarbonInterface $date): ChartValueDto
    {
        $this->date = $date;
        return $this;
    }

    public function getValue(): float|string
    {
        return $this->value;
    }

    public function setValue(float|string $value): ChartValueDto
    {
        $this->value = $value;
        return $this;
    }

    public function getValueFormatted(): string
    {
        return $this->valueFormatted;
    }

    public function setValueFormatted(string $valueFormatted): ChartValueDto
    {
        $this->valueFormatted = $valueFormatted;
        return $this;
    }

    public function getPrefix(): ?string
    {
        return $this->prefix;
    }

    public function setPrefix(?string $prefix): ChartValueDto
    {
        $this->prefix = $prefix;
        return $this;
    }

    public function getSuffix(): ?string
    {
        return $this->suffix;
    }

    public function setSuffix(?string $suffix): ChartValueDto
    {
        $this->suffix = $suffix;
        return $this;
    }
}
