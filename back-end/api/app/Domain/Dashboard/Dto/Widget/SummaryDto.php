<?php

namespace App\Domain\Dashboard\Dto\Widget;

class SummaryDto
{
    private string $text;
    private array $fields;

    public function getText(): string
    {
        return $this->text;
    }

    public function setText(string $text): SummaryDto
    {
        $this->text = $text;
        return $this;
    }

    /**
     * @return SummaryFieldDto[]
     */
    public function getFields(): array
    {
        return $this->fields;
    }

    /**
     * @param SummaryFieldDto[] $fields
     * @return $this
     */
    public function setFields(array $fields): SummaryDto
    {
        $this->fields = $fields;
        return $this;
    }
}
