<?php

namespace App\Domain\Dashboard\Dto\Widget;

class PeriodAnalysisRowDto
{
    private array $columns;

    /**
     * @return PeriodAnalysisColumnDto[]
     */
    public function getColumns(): array
    {
        return $this->columns;
    }

    /**
     * @param PeriodAnalysisColumnDto[] $columns
     * @return $this
     */
    public function setColumns(array $columns): PeriodAnalysisRowDto
    {
        $this->columns = $columns;
        return $this;
    }
}
