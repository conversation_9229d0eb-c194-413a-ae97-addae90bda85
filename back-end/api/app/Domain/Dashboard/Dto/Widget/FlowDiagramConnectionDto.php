<?php

namespace App\Domain\Dashboard\Dto\Widget;

class FlowDiagramConnectionDto
{
    private string $inputId;
    private string $outputId;

    public function getInputId(): string
    {
        return $this->inputId;
    }

    public function setInputId(string $inputId): FlowDiagramConnectionDto
    {
        $this->inputId = $inputId;
        return $this;
    }

    public function getOutputId(): string
    {
        return $this->outputId;
    }

    public function setOutputId(string $outputId): FlowDiagramConnectionDto
    {
        $this->outputId = $outputId;
        return $this;
    }
}
