<?php

namespace App\Domain\Dashboard\Dto\Widget;

class ChartSeriesDto
{
    private string $title;
    private array $values;

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): ChartSeriesDto
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @return ChartValueDto[]
     */
    public function getValues(): array
    {
        return $this->values;
    }

    /**
     * @param ChartValueDto[] $values
     * @return $this
     */
    public function setValues(array $values): ChartSeriesDto
    {
        $this->values = $values;
        return $this;
    }
}
