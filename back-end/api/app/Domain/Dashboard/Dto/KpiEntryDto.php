<?php

namespace App\Domain\Dashboard\Dto;

class KpiEntryDto
{
    private float $target;
    private int $year;
    private ?string $unit = null;

    public function getTarget(): float
    {
        return $this->target;
    }

    public function setTarget(float $target): KpiEntryDto
    {
        $this->target = $target;
        return $this;
    }

    public function getYear(): int
    {
        return $this->year;
    }

    public function setYear(int $year): KpiEntryDto
    {
        $this->year = $year;
        return $this;
    }

    public function getUnit(): ?string
    {
        return $this->unit;
    }

    public function setUnit(?string $unit): KpiEntryDto
    {
        $this->unit = $unit;
        return $this;
    }
}
