<?php

namespace App\Domain\Dashboard\Dto;

use App\Domain\Dashboard\Support\Enums\PageType;

class PageDto
{
    private PageType $type;
    private array $sections;

    public function getType(): PageType
    {
        return $this->type;
    }

    public function setType(PageType $type): PageDto
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return SectionDto[]
     */
    public function getSections(): array
    {
        return $this->sections;
    }

    /**
     * @param SectionDto[] $sections
     * @return $this
     */
    public function setSections(array $sections): PageDto
    {
        $this->sections = $sections;
        return $this;
    }
}
