<?php

namespace App\Domain\Dashboard\Dto;

use App\Domain\Dashboard\Support\Enums\WidgetAccuracy;
use Carbon\CarbonInterface;

class WidgetStatisticsFilter
{
    private ?CarbonInterface $startDate = null;

    private ?CarbonInterface $endDate = null;

    private ?WidgetAccuracy $accuracy = null;

    public function getStartDate(): ?CarbonInterface
    {
        return $this->startDate;
    }

    public function setStartDate(?CarbonInterface $startDate): WidgetStatisticsFilter
    {
        $this->startDate = $startDate;
        return $this;
    }

    public function getEndDate(): ?CarbonInterface
    {
        return $this->endDate;
    }

    public function setEndDate(?CarbonInterface $endDate): WidgetStatisticsFilter
    {
        $this->endDate = $endDate;
        return $this;
    }

    public function setAccuracy(?WidgetAccuracy $accuracy): WidgetStatisticsFilter
    {
        $this->accuracy = $accuracy;
        return $this;
    }

    public function getComparableStartDate(): ?CarbonInterface
    {
        if (!$this->getHoursInFilteredDateRange()) {
            return null;
        }

        if ($this->isMonthlyPeriod()) {
            return $this->getStartDate()->copy()->subMonthNoOverflow()->startOfMonth();
        }

        if ($this->isYearlyPeriod()) {
            return $this->getStartDate()->copy()->subYearNoOverflow()->startOfYear();
        }

        return $this->getStartDate()->copy()->subHours($this->getHoursInFilteredDateRange())->startOfDay();
    }

    public function getAccuracy(): WidgetAccuracy
    {
        if ($this->accuracy) {
            return $this->accuracy;
        }

        if (!$this->getHoursInFilteredDateRange()) {
            $this->accuracy = WidgetAccuracy::MONTH;
            return WidgetAccuracy::MONTH;
        }

        if ($this->getHoursInFilteredDateRange() > 744) {
            $this->accuracy = WidgetAccuracy::MONTH;
            return WidgetAccuracy::MONTH;
        }

        $this->accuracy = WidgetAccuracy::DAY;
        return WidgetAccuracy::DAY;
    }

    public function getComparableEndDate(): ?CarbonInterface
    {
        if (!$this->getHoursInFilteredDateRange()) {
            return null;
        }

        if ($this->isMonthlyPeriod()) {
            return $this->getEndDate()->copy()->subMonthNoOverflow()->endOfMonth();
        }

        if ($this->isYearlyPeriod()) {
            return $this->getEndDate()->copy()->subYearNoOverflow()->endOfYear();
        }

        return $this->getEndDate()->copy()->startOfDay()->subHours($this->getHoursInFilteredDateRange())->subSecond();
    }

    public function isMonthlyPeriod(): bool
    {
        if (!$this->getHoursInFilteredDateRange()) {
            return false;
        }

        if (!$this->getStartDate()->isSameMonth($this->getEndDate())) {
            return false;
        }

        $firstOfMonth = $this->getStartDate()->copy()->startOfMonth();
        $endOfMonth = $this->getEndDate()->copy()->endOfMonth();

        return $this->getStartDate()->isSameDay($firstOfMonth) && $this->getEndDate()->isSameDay($endOfMonth);
    }

    public function isYearlyPeriod(): bool
    {
        if (!$this->getHoursInFilteredDateRange()) {
            return false;
        }

        if (!$this->getStartDate()->isSameYear($this->getEndDate())) {
            return false;
        }

        $firstOfYear = $this->getStartDate()->copy()->startOfYear();
        $endOfYear = $this->getEndDate()->copy()->endOfYear();

        return $this->getStartDate()->isSameDay($firstOfYear) && $this->getEndDate()->isSameDay($endOfYear);
    }

    public function getHoursInFilteredDateRange(): ?int
    {
        if (!$this->getStartDate() || !$this->getEndDate()) {
            return null;
        }

        return (int) $this->getStartDate()->diffInHours($this->getEndDate());
    }
}
