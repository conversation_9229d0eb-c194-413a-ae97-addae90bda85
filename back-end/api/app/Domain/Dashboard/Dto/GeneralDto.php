<?php

namespace App\Domain\Dashboard\Dto;

use App\Domain\Dashboard\Support\Enums\DashboardBusinessType;

class GeneralDto
{
    private string $name;
    private int $userId;
    private DashboardBusinessType $type;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): GeneralDto
    {
        $this->name = $name;
        return $this;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): GeneralDto
    {
        $this->userId = $userId;
        return $this;
    }

    public function getType(): DashboardBusinessType
    {
        return $this->type;
    }

    public function setType(DashboardBusinessType $type): GeneralDto
    {
        $this->type = $type;
        return $this;
    }
}
