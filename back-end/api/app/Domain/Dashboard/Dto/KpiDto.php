<?php

namespace App\Domain\Dashboard\Dto;

use App\Domain\Dashboard\Support\Enums\WidgetType;

class KpiDto
{
    private WidgetType $type;
    private array $entries;

    public function getType(): WidgetType
    {
        return $this->type;
    }

    public function setType(WidgetType $type): KpiDto
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return KpiEntryDto[]
     */
    public function getEntries(): array
    {
        return $this->entries;
    }

    /**
     * @param KpiEntryDto[] $entries
     * @return $this
     */
    public function setEntries(array $entries): KpiDto
    {
        $this->entries = $entries;
        return $this;
    }
}
