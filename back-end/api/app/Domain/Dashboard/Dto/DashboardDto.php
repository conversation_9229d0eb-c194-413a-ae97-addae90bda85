<?php

namespace App\Domain\Dashboard\Dto;

class DashboardDto
{
    private GeneralDto $general;
    private array $pages;
    private array $sources;
    private RecipientDto $recipient;
    private array $kpis;

    public function getGeneral(): GeneralDto
    {
        return $this->general;
    }

    public function setGeneral(GeneralDto $general): DashboardDto
    {
        $this->general = $general;
        return $this;
    }

    /**
     * @return PageDto[];
     */
    public function getPages(): array
    {
        return $this->pages;
    }

    /**
     * @param PageDto[] $pages
     * @return $this
     */
    public function setPages(array $pages): DashboardDto
    {
        $this->pages = $pages;
        return $this;
    }

    /**
     * @return SourceDto[]
     */
    public function getSources(): array
    {
        return $this->sources;
    }

    /**
     * @param SourceDto[] $sources
     * @return $this
     */
    public function setSources(array $sources): DashboardDto
    {
        $this->sources = $sources;
        return $this;
    }

    public function getRecipient(): RecipientDto
    {
        return $this->recipient;
    }

    public function setRecipient(RecipientDto $recipient): DashboardDto
    {
        $this->recipient = $recipient;
        return $this;
    }

    /**
     * @return KpiDto[]
     */
    public function getKpis(): array
    {
        return $this->kpis;
    }

    /**
     * @param KpiDto[] $kpis
     * @return $this
     */
    public function setKpis(array $kpis): DashboardDto
    {
        $this->kpis = $kpis;
        return $this;
    }
}
