<?php

namespace App\Domain\Dashboard\Dto;

class SourceDto
{
    private ?array $ids = null;
    private string $type;

    public function getIds(): ?array
    {
        return $this->ids;
    }

    public function setIds(?array $ids): SourceDto
    {
        $this->ids = $ids;
        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): SourceDto
    {
        $this->type = $type;
        return $this;
    }
}
