<?php

namespace App\Domain\Dashboard\Support\Enums;

enum PageType: string
{
    case EXECUTIVE = 'EXECUTIVE';
    case TRENDS = 'TRENDS';
    case METRIC_TREE = 'METRIC_TREE';
    case INSIGHTS = 'INSIGHTS';

    public function toReadableString(): string
    {
        return match ($this) {
            self::EXECUTIVE => 'Executive',
            self::TRENDS => 'Trends',
            self::METRIC_TREE => 'Metric tree',
            self::INSIGHTS => 'Insights',
        };
    }
}
