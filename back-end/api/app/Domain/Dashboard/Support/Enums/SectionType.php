<?php

namespace App\Domain\Dashboard\Support\Enums;

enum SectionType: string
{
    case EXECUTIVE_SUMMARY = 'EXECUTIVE_SUMMARY';
    case EXECUTIVE_OVERVIEW = 'EXECUTIVE_OVERVIEW';
    case PERFORMANCE_TRENDS = 'PERFORMANCE_TRENDS';
    case CAMPAIGN_OVERVIEW = 'CAMPAIGN_OVERVIEW';
    case PERIOD_ANALYSIS = 'PERIOD_ANALYSIS';
    case METRIC_INFLUENCE = 'METRIC_INFLUENCE';
    case KEYWORD_QUALITY_SCORE = 'KEYWORD_QUALITY_SCORE';
    case QUALITY_SCORE = 'QUALITY_SCORE';
    case COMPETITION = 'COMPETITION';

    public function toReadableString(): string
    {
        return match ($this) {
            self::EXECUTIVE_SUMMARY => 'Executive summary',
            self::EXECUTIVE_OVERVIEW => 'Executive overview',
            self::PERFORMANCE_TRENDS => 'Performance trends',
            self::CAMPAIGN_OVERVIEW => 'Campaign overview',
            self::PERIOD_ANALYSIS => 'Period-over-Period Analysis',
            self::METRIC_INFLUENCE => 'Metric influence',
            self::KEYWORD_QUALITY_SCORE => 'Keyword Quality Score',
            self::QUALITY_SCORE => 'Quality Score',
            self::COMPETITION => 'Auction Insights',
        };
    }

    public function getGridColumns(): int
    {
        return match ($this) {
            self::EXECUTIVE_OVERVIEW => 4,
            self::QUALITY_SCORE => 3,
            self::EXECUTIVE_SUMMARY,
            self::PERFORMANCE_TRENDS,
            self::CAMPAIGN_OVERVIEW,
            self::KEYWORD_QUALITY_SCORE,
            self::COMPETITION
                => 2,
            default => 1,
        };
    }
}
