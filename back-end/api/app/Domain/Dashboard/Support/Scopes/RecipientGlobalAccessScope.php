<?php

namespace App\Domain\Dashboard\Support\Scopes;

use App\Domain\Dashboard\Models\Dashboard;
use App\Domain\Dashboard\Models\Recipient;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class RecipientGlobalAccessScope implements Scope
{
    public function apply(Builder $builder, Model $model): void
    {
        $dashboard = Auth::guard('dashboard')->user();

        if ($dashboard) {
            $builder->whereHas(
                Recipient::RELATION_DASHBOARD,
                fn(Builder $query) => $query->where(Dashboard::PROPERTY_ACCOUNT_ID, $dashboard->getId()),
            );
            return;
        }

        $user = Auth::guard('user')->user();

        if ($user) {
            $builder->whereHas(
                Recipient::RELATION_DASHBOARD,
                fn(Builder $query) => $query->where(Dashboard::PROPERTY_ACCOUNT_ID, $user->getAccountId()),
            );
            return;
        }
    }
}
