<?php

namespace App\Domain\Dashboard\Support\Scopes;

use App\Domain\Dashboard\Models\Dashboard;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class DashboardGlobalAccessScope implements Scope
{
    public function apply(Builder $builder, Model $model): void
    {
        $user = Auth::guard('user')->user();

        if ($user) {
            $builder->where(Dashboard::PROPERTY_ACCOUNT_ID, $user->getAccountId());
            return;
        }
    }
}
