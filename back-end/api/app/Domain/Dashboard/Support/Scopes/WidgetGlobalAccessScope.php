<?php

namespace App\Domain\Dashboard\Support\Scopes;

use App\Domain\Dashboard\Models\Dashboard;
use App\Domain\Dashboard\Models\Page;
use App\Domain\Dashboard\Models\Section;
use App\Domain\Dashboard\Models\Widget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class WidgetGlobalAccessScope implements Scope
{
    public function apply(Builder $builder, Model $model): void
    {
        $dashboard = Auth::guard('dashboard')->user();

        if ($dashboard) {
            $builder->whereHas(
                implode('.', [Widget::RELATION_SECTION, Section::RELATION_PAGE, Page::RELATION_DASHBOARD]),
                fn(Builder $query) => $query->where(Dashboard::PROPERTY_ID, $dashboard->getId()),
            );
            return;
        }

        $user = Auth::guard('user')->user();

        if ($user) {
            $builder->whereHas(
                implode('.', [Widget::RELATION_SECTION, Section::RELATION_PAGE, Page::RELATION_DASHBOARD]),
                fn(Builder $query) => $query->where(Dashboard::PROPERTY_ACCOUNT_ID, $user->getAccountId()),
            );
            return;
        }
    }
}
