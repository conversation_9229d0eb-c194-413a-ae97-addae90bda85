<?php

namespace App\Domain\Dashboard\Support\Traits;

use App\Domain\Dashboard\Models\DashboardSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

trait HasDashboardSourceThrough
{
    public const RELATION_DASHBOARD_SOURCE = 'dashboardSource';

    public function dashboardSource(): HasOneThrough
    {
        return $this->hasOneThrough(
            DashboardSource::class,
            static::DASHBOARD_SOURCE_CLASS,
            self::PROPERTY_ID,
            DashboardSource::PROPERTY_SOURCE_ID,
            static::DASHBOOARD_SOURCE_LOCAL_KEY,
            static::DASHBOARD_SOURCE_FOREIGN_KEY,
        )->where(DashboardSource::PROPERTY_SOURCE_TYPE, static::DASHBOARD_SOURCE_TYPE);
    }

    public function getDashboardSource(): ?DashboardSource
    {
        return $this->{self::RELATION_DASHBOARD_SOURCE};
    }

    public function scopeWithDashboardSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            self::RELATION_DASHBOARD_SOURCE,
            fn(Builder $query) => $query->whereIn(
                sprintf('%s.%s', DashboardSource::TABLE_NAME, DashboardSource::PROPERTY_ID),
                $dataSourceIds,
            ),
        );
    }
}
