<?php

namespace App\Domain\Dashboard\Support\Traits;

use App\Domain\Dashboard\Models\DashboardSource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

trait HasDashboardSource
{
    public const RELATION_DASHBOARD_SOURCE = 'dashboardSource';

    public function dashboardSource(): HasOne
    {
        return $this->hasOne(
            DashboardSource::class,
            DashboardSource::PROPERTY_SOURCE_ID,
            static::DASHBOARD_SOURCE_LOCAL_KEY,
        )->where(DashboardSource::PROPERTY_SOURCE_TYPE, static::DASHBOARD_SOURCE_TYPE);
    }

    public function getDashboardSource(): ?DashboardSource
    {
        return $this->{self::RELATION_DASHBOARD_SOURCE};
    }

    public function scopeWithDashboardSources(Builder $query, array $dataSourceIds): Builder
    {
        return $query->whereHas(
            self::RELATION_DASHBOARD_SOURCE,
            fn(Builder $query) => $query->whereIn(
                sprintf('%s.%s', DashboardSource::TABLE_NAME, DashboardSource::PROPERTY_ID),
                $dataSourceIds,
            ),
        );
    }
}
