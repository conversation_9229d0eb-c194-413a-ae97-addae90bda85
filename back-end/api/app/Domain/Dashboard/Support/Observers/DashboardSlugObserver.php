<?php

namespace App\Domain\Dashboard\Support\Observers;

use App\Domain\Dashboard\Models\Dashboard;
use Illuminate\Support\Str;

class DashboardSlugObserver
{
    public function creating(Dashboard $dashboard): void
    {
        $dashboard->setSlug($this->getSlug());
    }

    private function getSlug(): string
    {
        $slug = Str::random();

        $exists = Dashboard::query()->where(Dashboard::PROPERTY_SLUG)->exists();

        if ($exists) {
            return $this->getSlug();
        }

        return $slug;
    }
}
