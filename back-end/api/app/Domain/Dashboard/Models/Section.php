<?php

namespace App\Domain\Dashboard\Models;

use App\Domain\Dashboard\Support\Enums\SectionType;
use App\Domain\Dashboard\Support\Scopes\SectionGlobalAccessScope;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Section extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'sections';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_PAGE_ID = 'page_id';
    public const PROPERTY_TYPE = 'type';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_PLACEMENT = 'placement';
    public const PROPERTY_GRID_COLUMNS = 'grid_columns';

    public const RELATION_PAGE = 'page';
    public const RELATION_WIDGETS = 'widgets';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    protected $casts = [
        self::PROPERTY_TYPE => SectionType::class,
    ];

    public static function booted(): void
    {
        static::addGlobalScope(new SectionGlobalAccessScope());
    }

    public function page(): BelongsTo
    {
        return $this->belongsTo(Page::class, self::PROPERTY_PAGE_ID);
    }

    public function getPage(): Page
    {
        return $this->{self::RELATION_PAGE};
    }

    public function widgets(): HasMany
    {
        return $this->hasMany(Widget::class, Widget::PROPERTY_SECTION_ID);
    }

    public function getWidgets(): Collection
    {
        return $this->{self::RELATION_WIDGETS};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getPageId(): int
    {
        return $this->{self::PROPERTY_PAGE_ID};
    }

    public function setPageId(int $value): self
    {
        $this->{self::PROPERTY_PAGE_ID} = $value;
        return $this;
    }

    public function getType(): SectionType
    {
        return $this->{self::PROPERTY_TYPE};
    }

    public function setType(SectionType $value): self
    {
        $this->{self::PROPERTY_TYPE} = $value;
        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;
        return $this;
    }

    public function getPlacement(): int
    {
        return $this->{self::PROPERTY_PLACEMENT};
    }

    public function setPlacement(int $value): self
    {
        $this->{self::PROPERTY_PLACEMENT} = $value;
        return $this;
    }

    public function getGridColumns(): int
    {
        return $this->{self::PROPERTY_GRID_COLUMNS};
    }

    public function setGridColumns(int $value): self
    {
        $this->{self::PROPERTY_GRID_COLUMNS} = $value;

        return $this;
    }
}
