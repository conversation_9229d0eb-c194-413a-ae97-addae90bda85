<?php

namespace App\Domain\Dashboard\Models;

use App\Domain\Dashboard\Support\Enums\WidgetDataType;
use App\Domain\Dashboard\Support\Enums\WidgetType;
use App\Domain\Dashboard\Support\Scopes\WidgetGlobalAccessScope;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use App\Domain\Dashboard\Models\Section;
use Illuminate\Support\Collection;

class Widget extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'widgets';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_SECTION_ID = 'section_id';
    public const PROPERTY_DATA_TYPE = 'data_type';
    public const PROPERTY_TYPE = 'type';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_SETTINGS = 'settings';
    public const PROPERTY_FORMAT_DECIMALS = 'format_decimals';
    public const PROPERTY_COL_SPAN = 'col_span';

    public const RELATION_SECTION = 'section';
    public const RELATION_KPIS = 'kpis';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    protected $casts = [
        self::PROPERTY_DATA_TYPE => WidgetDataType::class,
        self::PROPERTY_TYPE => WidgetType::class,
        self::PROPERTY_SETTINGS => 'array',
    ];

    public static function booted(): void
    {
        static::addGlobalScope(new WidgetGlobalAccessScope());
    }

    public function section(): BelongsTo
    {
        return $this->belongsTo(Section::class, self::PROPERTY_SECTION_ID);
    }

    public function getSection(): Section
    {
        return $this->{self::RELATION_SECTION};
    }

    public function kpis(): HasMany
    {
        return $this->hasMany(WidgetKpi::class, WidgetKpi::PROPERTY_WIDGET_ID)->orderBy(
            WidgetKpi::PROPERTY_YEAR,
            'DESC',
        );
    }

    public function getKpis(): Collection
    {
        return $this->{self::RELATION_KPIS};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getSectionId(): int
    {
        return $this->{self::PROPERTY_SECTION_ID};
    }

    public function setSectionId(int $value): self
    {
        $this->{self::PROPERTY_SECTION_ID} = $value;
        return $this;
    }

    public function getDataType(): WidgetDataType
    {
        return $this->{self::PROPERTY_DATA_TYPE};
    }

    public function setDataType(WidgetDataType $value): self
    {
        $this->{self::PROPERTY_DATA_TYPE} = $value;
        return $this;
    }

    public function getType(): WidgetType
    {
        return $this->{self::PROPERTY_TYPE};
    }

    public function setType(WidgetType $value): self
    {
        $this->{self::PROPERTY_TYPE} = $value;
        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;
        return $this;
    }

    public function getSettings(): ?array
    {
        return $this->{self::PROPERTY_SETTINGS};
    }

    public function setSettings(?array $value): self
    {
        $this->{self::PROPERTY_SETTINGS} = $value;

        return $this;
    }

    public function getFormatDecimals(): int
    {
        return $this->{self::PROPERTY_FORMAT_DECIMALS};
    }

    public function setFormatDecimals(int $value): self
    {
        $this->{self::PROPERTY_FORMAT_DECIMALS} = $value;

        return $this;
    }

    public function getColSpan(): int
    {
        return $this->{self::PROPERTY_COL_SPAN};
    }

    public function setColSpan(int $value): self
    {
        $this->{self::PROPERTY_COL_SPAN} = $value;

        return $this;
    }
}
