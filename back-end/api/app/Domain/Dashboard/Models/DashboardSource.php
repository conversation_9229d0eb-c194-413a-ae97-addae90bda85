<?php

namespace App\Domain\Dashboard\Models;

use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class DashboardSource extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'dashboard_sources';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_DASHBOARD_ID = 'dashboard_id';
    public const PROPERTY_SOURCE_ID = 'source_id';
    public const PROPERTY_SOURCE_TYPE = 'source_type';

    public const RELATION_DASHBOARD = 'dashboard';
    public const RELATION_SOURCE = 'source';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    public function dashboard(): BelongsTo
    {
        return $this->belongsTo(Dashboard::class, self::PROPERTY_DASHBOARD_ID);
    }

    public function getDashboard(): Dashboard
    {
        return $this->{self::RELATION_DASHBOARD};
    }

    public function source(): MorphTo
    {
        return $this->morphTo('source', self::PROPERTY_SOURCE_TYPE, self::PROPERTY_SOURCE_ID);
    }

    public function getSource(): Model
    {
        return $this->{self::RELATION_SOURCE};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getDashboardId(): int
    {
        return $this->{self::PROPERTY_DASHBOARD_ID};
    }

    public function setDashboardId(int $value): self
    {
        $this->{self::PROPERTY_DASHBOARD_ID} = $value;

        return $this;
    }

    public function getSourceId(): int
    {
        return $this->{self::PROPERTY_SOURCE_ID};
    }

    public function setSourceId(int $value): self
    {
        $this->{self::PROPERTY_SOURCE_ID} = $value;

        return $this;
    }

    public function getSourceType(): string
    {
        return $this->{self::PROPERTY_SOURCE_TYPE};
    }

    public function setSourceType(string $value): self
    {
        $this->{self::PROPERTY_SOURCE_TYPE} = $value;

        return $this;
    }
}
