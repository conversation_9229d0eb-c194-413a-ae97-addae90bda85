<?php

namespace App\Domain\Dashboard\Models;

use App\Domain\Dashboard\Support\Enums\WidgetKpiUnit;
use App\Domain\Dashboard\Support\Scopes\WidgetKpiGlobalAccessScope;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WidgetKpi extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'widget_kpis';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_WIDGET_ID = 'widget_id';
    public const PROPERTY_TARGET = 'target';
    public const PROPERTY_YEAR = 'year';
    public const PROPERTY_UNIT = 'unit';

    public const RELATION_WIDGET = 'widget';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    protected $casts = [
        self::PROPERTY_UNIT => WidgetKpiUnit::class,
    ];

    public static function booted(): void
    {
        static::addGlobalScope(new WidgetKpiGlobalAccessScope());
    }

    public function widget(): BelongsTo
    {
        return $this->belongsTo(Widget::class, self::PROPERTY_WIDGET_ID);
    }

    public function getWidget(): Widget
    {
        return $this->{self::RELATION_WIDGET};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getWidgetId(): int
    {
        return $this->{self::PROPERTY_WIDGET_ID};
    }

    public function setWidgetId(int $value): self
    {
        $this->{self::PROPERTY_WIDGET_ID} = $value;

        return $this;
    }

    public function getTarget(): float
    {
        return $this->{self::PROPERTY_TARGET};
    }

    public function setTarget(float $value): self
    {
        $this->{self::PROPERTY_TARGET} = $value;

        return $this;
    }

    public function getYear(): int
    {
        return $this->{self::PROPERTY_YEAR};
    }

    public function setYear(int $value): self
    {
        $this->{self::PROPERTY_YEAR} = $value;

        return $this;
    }

    public function getUnit(): ?WidgetKpiUnit
    {
        return $this->{self::PROPERTY_UNIT};
    }

    public function setUnit(?WidgetKpiUnit $value): self
    {
        $this->{self::PROPERTY_UNIT} = $value;

        return $this;
    }
}
