<?php

namespace App\Domain\Dashboard\Models;

use App\Domain\Dashboard\Support\Enums\DashboardBusinessType;
use App\Domain\Dashboard\Support\Scopes\DashboardGlobalAccessScope;
use App\Domain\Profile\Models\Account;
use App\Domain\Profile\Models\User;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Dashboard extends Authenticatable
{
    use TimestampableMethods;

    public const TABLE_NAME = 'dashboards';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_ACCOUNT_ID = 'account_id';
    public const PROPERTY_USER_ID = 'user_id';
    public const PROPERTY_SLUG = 'slug';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_BUSINESS_TYPE = 'business_type';

    public const RELATION_ACCOUNT = 'account';
    public const RELATION_PAGES = 'pages';
    public const RELATION_USER = 'user';
    public const RELATION_SOURCES = 'sources';
    public const RELATION_RECIPIENT = 'recipient';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    protected $casts = [
        self::PROPERTY_BUSINESS_TYPE => DashboardBusinessType::class,
    ];

    public static function booted(): void
    {
        static::addGlobalScope(new DashboardGlobalAccessScope());
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class, self::PROPERTY_ACCOUNT_ID);
    }

    public function getAccount(): Account
    {
        return $this->{self::RELATION_ACCOUNT};
    }

    public function pages(): HasMany
    {
        return $this->hasMany(Page::class, Page::PROPERTY_DASHBOARD_ID);
    }

    public function getPages(): Collection
    {
        return $this->{self::RELATION_PAGES};
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::PROPERTY_USER_ID);
    }

    public function getUser(): ?User
    {
        return $this->{self::RELATION_USER};
    }

    public function sources(): HasMany
    {
        return $this->hasMany(DashboardSource::class, DashboardSource::PROPERTY_DASHBOARD_ID);
    }

    public function getSources(): Collection
    {
        return $this->{self::RELATION_SOURCES};
    }

    public function recipient(): HasOne
    {
        return $this->hasOne(Recipient::class, Recipient::PROPERTY_DASHBOARD_ID);
    }

    public function getRecipient(): ?Recipient
    {
        return $this->{self::RELATION_RECIPIENT};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getAccountId(): int
    {
        return $this->{self::PROPERTY_ACCOUNT_ID};
    }

    public function setAccountId(int $value): self
    {
        $this->{self::PROPERTY_ACCOUNT_ID} = $value;
        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;
        return $this;
    }

    public function getSlug(): string
    {
        return $this->{self::PROPERTY_SLUG};
    }

    public function setSlug(string $value): self
    {
        $this->{self::PROPERTY_SLUG} = $value;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->{self::PROPERTY_USER_ID};
    }

    public function setUserId(?int $value): self
    {
        $this->{self::PROPERTY_USER_ID} = $value;

        return $this;
    }

    public function getBusinessType(): ?DashboardBusinessType
    {
        return $this->{self::PROPERTY_BUSINESS_TYPE};
    }

    public function setBusinessType(?DashboardBusinessType $value): self
    {
        $this->{self::PROPERTY_BUSINESS_TYPE} = $value;

        return $this;
    }
}
