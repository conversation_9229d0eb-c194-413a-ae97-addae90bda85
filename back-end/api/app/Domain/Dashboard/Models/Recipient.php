<?php

namespace App\Domain\Dashboard\Models;

use App\Domain\Dashboard\Support\Scopes\RecipientGlobalAccessScope;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Recipient extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'dashboard_recipient';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_DASHBOARD_ID = 'dashboard_id';
    public const PROPERTY_COMPANY = 'company';

    public const RELATION_DASHBOARD = 'dashboard';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    public static function booted(): void
    {
        //        static::addGlobalScope(new RecipientGlobalAccessScope());
    }

    public function dashboard(): BelongsTo
    {
        return $this->belongsTo(Dashboard::class, self::PROPERTY_DASHBOARD_ID);
    }

    public function getDashboard(): Dashboard
    {
        return $this->{self::RELATION_DASHBOARD};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getDashboardId(): int
    {
        return $this->{self::PROPERTY_DASHBOARD_ID};
    }

    public function setDashboardId(int $value): self
    {
        $this->{self::PROPERTY_DASHBOARD_ID} = $value;

        return $this;
    }

    public function getCompany(): string
    {
        return $this->{self::PROPERTY_COMPANY};
    }

    public function setCompany(string $value): self
    {
        $this->{self::PROPERTY_COMPANY} = $value;

        return $this;
    }
}
