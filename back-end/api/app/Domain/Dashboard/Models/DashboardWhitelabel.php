<?php

namespace App\Domain\Dashboard\Models;

use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;

class DashboardWhitelabel extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'dashboard_whitelabels';

    // Property constants
    public const ACCOUNT_ID = 'account_id';
    public const FIRST_CHANGE = 'first_change';
    public const LANGUAGE = 'language';
    public const ZERO_SSL_ID = 'zero_ssl_id';
    public const ZERO_SSL_STATUS = 'zero_ssl_status';
    public const DOMAIN = 'domain';
    public const SSL_CERTIFICATE_EXPIRES_AT = 'ssl_certificate_expires_at';
    public const LOGO = 'logo';
    public const LOGO_SIZE = 'logo_size';
    public const ACCENT_COLOR = 'accent_color';
    public const ACCENT_TEXT_COLOR = 'accent_text_color';
    public const HEADER_BACKGROUND_COLOR = 'header_background_color';
    public const HEADER_TEXT_COLOR = 'header_text_color';
    public const BACKGROUND_COLOR = 'background_color';
    public const TEXT_COLOR = 'text_color';
    public const WIDGET_BACKGROUND_COLOR = 'widget_background_color';
    public const WIDGET_TEXT_COLOR = 'widget_text_color';

    // Account ID getter and setter
    public function getAccountId(): ?int
    {
        return $this->getAttribute(self::ACCOUNT_ID);
    }

    public function setAccountId(?int $accountId): self
    {
        $this->setAttribute(self::ACCOUNT_ID, $accountId);
        return $this;
    }

    // First Change getter and setter
    public function getFirstChange(): bool
    {
        return $this->getAttribute(self::FIRST_CHANGE) ?? false;
    }

    public function setFirstChange(bool $firstChange): self
    {
        $this->setAttribute(self::FIRST_CHANGE, $firstChange);
        return $this;
    }

    // Language getter and setter
    public function getLanguage(): ?string
    {
        return $this->getAttribute(self::LANGUAGE);
    }

    public function setLanguage(?string $language): self
    {
        $this->setAttribute(self::LANGUAGE, $language);
        return $this;
    }

    // Zero SSL ID getter and setter
    public function getZeroSslId(): ?string
    {
        return $this->getAttribute(self::ZERO_SSL_ID);
    }

    public function setZeroSslId(?string $zeroSslId): self
    {
        $this->setAttribute(self::ZERO_SSL_ID, $zeroSslId);
        return $this;
    }

    // Zero SSL Status getter and setter
    public function getZeroSslStatus(): ?string
    {
        return $this->getAttribute(self::ZERO_SSL_STATUS);
    }

    public function setZeroSslStatus(?string $zeroSslStatus): self
    {
        $this->setAttribute(self::ZERO_SSL_STATUS, $zeroSslStatus);
        return $this;
    }

    // Domain getter and setter
    public function getDomain(): ?string
    {
        return $this->getAttribute(self::DOMAIN);
    }

    public function setDomain(?string $domain): self
    {
        $this->setAttribute(self::DOMAIN, $domain);
        return $this;
    }

    // SSL Certificate Expires At getter and setter
    public function getSslCertificateExpiresAt(): ?string
    {
        return $this->getAttribute(self::SSL_CERTIFICATE_EXPIRES_AT);
    }

    public function setSslCertificateExpiresAt(?string $sslCertificateExpiresAt): self
    {
        $this->setAttribute(self::SSL_CERTIFICATE_EXPIRES_AT, $sslCertificateExpiresAt);
        return $this;
    }

    // Logo getter and setter
    public function getLogo(): ?string
    {
        return $this->getAttribute(self::LOGO);
    }

    public function setLogo(?string $logo): self
    {
        $this->setAttribute(self::LOGO, $logo);
        return $this;
    }

    // Logo Size getter and setter
    public function getLogoSize(): ?int
    {
        return $this->getAttribute(self::LOGO_SIZE);
    }

    public function setLogoSize(?int $logoSize): self
    {
        $this->setAttribute(self::LOGO_SIZE, $logoSize);
        return $this;
    }

    // Accent Color getter and setter
    public function getAccentColor(): ?string
    {
        return $this->getAttribute(self::ACCENT_COLOR);
    }

    public function setAccentColor(?string $accentColor): self
    {
        $this->setAttribute(self::ACCENT_COLOR, $accentColor);
        return $this;
    }

    // Accent Text Color getter and setter
    public function getAccentTextColor(): ?string
    {
        return $this->getAttribute(self::ACCENT_TEXT_COLOR);
    }

    public function setAccentTextColor(?string $accentTextColor): self
    {
        $this->setAttribute(self::ACCENT_TEXT_COLOR, $accentTextColor);
        return $this;
    }

    // Header Background Color getter and setter
    public function getHeaderBackgroundColor(): ?string
    {
        return $this->getAttribute(self::HEADER_BACKGROUND_COLOR);
    }

    public function setHeaderBackgroundColor(?string $headerBackgroundColor): self
    {
        $this->setAttribute(self::HEADER_BACKGROUND_COLOR, $headerBackgroundColor);
        return $this;
    }

    // Header Text Color getter and setter
    public function getHeaderTextColor(): ?string
    {
        return $this->getAttribute(self::HEADER_TEXT_COLOR);
    }

    public function setHeaderTextColor(?string $headerTextColor): self
    {
        $this->setAttribute(self::HEADER_TEXT_COLOR, $headerTextColor);
        return $this;
    }

    // Background Color getter and setter
    public function getBackgroundColor(): ?string
    {
        return $this->getAttribute(self::BACKGROUND_COLOR);
    }

    public function setBackgroundColor(?string $backgroundColor): self
    {
        $this->setAttribute(self::BACKGROUND_COLOR, $backgroundColor);
        return $this;
    }

    // Text Color getter and setter
    public function getTextColor(): ?string
    {
        return $this->getAttribute(self::TEXT_COLOR);
    }

    public function setTextColor(?string $textColor): self
    {
        $this->setAttribute(self::TEXT_COLOR, $textColor);
        return $this;
    }

    // Widget Background Color getter and setter
    public function getWidgetBackgroundColor(): ?string
    {
        return $this->getAttribute(self::WIDGET_BACKGROUND_COLOR);
    }

    public function setWidgetBackgroundColor(?string $widgetBackgroundColor): self
    {
        $this->setAttribute(self::WIDGET_BACKGROUND_COLOR, $widgetBackgroundColor);
        return $this;
    }

    // Widget Text Color getter and setter
    public function getWidgetTextColor(): ?string
    {
        return $this->getAttribute(self::WIDGET_TEXT_COLOR);
    }

    public function setWidgetTextColor(?string $widgetTextColor): self
    {
        $this->setAttribute(self::WIDGET_TEXT_COLOR, $widgetTextColor);
        return $this;
    }
}
