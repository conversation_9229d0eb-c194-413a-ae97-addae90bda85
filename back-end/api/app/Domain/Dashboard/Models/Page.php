<?php

namespace App\Domain\Dashboard\Models;

use App\Domain\Dashboard\Support\Enums\PageType;
use App\Domain\Dashboard\Support\Scopes\PageGlobalAccessScope;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

class Page extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'pages';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_DASHBOARD_ID = 'dashboard_id';
    public const PROPERTY_TYPE = 'type';
    public const PROPERTY_NAME = 'name';

    public const RELATION_DASHBOARD = 'dashboard';
    public const RELATION_SECTIONS = 'sections';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    protected $casts = [
        self::PROPERTY_TYPE => PageType::class,
    ];

    public static function booted(): void
    {
        static::addGlobalScope(new PageGlobalAccessScope());
    }

    public function dashboard(): BelongsTo
    {
        return $this->belongsTo(Dashboard::class, self::PROPERTY_DASHBOARD_ID);
    }

    public function getDashboard(): Dashboard
    {
        return $this->{self::RELATION_DASHBOARD};
    }

    public function sections(): HasMany
    {
        return $this->hasMany(Section::class, Section::PROPERTY_PAGE_ID)->orderBy(Section::PROPERTY_PLACEMENT, 'ASC');
    }

    public function geSections(): Collection
    {
        return $this->{self::RELATION_SECTIONS};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getDashboardId(): int
    {
        return $this->{self::PROPERTY_DASHBOARD_ID};
    }

    public function setDashboardId(int $value): self
    {
        $this->{self::PROPERTY_DASHBOARD_ID} = $value;
        return $this;
    }

    public function getType(): PageType
    {
        return $this->{self::PROPERTY_TYPE};
    }

    public function setType(PageType $value): self
    {
        $this->{self::PROPERTY_TYPE} = $value;
        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;
        return $this;
    }
}
