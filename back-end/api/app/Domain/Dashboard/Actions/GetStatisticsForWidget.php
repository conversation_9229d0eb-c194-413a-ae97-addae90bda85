<?php

namespace App\Domain\Dashboard\Actions;

use App\Domain\Dashboard\Actions\Widget\GetAccountQualityScore;
use App\Domain\Dashboard\Actions\Widget\GetAuctionInsightsData;
use App\Domain\Dashboard\Actions\Widget\GetCampaignPerformanceData;
use App\Domain\Dashboard\Actions\Widget\GetExecutiveOverviewData;
use App\Domain\Dashboard\Actions\Widget\GetExecutiveSummaryData;
use App\Domain\Dashboard\Actions\Widget\GetKeywordQualityScoreData;
use App\Domain\Dashboard\Actions\Widget\GetKeywordScoreTrendData;
use App\Domain\Dashboard\Actions\Widget\GetMetricTreeInfluenceData;
use App\Domain\Dashboard\Actions\Widget\GetQualityScoreData;
use App\Domain\Dashboard\Actions\Widget\GetRoasData;
use App\Domain\Dashboard\Actions\Widget\GetTotalConversionValueData;
use App\Domain\Dashboard\Actions\Widget\GetPerformanceTrendsData;
use App\Domain\Dashboard\Actions\Widget\GetTrendsPeriodAnalysisData;
use App\Domain\Dashboard\Dto\Widget\ChartDto;
use App\Domain\Dashboard\Dto\Widget\DataComparisonDto;
use App\Domain\Dashboard\Dto\Widget\FlowDiagramDto;
use App\Domain\Dashboard\Dto\Widget\PeriodAnalysisDto;
use App\Domain\Dashboard\Dto\Widget\PieChartDto;
use App\Domain\Dashboard\Dto\Widget\RadialChartDto;
use App\Domain\Dashboard\Dto\Widget\SummaryDto;
use App\Domain\Dashboard\Dto\Widget\TableDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Dashboard\Support\Enums\PageType;
use App\Domain\Dashboard\Support\Enums\SectionType;
use App\Domain\Dashboard\Support\Enums\WidgetType;
use App\Domain\Google\Models\GoogleAdAccountKeywordQualityScore;
use App\Domain\Google\Models\GoogleAdCampaignInsight;

class GetStatisticsForWidget
{
    public function execute(
        Widget $widget,
        WidgetStatisticsFilter $filter,
    ): DataComparisonDto|SummaryDto|ChartDto|TableDto|RadialChartDto|PeriodAnalysisDto|FlowDiagramDto|PieChartDto {
        return $this->getStatisticsForType($widget)->execute($widget, $filter);
    }

    private function getStatisticsForType(Widget $widget)
    {
        switch ($widget->getSection()->getPage()->getType()) {
            case PageType::EXECUTIVE:
                switch ($widget->getSection()->getType()) {
                    case SectionType::EXECUTIVE_OVERVIEW:
                        return match ($widget->getType()) {
                            WidgetType::IMPRESSIONS => app(GetExecutiveOverviewData::class, [
                                'field' => GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS,
                            ]),
                            WidgetType::CLICKS => app(GetExecutiveOverviewData::class, [
                                'field' => GoogleAdCampaignInsight::PROPERTY_CLICKS,
                            ]),
                            WidgetType::CONVERSIONS => app(GetExecutiveOverviewData::class, [
                                'field' => GoogleAdCampaignInsight::PROPERTY_CONVERSIONS,
                            ]),
                            WidgetType::REVENUE => app(GetExecutiveOverviewData::class, [
                                'field' => GoogleAdCampaignInsight::PROPERTY_REVENUE,
                            ]),
                            WidgetType::COST => app(GetExecutiveOverviewData::class, [
                                'field' => GoogleAdCampaignInsight::PROPERTY_SPEND,
                            ]),
                            WidgetType::PROFIT => app(GetExecutiveOverviewData::class, [
                                'field' => GoogleAdCampaignInsight::PROPERTY_PROFIT,
                            ]),
                            WidgetType::ROAS => app(GetExecutiveOverviewData::class, [
                                'field' => GoogleAdCampaignInsight::PROPERTY_ROAS,
                            ]),
                            WidgetType::CONVERSION_RATE => app(GetExecutiveOverviewData::class, [
                                'field' => GoogleAdCampaignInsight::PROPERTY_CONVERSION_RATE,
                            ]),
                            default => throw new \Exception('No widget found'),
                        };
                    case SectionType::PERFORMANCE_TRENDS:
                        return match ($widget->getType()) {
                            WidgetType::PERFORMANCE_TRENDS => app(GetPerformanceTrendsData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_REVENUE => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 2,
                                        GetPerformanceTrendsData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetPerformanceTrendsData::SETTING_PREFIX => '€',
                                    ],
                                    GoogleAdCampaignInsight::PROPERTY_SPEND => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 2,
                                        GetPerformanceTrendsData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetPerformanceTrendsData::SETTING_PREFIX => '€',
                                    ],
                                    GoogleAdCampaignInsight::PROPERTY_PROFIT => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 2,
                                        GetPerformanceTrendsData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetPerformanceTrendsData::SETTING_PREFIX => '€',
                                    ],
                                    GoogleAdCampaignInsight::PROPERTY_CONVERSIONS => [],
                                ],
                            ]),
                            default => throw new \Exception('No widget found'),
                        };
                    case SectionType::CAMPAIGN_OVERVIEW:
                        return match ($widget->getType()) {
                            WidgetType::CAMPAIGN_PERFORMANCE => app(GetCampaignPerformanceData::class),
                            default => throw new \Exception('No widget found'),
                        };
                    case SectionType::EXECUTIVE_SUMMARY:
                        return match ($widget->getType()) {
                            WidgetType::EXECUTIVE_SUMMARY => app(GetExecutiveSummaryData::class),
                            WidgetType::TOTAL_CONVERSION_VALUE => app(GetTotalConversionValueData::class),
                            WidgetType::ROAS => app(GetRoasData::class),
                            default => throw new \Exception('No widget found'),
                        };
                }
                break;
            case PageType::TRENDS:
                switch ($widget->getSection()->getType()) {
                    case SectionType::PERFORMANCE_TRENDS:
                        return match ($widget->getType()) {
                            WidgetType::IMPRESSIONS => app(GetPerformanceTrendsData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS => [],
                                ],
                                'chartName' => 'Impressions',
                            ]),
                            WidgetType::CLICKS => app(GetPerformanceTrendsData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_CLICKS => [],
                                ],
                                'chartName' => 'Clicks',
                            ]),
                            WidgetType::REVENUE => app(GetPerformanceTrendsData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_REVENUE => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 2,
                                        GetPerformanceTrendsData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetPerformanceTrendsData::SETTING_PREFIX => '€',
                                    ],
                                ],
                                'chartName' => 'Revenue',
                            ]),
                            WidgetType::SPEND => app(GetPerformanceTrendsData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_SPEND => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 2,
                                        GetPerformanceTrendsData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetPerformanceTrendsData::SETTING_PREFIX => '€',
                                    ],
                                ],
                                'chartName' => 'Spend',
                            ]),
                            WidgetType::PROFIT => app(GetPerformanceTrendsData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_PROFIT => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 2,
                                        GetPerformanceTrendsData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetPerformanceTrendsData::SETTING_PREFIX => '€',
                                    ],
                                ],
                                'chartName' => 'Profit',
                            ]),
                            WidgetType::ROAS => app(GetPerformanceTrendsData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_ROAS => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 1,
                                        GetPerformanceTrendsData::SETTING_SUFFIX => '%',
                                    ],
                                ],
                                'chartName' => 'ROAS',
                            ]),
                            WidgetType::CONVERSION_RATE => app(GetPerformanceTrendsData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_CONVERSION_RATE => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 1,
                                        GetPerformanceTrendsData::SETTING_SUFFIX => '%',
                                    ],
                                ],
                                'chartName' => 'Conversion Rate',
                            ]),
                            WidgetType::CONVERSIONS => app(GetPerformanceTrendsData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_CONVERSIONS => [],
                                ],
                                'chartName' => 'conversions',
                            ]),
                            default => throw new \Exception('No widget found'),
                        };
                    case SectionType::PERIOD_ANALYSIS:
                        return match ($widget->getType()) {
                            WidgetType::PERIOD_ANALYSIS => app(GetTrendsPeriodAnalysisData::class, [
                                'fields' => [
                                    GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS => [],
                                    GoogleAdCampaignInsight::PROPERTY_CLICKS => [],
                                    GoogleAdCampaignInsight::PROPERTY_CONVERSIONS => [],
                                    GoogleAdCampaignInsight::PROPERTY_REVENUE => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 2,
                                        GetPerformanceTrendsData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetPerformanceTrendsData::SETTING_PREFIX => '€',
                                    ],
                                    GoogleAdCampaignInsight::PROPERTY_SPEND => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 2,
                                        GetPerformanceTrendsData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetPerformanceTrendsData::SETTING_PREFIX => '€',
                                    ],
                                    GoogleAdCampaignInsight::PROPERTY_PROFIT => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 2,
                                        GetPerformanceTrendsData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetPerformanceTrendsData::SETTING_PREFIX => '€',
                                    ],
                                    GoogleAdCampaignInsight::PROPERTY_ROAS => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 1,
                                        GetPerformanceTrendsData::SETTING_SUFFIX => '%',
                                    ],
                                    GoogleAdCampaignInsight::PROPERTY_CONVERSION_RATE => [
                                        GetPerformanceTrendsData::SETTING_DECIMALS => 1,
                                        GetPerformanceTrendsData::SETTING_SUFFIX => '%',
                                    ],
                                ],
                            ]),
                            default => throw new \Exception('No widget found'),
                        };
                    case SectionType::QUALITY_SCORE:
                        return match ($widget->getType()) {
                            WidgetType::COUNT, WidgetType::IMPRESSIONS, WidgetType::CLICKS => app(
                                GetQualityScoreData::class,
                            ),
                            default => throw new \Exception('No widget found'),
                        };
                    case SectionType::KEYWORD_QUALITY_SCORE:
                        return match ($widget->getType()) {
                            WidgetType::IMPRESSIONS => app(GetKeywordQualityScoreData::class, [
                                'fields' => [
                                    GoogleAdAccountKeywordQualityScore::PROPERTY_IMPRESSIONS => [],
                                ],
                                'chartName' => 'Impressions',
                            ]),
                            WidgetType::CLICKS => app(GetKeywordQualityScoreData::class, [
                                'fields' => [
                                    GoogleAdAccountKeywordQualityScore::PROPERTY_CLICKS => [],
                                ],
                                'chartName' => 'Clicks',
                            ]),
                            WidgetType::CTR => app(GetKeywordQualityScoreData::class, [
                                'fields' => [
                                    GetKeywordQualityScoreData::FIELD_CTR => [
                                        GetKeywordQualityScoreData::SETTING_DECIMALS => 1,
                                        GetKeywordQualityScoreData::SETTING_SUFFIX => '%',
                                    ],
                                ],
                                'chartName' => 'CTR',
                            ]),
                            WidgetType::CPC => app(GetKeywordQualityScoreData::class, [
                                'fields' => [
                                    GetKeywordQualityScoreData::FIELD_CPC => [],
                                ],
                                'chartName' => 'CPC',
                            ]),
                            WidgetType::COST => app(GetKeywordQualityScoreData::class, [
                                'fields' => [
                                    GoogleAdAccountKeywordQualityScore::PROPERTY_COST => [
                                        GetKeywordQualityScoreData::SETTING_DECIMALS => 2,
                                        GetKeywordQualityScoreData::SETTING_NEEDS_FORMATTING_FOR_VALUE => true,
                                        GetKeywordQualityScoreData::SETTING_PREFIX => '€',
                                    ],
                                ],
                                'chartName' => 'Costs',
                            ]),
                            WidgetType::CONVERSIONS => app(GetKeywordQualityScoreData::class, [
                                'fields' => [
                                    GoogleAdAccountKeywordQualityScore::PROPERTY_CONVERSIONS => [],
                                ],
                                'chartName' => 'Conversions',
                            ]),
                            WidgetType::SCORE => app(GetAccountQualityScore::class),
                            WidgetType::SCORE_TREND => app(GetKeywordScoreTrendData::class),

                            default => throw new \Exception('No widget found'),
                        };
                    case SectionType::COMPETITION:
                        return match ($widget->getType()) {
                            WidgetType::COMPARISON => app(GetAuctionInsightsData::class),
                            default => throw new \Exception('No widget found'),
                        };
                }
                break;
            case PageType::METRIC_TREE:
                switch ($widget->getSection()->getType()) {
                    case SectionType::METRIC_INFLUENCE:
                        return match ($widget->getType()) {
                            WidgetType::INFLUENCE => app(GetMetricTreeInfluenceData::class),
                            default => throw new \Exception('No widget found'),
                        };
                }
                break;
        }

        throw new \Exception('No widget found');
    }
}
