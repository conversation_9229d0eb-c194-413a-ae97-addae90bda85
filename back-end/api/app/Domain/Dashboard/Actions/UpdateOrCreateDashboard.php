<?php

namespace App\Domain\Dashboard\Actions;

use App\Domain\Dashboard\Actions\Widget\GetAuctionInsightsData;
use App\Domain\Dashboard\Dto\DashboardDto;
use App\Domain\Dashboard\Dto\KpiDto;
use App\Domain\Dashboard\Dto\PageDto;
use App\Domain\Dashboard\Dto\SectionDto;
use App\Domain\Dashboard\Dto\SourceDto;
use App\Domain\Dashboard\Models\Dashboard;
use App\Domain\Dashboard\Models\DashboardSource;
use App\Domain\Dashboard\Models\Page;
use App\Domain\Dashboard\Models\Recipient;
use App\Domain\Dashboard\Models\Section;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Dashboard\Models\WidgetKpi;
use App\Domain\Dashboard\Support\Enums\PageType;
use App\Domain\Dashboard\Support\Enums\SectionType;
use App\Domain\Dashboard\Support\Enums\WidgetDataType;
use App\Domain\Dashboard\Support\Enums\WidgetType;
use App\Domain\Google\Models\GoogleAdAccountAuctionInsight;
use App\Domain\Google\Models\GoogleAdAccountQualityScore;
use App\Domain\Profile\Models\Account;
use Illuminate\Support\Facades\DB;

class UpdateOrCreateDashboard
{
    private array $kpis;

    public function execute(DashboardDto $dto, Account $account, ?Dashboard $dashboard = null): Dashboard
    {
        DB::beginTransaction();

        /** @var Dashboard $dashboard */
        $dashboard = Dashboard::query()->updateOrCreate(
            [
                Dashboard::PROPERTY_ACCOUNT_ID => $account->getId(),
                Dashboard::PROPERTY_ID => $dashboard?->getId(),
            ],
            [
                Dashboard::PROPERTY_NAME => $dto->getGeneral()->getName(),
                Dashboard::PROPERTY_USER_ID => $dto->getGeneral()->getUserId(),
                Dashboard::PROPERTY_BUSINESS_TYPE => $dto->getGeneral()->getType(),
            ],
        );

        Recipient::query()->updateOrCreate(
            [
                Recipient::PROPERTY_DASHBOARD_ID => $dashboard->getId(),
            ],
            [
                Recipient::PROPERTY_COMPANY => $dto->getRecipient()->getCompany(),
            ],
        );

        $this->kpis = $dto->getKpis();

        $this->processSources($dashboard, $dto->getSources());
        $this->processPages($dashboard, $dto->getPages());

        DB::commit();

        return $dashboard;
    }

    /**
     * @param SourceDto[] $sources
     */
    private function processSources(Dashboard $dashboard, array $sources): void
    {
        $dashboard->sources()->delete();

        foreach ($sources as $source) {
            if (!$source->getIds()) {
                continue;
            }

            $dashboard->sources()->createMany(
                array_map(
                    fn($id) => [
                        DashboardSource::PROPERTY_SOURCE_ID => $id,
                        DashboardSource::PROPERTY_SOURCE_TYPE => $source->getType(),
                    ],
                    $source->getIds(),
                ),
            );
        }
    }

    /**
     * @param Dashboard $dashboard
     * @param PageDto[] $pages
     * @return void
     */
    private function processPages(Dashboard $dashboard, array $pages): void
    {
        $dashboard->pages()->delete();

        foreach ($pages as $data) {
            $page = $dashboard->pages()->create([
                Page::PROPERTY_TYPE => $data->getType(),
                Page::PROPERTY_NAME => $data->getType()->toReadableString(),
            ]);

            $this->processSections($page, $data->getSections());
        }
    }

    /**
     * @param Page $page
     * @param SectionDto[] $sections
     * @return void
     */
    private function processSections(Page $page, array $sections): void
    {
        foreach ($sections as $index => $section) {
            $section = $page->sections()->create([
                Section::PROPERTY_TYPE => $section->getType(),
                Section::PROPERTY_NAME => $section->getType()->toReadableString(),
                Section::PROPERTY_PLACEMENT => $index,
                Section::PROPERTY_GRID_COLUMNS => $section->getType()->getGridColumns(),
            ]);

            $this->createWidgets($page, $section);
        }
    }

    private function createWidgets(Page $page, Section $section): void
    {
        switch ($page->getType()) {
            case PageType::EXECUTIVE:
                switch ($section->getType()) {
                    case SectionType::EXECUTIVE_OVERVIEW:
                        $this->createWidget(
                            $section,
                            'Impressions',
                            WidgetType::IMPRESSIONS,
                            WidgetDataType::DATA_COMPARISON,
                            settings: ['icon' => 'fa-regular fa-eye'],
                        );
                        $this->createWidget(
                            $section,
                            'Clicks',
                            WidgetType::CLICKS,
                            WidgetDataType::DATA_COMPARISON,
                            settings: ['icon' => 'fa-regular fa-arrow-pointer'],
                        );
                        $this->createWidget(
                            $section,
                            'Conversions',
                            WidgetType::CONVERSIONS,
                            WidgetDataType::DATA_COMPARISON,
                            settings: ['icon' => 'fa-regular fa-cart-shopping'],
                        );
                        $this->createWidget(
                            $section,
                            'Revenue',
                            WidgetType::REVENUE,
                            WidgetDataType::DATA_COMPARISON,
                            2,
                            settings: [
                                'prefix' => '€',
                                'icon' => 'fa-regular fa-dollar-sign',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Cost',
                            WidgetType::PROFIT,
                            WidgetDataType::DATA_COMPARISON,
                            2,
                            settings: [
                                'prefix' => '€',
                                'icon' => 'fa-regular fa-dollar-sign',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Profit',
                            WidgetType::PROFIT,
                            WidgetDataType::DATA_COMPARISON,
                            2,
                            settings: [
                                'prefix' => '€',
                                'icon' => 'fa-regular fa-arrow-trend-up',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'ROAS',
                            WidgetType::ROAS,
                            WidgetDataType::DATA_COMPARISON,
                            2,
                            settings: [
                                'suffix' => '%',
                                'icon' => 'fa-regular fa-chart-simple',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Conversion Rate',
                            WidgetType::CONVERSION_RATE,
                            WidgetDataType::DATA_COMPARISON,
                            2,
                            settings: ['suffix' => '%', 'icon' => 'fa-regular fa-chart-simple'],
                        );
                        break;
                    case SectionType::EXECUTIVE_SUMMARY:
                        $this->createWidget(
                            $section,
                            'Summary',
                            WidgetType::EXECUTIVE_SUMMARY,
                            WidgetDataType::SUMMARY,
                            colSpan: 2,
                        );
                        $this->createWidget(
                            $section,
                            'Total conversion value',
                            WidgetType::TOTAL_CONVERSION_VALUE,
                            WidgetDataType::RADIAL_CHART,
                            settings: [
                                'angle' => [
                                    'start' => -90,
                                    'end' => 90,
                                ],
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'ROAS',
                            WidgetType::ROAS,
                            WidgetDataType::RADIAL_CHART,
                            settings: [
                                'angle' => [
                                    'start' => -90,
                                    'end' => 90,
                                ],
                            ],
                        );
                        break;
                    case SectionType::PERFORMANCE_TRENDS:
                        $this->createWidget($section, '', WidgetType::PERFORMANCE_TRENDS, WidgetDataType::COLUMN_CHART);
                        break;
                    case SectionType::CAMPAIGN_OVERVIEW:
                        $this->createWidget(
                            $section,
                            '',
                            WidgetType::CAMPAIGN_PERFORMANCE,
                            WidgetDataType::TABLE,
                            colSpan: 2,
                        );
                        break;
                }
                break;
            case PageType::TRENDS:
                switch ($section->getType()) {
                    case SectionType::PERFORMANCE_TRENDS:
                        $this->createWidget(
                            $section,
                            $section->getType()->toReadableString(),
                            WidgetType::IMPRESSIONS,
                            WidgetDataType::LINE_CHART,
                        );
                        $this->createWidget(
                            $section,
                            $section->getType()->toReadableString(),
                            WidgetType::CLICKS,
                            WidgetDataType::LINE_CHART,
                        );
                        $this->createWidget(
                            $section,
                            $section->getType()->toReadableString(),
                            WidgetType::CONVERSIONS,
                            WidgetDataType::LINE_CHART,
                        );
                        $this->createWidget(
                            $section,
                            $section->getType()->toReadableString(),
                            WidgetType::REVENUE,
                            WidgetDataType::LINE_CHART,
                        );
                        $this->createWidget(
                            $section,
                            $section->getType()->toReadableString(),
                            WidgetType::SPEND,
                            WidgetDataType::LINE_CHART,
                        );
                        $this->createWidget(
                            $section,
                            $section->getType()->toReadableString(),
                            WidgetType::ROAS,
                            WidgetDataType::LINE_CHART,
                        );
                        $this->createWidget(
                            $section,
                            $section->getType()->toReadableString(),
                            WidgetType::CONVERSION_RATE,
                            WidgetDataType::LINE_CHART,
                        );
                        break;
                    case SectionType::PERIOD_ANALYSIS:
                        $this->createWidget(
                            $section,
                            $section->getType()->toReadableString(),
                            WidgetType::PERIOD_ANALYSIS,
                            WidgetDataType::PERIOD_ANALYSIS,
                        );
                        break;
                    case SectionType::KEYWORD_QUALITY_SCORE:
                        $this->createWidget(
                            $section,
                            'Account Quality Score',
                            WidgetType::SCORE,
                            WidgetDataType::RADIAL_CHART,
                            settings: [
                                'angle' => [
                                    'start' => -90,
                                    'end' => 90,
                                ],
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Score',
                            WidgetType::SCORE_TREND,
                            WidgetDataType::COLUMN_CHART,
                            1,
                        );
                        $this->createWidget(
                            $section,
                            'Impressions',
                            WidgetType::IMPRESSIONS,
                            WidgetDataType::COLUMN_CHART,
                        );
                        $this->createWidget($section, 'Clicks', WidgetType::CLICKS, WidgetDataType::COLUMN_CHART);
                        $this->createWidget($section, 'CTR', WidgetType::CTR, WidgetDataType::COLUMN_CHART);
                        $this->createWidget($section, 'CPC', WidgetType::CPC, WidgetDataType::COLUMN_CHART, 2);
                        $this->createWidget($section, 'Costs', WidgetType::COST, WidgetDataType::COLUMN_CHART, 2);
                        $this->createWidget(
                            $section,
                            'Conversions',
                            WidgetType::CONVERSIONS,
                            WidgetDataType::COLUMN_CHART,
                        );
                        break;
                    case SectionType::QUALITY_SCORE:
                        $this->createWidget(
                            $section,
                            'Ad Relevance - Keyword count',
                            WidgetType::COUNT,
                            WidgetDataType::PIE_CHART,
                            settings: [
                                GoogleAdAccountQualityScore::PROPERTY_FACTOR => 'Ad relevance',
                                GoogleAdAccountQualityScore::PROPERTY_METRIC => 'Keywordcount',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Ad Relevance - Impressions',
                            WidgetType::IMPRESSIONS,
                            WidgetDataType::PIE_CHART,
                            settings: [
                                GoogleAdAccountQualityScore::PROPERTY_FACTOR => 'Ad relevance',
                                GoogleAdAccountQualityScore::PROPERTY_METRIC => 'Impressions',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Ad Relevance - Clicks',
                            WidgetType::CLICKS,
                            WidgetDataType::PIE_CHART,
                            settings: [
                                GoogleAdAccountQualityScore::PROPERTY_FACTOR => 'Ad relevance',
                                GoogleAdAccountQualityScore::PROPERTY_METRIC => 'Clicks',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Expected CTR - Keyword count',
                            WidgetType::COUNT,
                            WidgetDataType::PIE_CHART,
                            settings: [
                                GoogleAdAccountQualityScore::PROPERTY_FACTOR => 'Expected CTR',
                                GoogleAdAccountQualityScore::PROPERTY_METRIC => 'Keywordcount',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Expected CTR - Impressions',
                            WidgetType::IMPRESSIONS,
                            WidgetDataType::PIE_CHART,
                            settings: [
                                GoogleAdAccountQualityScore::PROPERTY_FACTOR => 'Expected CTR',
                                GoogleAdAccountQualityScore::PROPERTY_METRIC => 'Impressions',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Expected CTR - Clicks',
                            WidgetType::CLICKS,
                            WidgetDataType::PIE_CHART,
                            settings: [
                                GoogleAdAccountQualityScore::PROPERTY_FACTOR => 'Expected CTR',
                                GoogleAdAccountQualityScore::PROPERTY_METRIC => 'Clicks',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Landing Page Experience - Keyword count',
                            WidgetType::COUNT,
                            WidgetDataType::PIE_CHART,
                            settings: [
                                GoogleAdAccountQualityScore::PROPERTY_FACTOR => 'Landing page experience',
                                GoogleAdAccountQualityScore::PROPERTY_METRIC => 'Keywordcount',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Landing Page Experience - Impressions',
                            WidgetType::IMPRESSIONS,
                            WidgetDataType::PIE_CHART,
                            settings: [
                                GoogleAdAccountQualityScore::PROPERTY_FACTOR => 'Landing page experience',
                                GoogleAdAccountQualityScore::PROPERTY_METRIC => 'Impressions',
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Landing Page Experience - Clicks',
                            WidgetType::CLICKS,
                            WidgetDataType::PIE_CHART,
                            settings: [
                                GoogleAdAccountQualityScore::PROPERTY_FACTOR => 'Landing page experience',
                                GoogleAdAccountQualityScore::PROPERTY_METRIC => 'Clicks',
                            ],
                        );
                        break;
                    case SectionType::COMPETITION:
                        $this->createWidget(
                            $section,
                            'Impression Share',
                            WidgetType::COMPARISON,
                            WidgetDataType::LINE_CHART,
                            settings: [
                                GetAuctionInsightsData::SETTING_METRIC =>
                                    GoogleAdAccountAuctionInsight::PROPERTY_IMPRESSION_SHARE,
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Absolute top of page rate',
                            WidgetType::COMPARISON,
                            WidgetDataType::LINE_CHART,
                            settings: [
                                GetAuctionInsightsData::SETTING_METRIC =>
                                    GoogleAdAccountAuctionInsight::PROPERTY_ABSOLUTE_TOP_OF_PAGE_RATE,
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Top of page rate',
                            WidgetType::COMPARISON,
                            WidgetDataType::LINE_CHART,
                            settings: [
                                GetAuctionInsightsData::SETTING_METRIC =>
                                    GoogleAdAccountAuctionInsight::PROPERTY_TOP_OF_PAGE_RATE,
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Position above rate',
                            WidgetType::COMPARISON,
                            WidgetDataType::LINE_CHART,
                            settings: [
                                GetAuctionInsightsData::SETTING_METRIC =>
                                    GoogleAdAccountAuctionInsight::PROPERTY_POSITION_ABOVE_RATE,
                            ],
                        );
                        $this->createWidget(
                            $section,
                            'Overlap rate',
                            WidgetType::COMPARISON,
                            WidgetDataType::LINE_CHART,
                            settings: [
                                GetAuctionInsightsData::SETTING_METRIC =>
                                    GoogleAdAccountAuctionInsight::PROPERTY_OVERLAP_RATE,
                            ],
                        );
                        break;
                }
            case PageType::METRIC_TREE:
                switch ($section->getType()) {
                    case SectionType::METRIC_INFLUENCE:
                        $this->createWidget(
                            $section,
                            $section->getType()->toReadableString(),
                            WidgetType::INFLUENCE,
                            WidgetDataType::FLOW_DIAGRAM,
                        );
                        break;
                }
        }
    }

    private function createWidget(
        Section $section,
        string $name,
        WidgetType $type,
        WidgetDataType $dataType,
        int $formatDecimals = 0,
        int $colSpan = 1,
        ?array $settings = null,
    ): void {
        /** @var Widget $widget */
        $widget = $section->widgets()->create([
            Widget::PROPERTY_NAME => $name,
            Widget::PROPERTY_TYPE => $type,
            Widget::PROPERTY_DATA_TYPE => $dataType,
            Widget::PROPERTY_FORMAT_DECIMALS => $formatDecimals,
            Widget::PROPERTY_COL_SPAN => $colSpan,
            Widget::PROPERTY_SETTINGS => $settings,
        ]);

        /** @var KpiDto[] $kpis */
        $kpis = array_filter($this->kpis, fn(KpiDto $kpi) => $kpi->getType() === $type);

        foreach ($kpis as $kpi) {
            foreach ($kpi->getEntries() as $entry) {
                $widget->kpis()->create([
                    WidgetKpi::PROPERTY_TARGET => $entry->getTarget(),
                    WidgetKpi::PROPERTY_YEAR => $entry->getYear(),
                    WidgetKpi::PROPERTY_UNIT => $entry->getUnit(),
                ]);
            }
        }
    }
}
