<?php

namespace App\Domain\Dashboard\Actions;

use App\Domain\Dashboard\Support\Enums\PageType;
use App\Domain\Dashboard\Support\Enums\SectionType;
use App\Domain\Dashboard\Support\Enums\WidgetType;

class GetDashboardConfiguration
{
    public function execute(): array
    {
        return [
            PageType::EXECUTIVE->value => [
                SectionType::EXECUTIVE_SUMMARY->value => [
                    WidgetType::TOTAL_CONVERSION_VALUE->value => [
                        'has_kpis' => true,
                    ],
                    WidgetType::ROAS->value => [
                        'has_kpis' => true,
                    ],
                ],
                SectionType::CAMPAIGN_OVERVIEW->value => [],
                SectionType::PERFORMANCE_TRENDS->value => [],
                SectionType::EXECUTIVE_OVERVIEW->value => [],
            ],
            PageType::INSIGHTS->value => [],
            PageType::TRENDS->value => [
                SectionType::PERFORMANCE_TRENDS->value => [],
                SectionType::PERIOD_ANALYSIS->value => [],
                SectionType::QUALITY_SCORE->value => [],
                SectionType::KEYWORD_QUALITY_SCORE->value => [],
                SectionType::COMPETITION->value => [],
            ],
            PageType::METRIC_TREE->value => [
                SectionType::METRIC_INFLUENCE->value => [],
            ],
        ];
    }
}
