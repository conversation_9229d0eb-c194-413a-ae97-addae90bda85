<?php

namespace App\Domain\Dashboard\Actions;

use App\Domain\Dashboard\Models\Dashboard;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class GetSignedDashboardUrl
{
    public function execute(Dashboard $dashboard): string
    {
        $url = Url::signedRoute('api.recipient.auth.login', $dashboard->getSlug(), absolute: false);

        return sprintf(
            '%s/#/%s?&signature=%s',
            $this->getUrl(),
            $dashboard->getSlug(),
            Str::afterLast($url, 'signature='),
        );
    }

    private function getUrl(): string
    {
        //        $settings = $this->link->getAccount()?->getWhitelabelSettings();
        //
        //        if ($settings?->getCname() && $settings?->getZeroSslStatus() === ZeroSslStatus::ISSUED) {
        //            return sprintf('https://%s', $settings->getCname());
        //        }

        return config('eaglo.recipient.url');
    }
}
