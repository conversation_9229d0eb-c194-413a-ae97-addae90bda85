<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\PeriodAnalysisCategoryDto;
use App\Domain\Dashboard\Dto\Widget\PeriodAnalysisDto;
use App\Domain\Dashboard\Dto\Widget\PeriodAnalysisColumnDto;
use App\Domain\Dashboard\Dto\Widget\PeriodAnalysisRowDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Dashboard\Support\Enums\WidgetAccuracy;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Support\Actions\FormatValue;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class GetTrendsPeriodAnalysisData
{
    private Widget $widget;
    private WidgetStatisticsFilter $filter;

    public const SETTING_DECIMALS = 'decimals';
    public const SETTING_PREFIX = 'prefix';
    public const SETTING_SUFFIX = 'suffix';

    public function __construct(private readonly array $fields = []) {}

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): PeriodAnalysisDto
    {
        if (!$filter->getAccuracy()) {
            $filter->setAccuracy(WidgetAccuracy::WEEK);
        }

        $this->widget = $widget;
        $this->filter = $filter;

        $dto = (new PeriodAnalysisDto())->setHeaders([
            'Period',
            'Current',
            'Previous period',
            'Change %',
            'Previous year',
            'YoY change %',
        ]);

        $rows = [];

        foreach ($this->getStatistics($filter->getStartDate(), $filter->getEndDate()) as $statistic) {
            $dateParts = explode('-', $statistic[GoogleAdCampaignInsight::PROPERTY_DATE]);

            $date = match ($filter->getAccuracy()) {
                WidgetAccuracy::WEEK, WidgetAccuracy::DAY => Carbon::create()->setISODate($dateParts[0], $dateParts[1]),
                WidgetAccuracy::MONTH => Carbon::create()->setYear((int) $dateParts[0])->setMonth((int) $dateParts[1]),
                WidgetAccuracy::QUARTER => Carbon::create()
                    ->setYear((int) $dateParts[0])
                    ->addQuarters((int) explode('Q', $dateParts[1])[1] - 1),
                WidgetAccuracy::YEAR => Carbon::create()->setYear((int) $dateParts[0]),
            };

            $previousPeriod = $this->getPreviousPeriod(
                match ($filter->getAccuracy()) {
                    WidgetAccuracy::WEEK, WidgetAccuracy::DAY => $date->copy()->subWeek(),
                    WidgetAccuracy::MONTH => $date->copy()->subMonth(),
                    WidgetAccuracy::QUARTER => $date->copy()->subQuarter(),
                    WidgetAccuracy::YEAR => $date->copy()->subYear(),
                },
            );

            $previousYear = $this->getPreviousPeriod($date->copy()->subYear());

            $previousPeriod = $this->getStatistics($previousPeriod[0], $previousPeriod[1]);
            $previousYear = $this->getStatistics($previousYear[0], $previousYear[1]);

            foreach ($this->fields as $field => $settings) {
                $columns = [];

                $columns[] = (new PeriodAnalysisColumnDto())
                    ->setValue($statistic[GoogleAdCampaignInsight::PROPERTY_DATE])
                    ->setFormattedValue($statistic[GoogleAdCampaignInsight::PROPERTY_DATE]);

                $columns[] = (new PeriodAnalysisColumnDto())
                    ->setValue($statistic[$field])
                    ->setFormattedValue(
                        app(FormatValue::class)->execute(
                            $statistic[$field],
                            Arr::get($settings, self::SETTING_DECIMALS, 0),
                        ),
                    )
                    ->setPrefix(Arr::get($settings, self::SETTING_PREFIX))
                    ->setSuffix(Arr::get($settings, self::SETTING_SUFFIX));

                if ($previousPeriod?->count() > 0) {
                    $totalPreviousPeriod = $previousPeriod->sum($field);

                    $columns[] = (new PeriodAnalysisColumnDto())
                        ->setValue($totalPreviousPeriod)
                        ->setFormattedValue(
                            app(FormatValue::class)->execute(
                                $totalPreviousPeriod,
                                Arr::get($settings, self::SETTING_DECIMALS, 0),
                            ),
                        )
                        ->setPrefix(Arr::get($settings, self::SETTING_PREFIX))
                        ->setSuffix(Arr::get($settings, self::SETTING_SUFFIX));

                    if ((int) $totalPreviousPeriod === 0 || (int) $statistic[$field] === 0) {
                        $diffPeriods = 0;
                    } else {
                        $diffPeriods = (($statistic[$field] - $totalPreviousPeriod) / $totalPreviousPeriod) * 100;
                    }

                    $columns[] = (new PeriodAnalysisColumnDto())
                        ->setValue($diffPeriods)
                        ->setFormattedValue(app(FormatValue::class)->execute($diffPeriods, 1))
                        ->setSuffix('%')
                        ->setStyling(sprintf('color: %s', $diffPeriods > 0 ? '#22c55e' : '#ef4444'));
                } else {
                    $columns[] = (new PeriodAnalysisColumnDto())->setValue('N/A')->setFormattedValue('N/A');
                    $columns[] = (new PeriodAnalysisColumnDto())->setValue('N/A')->setFormattedValue('N/A');
                }

                if ($previousYear?->count() > 0) {
                    $totalPreviousYear = $previousYear->sum($field);

                    $columns[] = (new PeriodAnalysisColumnDto())
                        ->setValue($totalPreviousYear)
                        ->setFormattedValue(
                            app(FormatValue::class)->execute(
                                $totalPreviousYear,
                                Arr::get($settings, self::SETTING_DECIMALS, 0),
                            ),
                        )
                        ->setPrefix(Arr::get($settings, self::SETTING_PREFIX))
                        ->setSuffix(Arr::get($settings, self::SETTING_SUFFIX));

                    if ((int) $totalPreviousYear === 0 || (int) $statistic[$field] === 0) {
                        $diffYears = 0;
                    } else {
                        $diffYears = (($statistic[$field] - $totalPreviousYear) / $totalPreviousYear) * 100;
                    }

                    $columns[] = (new PeriodAnalysisColumnDto())
                        ->setValue($diffYears)
                        ->setFormattedValue(app(FormatValue::class)->execute($diffYears, 1))
                        ->setSuffix('%')
                        ->setStyling(sprintf('color: %s', $diffYears > 0 ? '#22c55e' : '#ef4444'));
                } else {
                    $columns[] = (new PeriodAnalysisColumnDto())->setValue('N/A')->setFormattedValue('N/A');
                    $columns[] = (new PeriodAnalysisColumnDto())->setValue('N/A')->setFormattedValue('N/A');
                }

                $rows[$field][] = (new PeriodAnalysisRowDto())->setColumns($columns);
            }
        }

        $categories = [];

        foreach ($rows as $category => $row) {
            $categories[] = (new PeriodAnalysisCategoryDto())->setName($category)->setRows($row);
        }

        $dto->setCategories($categories);

        return $dto;
    }

    private function getStatistics(?Carbon $startDate = null, ?Carbon $endDate = null): Collection
    {
        $dateSelector = $this->getDateFormat();

        $baseQuery = GoogleAdCampaignInsight::query();

        foreach ($this->fields as $field => $settings) {
            $baseQuery->selectRaw(sprintf('SUM(%1$s) as %1$s', $field));
        }

        $baseQuery
            ->selectRaw(sprintf('%s as %s', $dateSelector, GoogleAdCampaignInsight::PROPERTY_DATE))
            ->groupByRaw($dateSelector);

        $baseQuery
            ->orderBy(GoogleAdCampaignInsight::PROPERTY_DATE, 'DESC')
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget));

        if ($startDate) {
            $baseQuery->where(GoogleAdCampaignInsight::PROPERTY_DATE, '>=', $startDate);
        }

        if ($endDate) {
            $baseQuery->where(GoogleAdCampaignInsight::PROPERTY_DATE, '<=', $endDate);
        }

        return $baseQuery->get();
    }

    private function getDateFormat(): string
    {
        return match ($this->filter->getAccuracy()) {
            WidgetAccuracy::WEEK, WidgetAccuracy::DAY => 'CONCAT(DATE_FORMAT(' .
                GoogleAdCampaignInsight::PROPERTY_DATE .
                ', "%Y"), "-", LPAD(WEEK(' .
                GoogleAdCampaignInsight::PROPERTY_DATE .
                ', 1), 2, "0"))',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(' . GoogleAdCampaignInsight::PROPERTY_DATE . ', "%Y-%m")',
            WidgetAccuracy::QUARTER => 'CONCAT(DATE_FORMAT(' .
                GoogleAdCampaignInsight::PROPERTY_DATE .
                ', "%Y"), "-Q", QUARTER(' .
                GoogleAdCampaignInsight::PROPERTY_DATE .
                '))',
            WidgetAccuracy::YEAR => 'DATE_FORMAT(' . GoogleAdCampaignInsight::PROPERTY_DATE . ', "%Y")',
        };
    }

    private function getPreviousPeriod(Carbon $date): array
    {
        $dates = [];

        switch ($this->filter->getAccuracy()) {
            case WidgetAccuracy::WEEK:
            case WidgetAccuracy::DAY:
                $dates = [$date->copy()->startOfWeek(), $date->copy()->endOfWeek()];
                break;
            case WidgetAccuracy::MONTH:
                $dates = [$date->copy()->startOfMonth(), $date->copy()->endOfMonth()];
                break;
            case WidgetAccuracy::QUARTER:
                $dates = [$date->copy()->startOfQuarter(), $date->copy()->endOfQuarter()];
                break;
            case WidgetAccuracy::YEAR:
                $dates = [$date->copy()->startOfYear(), $date->copy()->endOfYear()];
                break;
        }

        return $dates;
    }
}
