<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\FlowDiagramConnectionDto;
use App\Domain\Dashboard\Dto\Widget\FlowDiagramDto;
use App\Domain\Dashboard\Dto\Widget\FlowDiagramEntryDto;
use App\Domain\Dashboard\Dto\Widget\FlowDiagramEntryPositonDto;
use App\Domain\Dashboard\Dto\Widget\FlowDiagramGroupDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Support\Actions\FormatValue;
use Carbon\CarbonInterface;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class GetMetricTreeInfluenceData
{
    private Widget $widget;
    private WidgetStatisticsFilter $filter;

    public const FIELD_SEARCH_VOLUME = 'search_volume';
    public const FIELD_LOST_IS_RANK = 'lost_IS_rank';
    public const FIELD_CONVERSION_VALUE = 'conversion_value';
    public const FIELD_CONVERSION_PERCENTAGE = 'conversion_percentage';
    public const FIELD_CPA = 'CPA';

    public const GROUP_BEFORE_THE_CLICK = 'before_the_click';
    public const GROUP_CLICK_TAKES_PLACE = 'click_takes_place';
    public const GROUP_AFTER_THE_CLICK = 'after_the_click';

    public const X_MULTIPLIER = 210;
    public const Y_MULTIPLIER = 140;

    public const SETTING_SUFFIX = 'suffix';
    public const SETTING_PREFIX = 'prefix';
    public const SETTING_DECIMALS = 'decimals';

    public const ENTRY_WIDTH = 140;

    public const GROUPS = [
        self::GROUP_BEFORE_THE_CLICK => 'Before the click',
        self::GROUP_CLICK_TAKES_PLACE => 'Click takes place',
        self::GROUP_AFTER_THE_CLICK => 'After the click',
    ];

    public const CONNECTIONS = [
        [GoogleAdCampaignInsight::PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE => self::FIELD_LOST_IS_RANK],
        [GoogleAdCampaignInsight::PROPERTY_SEARCH_TOP_IMPRESSION_SHARE => self::FIELD_LOST_IS_RANK],
        [self::FIELD_LOST_IS_RANK => GoogleAdCampaignInsight::PROPERTY_SEARCH_IMPRESSION_SHARE],
        [self::FIELD_SEARCH_VOLUME => GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS],
        [GoogleAdCampaignInsight::PROPERTY_SEARCH_IMPRESSION_SHARE => GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS],
        [
            GoogleAdCampaignInsight::PROPERTY_CTR =>
                GoogleAdCampaignInsight::PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE,
        ],
        [GoogleAdCampaignInsight::PROPERTY_CTR => GoogleAdCampaignInsight::PROPERTY_CLICKS],
        [
            GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPC =>
                GoogleAdCampaignInsight::PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE,
        ],
        [GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPC => GoogleAdCampaignInsight::PROPERTY_SPEND],
        [GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS => GoogleAdCampaignInsight::PROPERTY_CLICKS],
        [GoogleAdCampaignInsight::PROPERTY_CLICKS => GoogleAdCampaignInsight::PROPERTY_CONVERSIONS],
        [GoogleAdCampaignInsight::PROPERTY_CLICKS => GoogleAdCampaignInsight::PROPERTY_SPEND],
        [GoogleAdCampaignInsight::PROPERTY_SPEND => self::FIELD_CPA],
        [self::FIELD_CONVERSION_PERCENTAGE => GoogleAdCampaignInsight::PROPERTY_CONVERSIONS],
        [GoogleAdCampaignInsight::PROPERTY_CONVERSIONS => self::FIELD_CPA],
        [GoogleAdCampaignInsight::PROPERTY_CONVERSIONS => self::FIELD_CONVERSION_VALUE],
        [self::FIELD_CONVERSION_VALUE => GoogleAdCampaignInsight::PROPERTY_ROAS],
        [GoogleAdCampaignInsight::PROPERTY_SPEND => GoogleAdCampaignInsight::PROPERTY_ROAS],
    ];

    public const GROUP_CONNECTIONS = [
        self::GROUP_BEFORE_THE_CLICK => [
            [
                GoogleAdCampaignInsight::PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE,
                GoogleAdCampaignInsight::PROPERTY_SEARCH_TOP_IMPRESSION_SHARE,
            ],
            [self::FIELD_LOST_IS_RANK],
            [self::FIELD_SEARCH_VOLUME, GoogleAdCampaignInsight::PROPERTY_SEARCH_IMPRESSION_SHARE],
            [GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS],
        ],
        self::GROUP_CLICK_TAKES_PLACE => [
            [GoogleAdCampaignInsight::PROPERTY_CTR, GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPC],
            [GoogleAdCampaignInsight::PROPERTY_CLICKS, GoogleAdCampaignInsight::PROPERTY_SPEND],
        ],
        self::GROUP_AFTER_THE_CLICK => [
            [self::FIELD_CONVERSION_PERCENTAGE],
            [self::FIELD_CPA],
            [GoogleAdCampaignInsight::PROPERTY_CONVERSIONS],
            [self::FIELD_CONVERSION_VALUE, GoogleAdCampaignInsight::PROPERTY_ROAS],
        ],
    ];

    public const SETTINGS = [
        GoogleAdCampaignInsight::PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE => [
            self::SETTING_SUFFIX => '%',
            self::SETTING_DECIMALS => 2,
        ],
        GoogleAdCampaignInsight::PROPERTY_SEARCH_TOP_IMPRESSION_SHARE => [
            self::SETTING_SUFFIX => '%',
            self::SETTING_DECIMALS => 2,
        ],
        GoogleAdCampaignInsight::PROPERTY_SEARCH_IMPRESSION_SHARE => [
            self::SETTING_SUFFIX => '%',
            self::SETTING_DECIMALS => 2,
        ],
        self::FIELD_LOST_IS_RANK => [
            self::SETTING_SUFFIX => '%',
            self::SETTING_DECIMALS => 2,
        ],
        GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPC => [
            self::SETTING_PREFIX => '€',
            self::SETTING_DECIMALS => 2,
        ],
        GoogleAdCampaignInsight::PROPERTY_SPEND => [
            self::SETTING_PREFIX => '€',
            self::SETTING_DECIMALS => 2,
        ],
        self::FIELD_CONVERSION_VALUE => [
            self::SETTING_PREFIX => '€',
            self::SETTING_DECIMALS => 2,
        ],
        GoogleAdCampaignInsight::PROPERTY_ROAS => [
            self::SETTING_SUFFIX => '%',
            self::SETTING_DECIMALS => 2,
        ],
        self::FIELD_CONVERSION_PERCENTAGE => [
            self::SETTING_SUFFIX => '%',
            self::SETTING_DECIMALS => 2,
        ],
        self::FIELD_CPA => [
            self::SETTING_PREFIX => '€',
            self::SETTING_DECIMALS => 2,
        ],
    ];

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): FlowDiagramDto
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $statistics = $this->getStatistics($filter->getStartDate(), $filter->getEndDate());

        $comparison = [];

        if ($filter->getComparableStartDate() && $filter->getComparableEndDate()) {
            $comparison = $this->getStatistics($filter->getComparableStartDate(), $filter->getComparableEndDate());
        }

        $entries = [];

        foreach ($statistics as $entry => $value) {
            $totalFields = 0;
            foreach (self::GROUP_CONNECTIONS as $group => $rows) {
                $y = self::Y_MULTIPLIER - 80;
                foreach ($rows as $rowIndex => $fields) {
                    $x = self::X_MULTIPLIER * $totalFields;
                    foreach ($fields as $fieldIndex => $field) {
                        if ($field !== $entry) {
                            $x += self::X_MULTIPLIER;
                            continue;
                        }

                        $position = (new FlowDiagramEntryPositonDto())->setX($x)->setY($y);

                        $comparisonValue = Arr::get($comparison, $field);

                        $entries[] = (new FlowDiagramEntryDto())
                            ->setValue($value ?? 0)
                            ->setValueFormatted(
                                app(FormatValue::class)->execute(
                                    $value ?? 0,
                                    Arr::get(self::SETTINGS, implode('.', [$field, self::SETTING_DECIMALS]), 0),
                                ),
                            )
                            ->setGroupId($group)
                            ->setPosition($position)
                            ->setComparisonValue($comparisonValue)
                            ->setComparisonFormatted(
                                $comparisonValue
                                    ? app(FormatValue::class)->execute(
                                        $comparisonValue,
                                        Arr::get(self::SETTINGS, implode('.', [$field, self::SETTING_DECIMALS]), 0),
                                    )
                                    : null,
                            )
                            ->setInputId($this->getInputId($field))
                            ->setOutputId($this->getOutputId($field))
                            ->setName(Str::title(str_replace('_', ' ', $field)))
                            ->setPrefix(Arr::get(self::SETTINGS, implode('.', [$field, self::SETTING_PREFIX])))
                            ->setSuffix(Arr::get(self::SETTINGS, implode('.', [$field, self::SETTING_SUFFIX])));
                        $x += self::X_MULTIPLIER;
                    }
                    $y += self::Y_MULTIPLIER;
                }

                $totalFields += max(array_map('count', self::GROUP_CONNECTIONS[$group]));
            }
        }

        return (new FlowDiagramDto())
            ->setEntries($entries)
            ->setConnections($this->getConnections())
            ->setGroups($this->getGroups());
    }

    private function getStatistics(?CarbonInterface $startDate = null, ?CarbonInterface $endDate = null): array
    {
        $baseQuery = GoogleAdCampaignInsight::query()
            ->selectRaw(
                sprintf('AVG(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_SEARCH_ABSOLUTE_TOP_IMPRESSION_SHARE),
            )
            ->selectRaw(sprintf('AVG(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_SEARCH_TOP_IMPRESSION_SHARE))
            ->selectRaw(sprintf('AVG(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_SEARCH_IMPRESSION_SHARE))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_CLICKS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_CONVERSIONS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_CTR))
            ->selectRaw(sprintf('AVG(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPC))
            ->selectRaw(sprintf('AVG(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_ROAS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_SPEND))
            ->selectRaw(
                sprintf(
                    '(SUM(%s) / SUM(%s)) as %s',
                    GoogleAdCampaignInsight::PROPERTY_SPEND,
                    GoogleAdCampaignInsight::PROPERTY_CONVERSIONS,
                    self::FIELD_CPA,
                ),
            )
            ->selectRaw(
                sprintf(
                    '(SUM(%s) / SUM(%s)) * 100 as %s',
                    GoogleAdCampaignInsight::PROPERTY_CONVERSIONS,
                    GoogleAdCampaignInsight::PROPERTY_CLICKS,
                    self::FIELD_CONVERSION_PERCENTAGE,
                ),
            )
            ->selectRaw(
                sprintf(
                    'SUM(%1$s) * (SUM(%2$s) / SUM(%1$s)) as %3$s',
                    GoogleAdCampaignInsight::PROPERTY_CONVERSIONS,
                    GoogleAdCampaignInsight::PROPERTY_SPEND,
                    self::FIELD_CONVERSION_VALUE,
                ),
            )
            ->selectRaw(
                sprintf(
                    '(SUM(%s) / AVG(%s)) * 100 as %s',
                    GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS,
                    GoogleAdCampaignInsight::PROPERTY_SEARCH_IMPRESSION_SHARE,
                    self::FIELD_SEARCH_VOLUME,
                ),
            )
            ->selectRaw(
                sprintf(
                    '100 - AVG(%s) as %s',
                    GoogleAdCampaignInsight::PROPERTY_SEARCH_IMPRESSION_SHARE,
                    self::FIELD_LOST_IS_RANK,
                ),
            )
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget));

        if ($startDate) {
            $baseQuery->where('date', '>=', $startDate);
        }

        if ($endDate) {
            $baseQuery->where('date', '<=', $endDate);
        }

        return $baseQuery->first()->toArray();
    }

    private function getGroups(): array
    {
        $groups = [];

        $totalColumnCount = 0;

        foreach (self::GROUPS as $id => $name) {
            $columnCount = max(array_map('count', self::GROUP_CONNECTIONS[$id]));
            $index = array_search($id, array_keys(self::GROUPS));

            $groups[] = (new FlowDiagramGroupDto())
                ->setId($id)
                ->setName($name)
                ->setWidth(self::X_MULTIPLIER * ($columnCount - 1) + self::ENTRY_WIDTH)
                ->setX($index === 0 ? 0 : self::X_MULTIPLIER * $totalColumnCount);

            $totalColumnCount += $columnCount;
        }

        return $groups;
    }

    private function getConnections(): array
    {
        $connections = [];

        foreach (self::CONNECTIONS as $entries) {
            foreach ($entries as $output => $input) {
                $connections[] = (new FlowDiagramConnectionDto())
                    ->setOutputId($this->getOutputId($output))
                    ->setInputId($this->getInputId($input));
            }
        }

        return $connections;
    }

    private function getOutputId(string $field): string
    {
        return sprintf('%s_output', $field);
    }

    private function getInputId(string $field): string
    {
        return sprintf('%s_input', $field);
    }
}
