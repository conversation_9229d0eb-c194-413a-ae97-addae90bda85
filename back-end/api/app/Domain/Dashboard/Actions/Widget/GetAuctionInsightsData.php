<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\ChartDto;
use App\Domain\Dashboard\Dto\Widget\ChartSeriesDto;
use App\Domain\Dashboard\Dto\Widget\ChartValueDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Dashboard\Support\Enums\WidgetAccuracy;
use App\Domain\Google\Models\GoogleAdAccountAuctionInsight;
use App\Domain\Google\Models\GoogleAdAccountKeywordQualityScore;
use App\Domain\Support\Actions\FormatValue;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class GetAuctionInsightsData
{
    public const SETTING_METRIC = 'metric';

    private Widget $widget;
    private WidgetStatisticsFilter $filter;

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): ChartDto
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $series = [];

        $statistics = $this->getStatistics();

        foreach ($statistics as $statistic) {
            $filtered = array_filter(
                $series,
                fn(ChartSeriesDto $dto) => $dto->getTitle() ===
                    $statistic[GoogleAdAccountAuctionInsight::PROPERTY_DOMAIN],
            );

            $index = Arr::first(array_keys($filtered));
            $entry = Arr::first($filtered);

            if (!$entry) {
                $entry = (new ChartSeriesDto())
                    ->setTitle($statistic[GoogleAdAccountAuctionInsight::PROPERTY_DOMAIN])
                    ->setValues([]);
            }

            $value = $statistic[$this->widget->getSettings()[self::SETTING_METRIC]] ?? 0;

            $dto = (new ChartValueDto())
                ->setValue($value)
                ->setValueFormatted(app(FormatValue::class)->execute($value, 1))
                ->setSuffix('%')
                ->setDate(Carbon::parse($statistic[GoogleAdAccountAuctionInsight::PROPERTY_DATE]));

            $entry->setValues(array_merge($entry->getValues(), [$dto]));

            if ($index !== null) {
                $series[$index] = $entry;
            } else {
                $series[] = $entry;
            }
        }

        foreach ($series as $entry) {
            if (!$filter->getStartDate() && !$filter->getEndDate()) {
                continue;
            }

            $values = app(GenerateMissingChartDateValues::class)->execute(
                $entry->getValues(),
                $filter->getStartDate()->copy(),
                $filter->getEndDate()->copy(),
            );

            $entry->setValues($values);
        }

        return (new ChartDto())->setSeries($series)->setAccuracy($filter->getAccuracy())->setName($widget->getName());
    }

    private function getStatistics(): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY, WidgetAccuracy::WEEK => 'DATE_FORMAT(' .
                GoogleAdAccountAuctionInsight::PROPERTY_DATE .
                ', "%Y-%m-%d")',
            WidgetAccuracy::MONTH, WidgetAccuracy::QUARTER, WidgetAccuracy::YEAR => 'DATE_FORMAT(' .
                GoogleAdAccountAuctionInsight::PROPERTY_DATE .
                ', "%Y-%m")',
        };

        $baseQuery = GoogleAdAccountAuctionInsight::query()
            ->selectRaw(sprintf('%s as %s', $dateSelector, GoogleAdAccountAuctionInsight::PROPERTY_DATE))
            ->selectRaw(GoogleAdAccountAuctionInsight::PROPERTY_DOMAIN)
            ->selectRaw(sprintf('AVG(%1$s) as %1$s', $this->widget->getSettings()[self::SETTING_METRIC]))
            ->groupByRaw(GoogleAdAccountAuctionInsight::PROPERTY_DOMAIN)
            ->groupByRaw($dateSelector)
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget));

        if ($this->filter->getStartDate()) {
            $baseQuery->where(GoogleAdAccountKeywordQualityScore::PROPERTY_DATE, '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where(GoogleAdAccountKeywordQualityScore::PROPERTY_DATE, '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }
}
