<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\RadialChartDto;
use App\Domain\Dashboard\Dto\Widget\RadialChartSeriesDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Dashboard\Models\WidgetKpi;
use App\Domain\Dashboard\Support\Enums\WidgetKpiUnit;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Support\Actions\FormatValue;
use Illuminate\Database\Eloquent\Collection;

class GetTotalConversionValueData
{
    private WidgetStatisticsFilter $filter;
    private Widget $widget;

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): RadialChartDto
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $kpi = $this->getKpiForYear();

        $dto = new RadialChartDto();

        $max = $kpi->getTarget();

        if ($kpi->getUnit() === WidgetKpiUnit::AMOUNT) {
            $max = ($kpi->getTarget() / 365) * $filter->getStartDate()->diffInDays($filter->getEndDate());
        }

        $dto->setMax(app(FormatValue::class)->execute($max, 2));
        $dto->setMin(0);
        $dto->setPrefix('€');

        $statistics = $this->getStatistics();

        $costPerConversion =
            $statistics[GoogleAdCampaignInsight::PROPERTY_SPEND] /
            $statistics[GoogleAdCampaignInsight::PROPERTY_CONVERSIONS];

        $conversionValue = $statistics[GoogleAdCampaignInsight::PROPERTY_CONVERSIONS] * $costPerConversion;

        $series = (new RadialChartSeriesDto())
            ->setPercentage(($conversionValue / $max) * 100)
            ->setFormattedValue(app(FormatValue::class)->execute($conversionValue, 2))
            ->setPrefix('€');

        $dto->setSeries([$series]);

        return $dto;
    }

    private function getKpiForYear(): ?WidgetKpi
    {
        return WidgetKpi::query()
            ->where(WidgetKpi::PROPERTY_WIDGET_ID, $this->widget->getId())
            ->where(WidgetKpi::PROPERTY_YEAR, $this->filter->getStartDate()?->year ?? now()->year)
            ->first();
    }

    private function getStatistics(): array
    {
        $baseQuery = GoogleAdCampaignInsight::query()
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_SPEND))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_CONVERSIONS))
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget));

        if ($this->filter->getStartDate()) {
            $baseQuery->where(GoogleAdCampaignInsight::PROPERTY_DATE, '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where(GoogleAdCampaignInsight::PROPERTY_DATE, '<=', $this->filter->getEndDate());
        }

        return $baseQuery->first()->toArray();
    }
}
