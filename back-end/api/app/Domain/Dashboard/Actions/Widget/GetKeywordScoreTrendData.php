<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\ChartDto;
use App\Domain\Dashboard\Dto\Widget\ChartSeriesDto;
use App\Domain\Dashboard\Dto\Widget\ChartValueDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Dashboard\Support\Enums\WidgetAccuracy;
use App\Domain\Google\Models\GoogleAdAccountKeywordQualityScore;
use App\Domain\Support\Actions\FormatValue;
use Illuminate\Support\Collection;

class GetKeywordScoreTrendData
{
    private Widget $widget;
    private WidgetStatisticsFilter $filter;

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): ChartDto
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $statistics = $this->getStatistics();

        $entries = [];

        foreach ($statistics as $statistic) {
            $dto = (new ChartValueDto())
                ->setDate($statistic[GoogleAdAccountKeywordQualityScore::PROPERTY_DATE])
                ->setValue($statistic[GoogleAdAccountKeywordQualityScore::PROPERTY_SCORE])
                ->setValueFormatted(
                    app(FormatValue::class)->execute($statistic[GoogleAdAccountKeywordQualityScore::PROPERTY_SCORE], 1),
                );

            $entries[] = $dto;
        }

        if ($filter->getStartDate() && $filter->getEndDate()) {
            $entries = app(GenerateMissingChartDateValues::class)->execute(
                $entries,
                $filter->getStartDate(),
                $filter->getEndDate(),
            );
        }

        $series = (new ChartSeriesDto())->setTitle($widget->getName())->setValues($entries);

        return (new ChartDto())
            ->setName($widget->getName())
            ->setSeries([$series])
            ->setAccuracy($filter->getAccuracy());
    }

    private function getStatistics(): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY, WidgetAccuracy::WEEK => 'DATE_FORMAT(' .
                GoogleAdAccountKeywordQualityScore::PROPERTY_DATE .
                ', "%Y-%m-%d")',
            WidgetAccuracy::MONTH, WidgetAccuracy::QUARTER, WidgetAccuracy::YEAR => 'DATE_FORMAT(' .
                GoogleAdAccountKeywordQualityScore::PROPERTY_DATE .
                ', "%Y-%m")',
        };

        $baseQuery = GoogleAdAccountKeywordQualityScore::query()
            ->selectRaw(sprintf('%s as %s', $dateSelector, GoogleAdAccountKeywordQualityScore::PROPERTY_DATE))
            ->selectRaw(
                sprintf(
                    'ROUND(SUM(%1$s * %2$s) / sum(%2$s), 1) as %3$s',
                    GoogleAdAccountKeywordQualityScore::PROPERTY_SCORE,
                    GoogleAdAccountKeywordQualityScore::PROPERTY_IMPRESSIONS,
                    GoogleAdAccountKeywordQualityScore::PROPERTY_SCORE,
                ),
            )
            ->where(GoogleAdAccountKeywordQualityScore::PROPERTY_SCORE, '>', 0)
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget))
            ->groupBy(GoogleAdAccountKeywordQualityScore::PROPERTY_DATE);

        if ($this->filter->getStartDate()) {
            $baseQuery->where(GoogleAdAccountKeywordQualityScore::PROPERTY_DATE, '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where(GoogleAdAccountKeywordQualityScore::PROPERTY_DATE, '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }
}
