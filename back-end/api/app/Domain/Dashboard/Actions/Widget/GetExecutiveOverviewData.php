<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\DataComparisonDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Support\Actions\FormatValue;
use Carbon\CarbonInterface;
use Illuminate\Support\Collection;

class GetExecutiveOverviewData
{
    private Widget $widget;

    public function __construct(protected string $field = GoogleAdCampaignInsight::PROPERTY_CLICKS) {}

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): DataComparisonDto
    {
        $this->widget = $widget;

        $currentPeriod = $this->getStatisticsForPeriod($filter->getStartDate(), $filter->getEndDate());

        $comparisonPeriod = null;

        if ($filter->getComparableStartDate() && $filter->getComparableEndDate()) {
            $comparisonPeriod = $this->getStatisticsForPeriod(
                $filter->getComparableStartDate(),
                $filter->getComparableEndDate(),
            );
        }

        $dto = new DataComparisonDto();

        $dto->setValue($currentPeriod[$this->field] ?? 0);
        $dto->setValueFormatted(
            $currentPeriod[$this->field] ? app(FormatValue::class)->execute($currentPeriod[$this->field]) : 0,
        );

        if ($comparisonPeriod) {
            $dto->setComparison($comparisonPeriod[$this->field]);
            $dto->setComparisonFormatted(
                $comparisonPeriod[$this->field]
                    ? app(FormatValue::class)->execute($comparisonPeriod[$this->field], $widget->getFormatDecimals())
                    : null,
            );
        }

        return $dto;
    }

    private function getStatisticsForPeriod(?CarbonInterface $startDate, ?CarbonInterface $endDate): array
    {
        $baseQuery = GoogleAdCampaignInsight::query()
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_CLICKS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_SPEND))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_VIDEO_VIEWS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_CONVERSIONS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_CONVERSION_RATE))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_REVENUE))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_ROAS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_PROFIT))
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget));

        if ($startDate) {
            $baseQuery->where('date', '>=', $startDate);
        }

        if ($endDate) {
            $baseQuery->where('date', '<=', $endDate);
        }

        return $baseQuery->first()->toArray();
    }
}
