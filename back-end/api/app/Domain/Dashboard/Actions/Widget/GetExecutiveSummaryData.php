<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\SummaryDto;
use App\Domain\Dashboard\Dto\Widget\SummaryFieldDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Support\Actions\FormatValue;
use Carbon\CarbonInterface;

class GetExecutiveSummaryData
{
    private Widget $widget;

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): SummaryDto
    {
        $this->widget = $widget;

        $summary = new SummaryDto();

        $currentPeriod = $this->getStatisticsForPeriod($filter->getStartDate(), $filter->getEndDate());
        $comparisonPeriod = null;

        if ($filter->getComparableStartDate() && $filter->getComparableEndDate()) {
            $comparisonPeriod = $this->getStatisticsForPeriod(
                $filter->getComparableStartDate(),
                $filter->getComparableEndDate(),
            );
        }

        if (
            $currentPeriod[GoogleAdCampaignInsight::PROPERTY_CLICKS] &&
            $currentPeriod[GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS]
        ) {
            $ctr =
                ($currentPeriod[GoogleAdCampaignInsight::PROPERTY_CLICKS] /
                    $currentPeriod[GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS]) *
                100;
        } else {
            $ctr = 0;
        }

        if ($comparisonPeriod) {
            if (
                $currentPeriod[GoogleAdCampaignInsight::PROPERTY_CLICKS] &&
                $comparisonPeriod[GoogleAdCampaignInsight::PROPERTY_CLICKS]
            ) {
                $percentage =
                    (($currentPeriod[GoogleAdCampaignInsight::PROPERTY_CLICKS] -
                        $comparisonPeriod[GoogleAdCampaignInsight::PROPERTY_CLICKS]) /
                        $comparisonPeriod[GoogleAdCampaignInsight::PROPERTY_CLICKS]) *
                    100;
            } else {
                $percentage = 0;
            }

            $text = sprintf(
                'Your campaigns performed well this month with <span class="font-semibold">%s clicks</span> (%s%s from last period) and a <span class="font-semibold">%s click-through-rate</span>.',
                $currentPeriod[GoogleAdCampaignInsight::PROPERTY_CLICKS],
                $percentage > 0 ? '+' : '-',
                app(FormatValue::class)->execute($percentage, 2) . '%',
                app(FormatValue::class)->execute($ctr, 2),
            );
        } else {
            $text = sprintf(
                'Your campaigns performed well this month with <span class="font-semibold">%s clicks</span> and a <span class="font-semibold">%s click-through-rate</span>.',
                $currentPeriod[GoogleAdCampaignInsight::PROPERTY_CLICKS],
                app(FormatValue::class)->execute($ctr, 2),
            );
        }

        $summary->setText($text);
        $summary->setFields($this->getFields($currentPeriod));

        return $summary;
    }

    private function getStatisticsForPeriod(?CarbonInterface $startDate, ?CarbonInterface $endDate): array
    {
        $baseQuery = GoogleAdCampaignInsight::query()
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_CLICKS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_SPEND))
            ->selectRaw(sprintf('SUM(%1$s) as %1$s', GoogleAdCampaignInsight::PROPERTY_CONVERSIONS))
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget));

        if ($startDate) {
            $baseQuery->where('date', '>=', $startDate);
        }

        if ($endDate) {
            $baseQuery->where('date', '<=', $endDate);
        }

        return $baseQuery->first()->toArray();
    }

    private function getFields(array $statistics): array
    {
        $fields = [];

        $spend = $statistics[GoogleAdCampaignInsight::PROPERTY_SPEND] ?? 0;
        $conversions = $statistics[GoogleAdCampaignInsight::PROPERTY_CONVERSIONS] ?? 0;

        $fields[] = (new SummaryFieldDto())
            ->setTitle('Total spend')
            ->setValue($spend)
            ->setValueFormatted(app(FormatValue::class)->execute($spend, 2))
            ->setPrefix('€');

        $fields[] = (new SummaryFieldDto())
            ->setTitle('Leads generated')
            ->setValue($conversions)
            ->setValueFormatted(app(FormatValue::class)->execute($conversions));

        $costPerConversion = 0;
        $roi = 0;

        if ($spend && $conversions) {
            $costPerConversion = $spend / $conversions;

            $roi = ($conversions - $spend) / $spend;
        }

        $fields[] = (new SummaryFieldDto())
            ->setTitle('Cost per lead')
            ->setValue($costPerConversion)
            ->setValueFormatted(app(FormatValue::class)->execute($costPerConversion, 2))
            ->setPrefix('€');

        $fields[] = (new SummaryFieldDto())
            ->setTitle('ROI')
            ->setValue($roi)
            ->setValueFormatted(app(FormatValue::class)->execute($roi, 1))
            ->setSuffix('x');

        return $fields;
    }
}
