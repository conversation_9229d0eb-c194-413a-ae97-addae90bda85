<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\PieChartDto;
use App\Domain\Dashboard\Dto\Widget\PieChartSeriesDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Google\Models\GoogleAdAccountQualityScore;
use App\Domain\Support\Actions\FormatValue;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class GetQualityScoreData
{
    private Widget $widget;
    private WidgetStatisticsFilter $filter;

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): PieChartDto
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $statistics = $this->getStatistics($filter->getStartDate(), $filter->getEndDate());

        $dto = new PieChartDto();

        $series = [];

        foreach ($statistics as $statistic) {
            /** @var GoogleAdAccountQualityScore $statistic */
            $entry = new PieChartSeriesDto();
            $entry->setLabel($statistic->getRating()->toReadableString());
            $entry->setValue($statistic->getValue());
            $entry->setFormattedValue(app(FormatValue::class)->execute($statistic->getValue()));
            $entry->setColor($statistic->getRating()->toColor());

            $series[] = $entry;
        }

        $dto->setSeries($series);

        return $dto;
    }

    private function getStatistics(?CarbonInterface $startDate = null, ?CarbonInterface $endDate = null): Collection
    {
        $factor = Arr::get($this->widget->getSettings(), GoogleAdAccountQualityScore::PROPERTY_FACTOR);
        $metric = Arr::get($this->widget->getSettings(), GoogleAdAccountQualityScore::PROPERTY_METRIC);

        /** @var Builder $query */
        $query = GoogleAdAccountQualityScore::query()
            ->selectRaw(sprintf('sum(%1$s) as %1$s', GoogleAdAccountQualityScore::PROPERTY_VALUE))
            ->selectRaw(GoogleAdAccountQualityScore::PROPERTY_RATING)
            ->groupByRaw(GoogleAdAccountQualityScore::PROPERTY_RATING)
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget));

        if ($factor) {
            $query->where(GoogleAdAccountQualityScore::PROPERTY_FACTOR, $factor);
        }

        if ($metric) {
            $query->where(GoogleAdAccountQualityScore::PROPERTY_METRIC, $metric);
        }

        if ($startDate) {
            $query->where(GoogleAdAccountQualityScore::PROPERTY_DATE, '>=', $startDate);
        }

        if ($endDate) {
            $query->where(GoogleAdAccountQualityScore::PROPERTY_DATE, '<=', $endDate);
        }

        return $query->get();
    }
}
