<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Actions\GetStatisticDateSelector;
use App\Domain\Dashboard\Dto\Widget\ChartDto;
use App\Domain\Dashboard\Dto\Widget\ChartSeriesDto;
use App\Domain\Dashboard\Dto\Widget\ChartValueDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Dashboard\Support\Enums\WidgetAccuracy;
use App\Domain\Google\Models\GoogleAdAccountKeywordQualityScore;
use App\Domain\Support\Actions\FormatValue;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class GetKeywordQualityScoreData
{
    private Widget $widget;
    private WidgetStatisticsFilter $filter;

    public const FIELD_CTR = 'CTR';
    public const FIELD_CPC = 'CPC';

    public const SETTING_DECIMALS = 'decimals';
    public const SETTING_NEEDS_FORMATTING_FOR_VALUE = 'needs_formatting_for_value';
    public const SETTING_PREFIX = 'prefix';
    public const SETTING_SUFFIX = 'suffix';

    public function __construct(private readonly array $fields, private readonly ?string $chartName = null) {}

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): ChartDto
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $statistics = $this->getStatistics();

        $chart = new ChartDto();

        $data = [];

        $anomalies = [];

        foreach ($statistics as $statistic) {
            $date = Carbon::parse($statistic[GoogleAdAccountKeywordQualityScore::PROPERTY_DATE]);

            foreach ($this->fields as $field => $settings) {
                $value = Arr::get($settings, self::SETTING_NEEDS_FORMATTING_FOR_VALUE, false)
                    ? app(FormatValue::class)->execute(
                        $statistic[$field],
                        Arr::get($settings, self::SETTING_DECIMALS, 0),
                    )
                    : $statistic[$field] ?? 0;

                $data[$field][] = (new ChartValueDto())
                    ->setValue($value)
                    ->setDate($date)
                    ->setPrefix(Arr::get($settings, self::SETTING_PREFIX))
                    ->setSuffix(Arr::get($settings, self::SETTING_SUFFIX))
                    ->setValueFormatted(
                        app(FormatValue::class)->execute(
                            $statistic[$field] ?? 0,
                            Arr::get($settings, self::SETTING_DECIMALS, 0),
                        ),
                    );
            }
        }

        $series = [];

        foreach ($data as $entry => $values) {
            if ($filter->getStartDate() && $filter->getEndDate()) {
                $values = app(GenerateMissingChartDateValues::class)->execute(
                    $values,
                    $filter->getStartDate()->copy(),
                    $filter->getEndDate()->copy(),
                );
            }

            $series[] = (new ChartSeriesDto())->setTitle($entry)->setValues($values);
        }

        $chart->setSeries($series);
        $chart->setAccuracy($filter->getAccuracy());
        $chart->setAnomalies($anomalies);
        $chart->setName($this->chartName);

        return $chart;
    }

    private function getStatistics(): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY, WidgetAccuracy::WEEK => 'DATE_FORMAT(' .
                GoogleAdAccountKeywordQualityScore::PROPERTY_DATE .
                ', "%Y-%m-%d")',
            WidgetAccuracy::MONTH, WidgetAccuracy::QUARTER, WidgetAccuracy::YEAR => 'DATE_FORMAT(' .
                GoogleAdAccountKeywordQualityScore::PROPERTY_DATE .
                ', "%Y-%m")',
        };

        $baseQuery = GoogleAdAccountKeywordQualityScore::query();

        $baseQuery
            ->selectRaw(sprintf('sum(%1$s) as %1$s', GoogleAdAccountKeywordQualityScore::PROPERTY_IMPRESSIONS))
            ->selectRaw(sprintf('sum(%1$s) as %1$s', GoogleAdAccountKeywordQualityScore::PROPERTY_CLICKS))
            ->selectRaw(sprintf('sum(%1$s) as %1$s', GoogleAdAccountKeywordQualityScore::PROPERTY_COST))
            ->selectRaw(sprintf('sum(%1$s) as %1$s', GoogleAdAccountKeywordQualityScore::PROPERTY_CONVERSIONS))
            ->selectRaw(
                sprintf(
                    'sum(%s) / sum(%s) * 100 as %s',
                    GoogleAdAccountKeywordQualityScore::PROPERTY_CLICKS,
                    GoogleAdAccountKeywordQualityScore::PROPERTY_IMPRESSIONS,
                    self::FIELD_CTR,
                ),
            )
            ->selectRaw(
                sprintf(
                    'sum(%s) / sum(%s) as %s',
                    GoogleAdAccountKeywordQualityScore::PROPERTY_COST,
                    GoogleAdAccountKeywordQualityScore::PROPERTY_CLICKS,
                    self::FIELD_CPC,
                ),
            )
            ->selectRaw(sprintf('%s as %s', $dateSelector, GoogleAdAccountKeywordQualityScore::PROPERTY_DATE))
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget))
            ->groupByRaw($dateSelector);

        if ($this->filter->getStartDate()) {
            $baseQuery->where(GoogleAdAccountKeywordQualityScore::PROPERTY_DATE, '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where(GoogleAdAccountKeywordQualityScore::PROPERTY_DATE, '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }
}
