<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\RadialChartDto;
use App\Domain\Dashboard\Dto\Widget\RadialChartSeriesDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Google\Models\GoogleAdAccountKeywordQualityScore;
use App\Domain\Support\Actions\FormatValue;

class GetAccountQualityScore
{
    private Widget $widget;
    private WidgetStatisticsFilter $filter;

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): RadialChartDto
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $dto = new RadialChartDto();

        $dto->setMax(10);
        $dto->setMin(0);

        $statistic = $this->getStatistic();

        $series = (new RadialChartSeriesDto())
            ->setPercentage(($statistic / $dto->getMax()) * 100)
            ->setFormattedValue(app(FormatValue::class)->execute($statistic, 1));

        $dto->setSeries([$series]);

        return $dto;
    }

    private function getStatistic(): float
    {
        $baseQuery = GoogleAdAccountKeywordQualityScore::query()
            ->selectRaw(sprintf('AVG(%1$s) as %1$s', GoogleAdAccountKeywordQualityScore::PROPERTY_SCORE))
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget));

        if ($this->filter->getStartDate()) {
            $baseQuery->where(GoogleAdAccountKeywordQualityScore::PROPERTY_DATE, '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where(GoogleAdAccountKeywordQualityScore::PROPERTY_DATE, '<=', $this->filter->getEndDate());
        }

        return $baseQuery->first()->getScore() ?? 0;
    }
}
