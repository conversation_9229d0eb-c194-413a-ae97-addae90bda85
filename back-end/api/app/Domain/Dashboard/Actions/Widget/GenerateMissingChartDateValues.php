<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Dto\Widget\ChartValueDto;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Support\Actions\FormatValue;
use Carbon\CarbonInterface;

class GenerateMissingChartDateValues
{
    /**
     * @param ChartValueDto[] $values
     * @param CarbonInterface $startDate
     * @param CarbonInterface $endDate
     * @return array
     */
    public function execute(array $values, CarbonInterface $startDate, CarbonInterface $endDate): array
    {
        while (!$startDate->isSameDay($endDate)) {
            $exists = array_filter($values, fn($value) => $value->getDate()->isSameDay($startDate));

            if ($exists) {
                $values[] = (new ChartValueDto())->setValue(0)->setDate($startDate)->setValueFormatted(0);
            }

            $startDate->addDay();
        }

        return $values;
    }
}
