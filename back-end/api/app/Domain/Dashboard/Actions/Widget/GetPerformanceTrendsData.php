<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\ChartAnomalyDto;
use App\Domain\Dashboard\Dto\Widget\ChartDto;
use App\Domain\Dashboard\Dto\Widget\ChartSeriesDto;
use App\Domain\Dashboard\Dto\Widget\ChartValueDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Dashboard\Support\Enums\WidgetAccuracy;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Support\Actions\FormatValue;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class GetPerformanceTrendsData
{
    private Widget $widget;
    private WidgetStatisticsFilter $filter;

    public const SETTING_DECIMALS = 'decimals';
    public const SETTING_NEEDS_FORMATTING_FOR_VALUE = 'needs_formatting_for_value';
    public const SETTING_PREFIX = 'prefix';
    public const SETTING_SUFFIX = 'suffix';

    public const ANOMALY_FACTOR_PERCENTAGE = 30;

    public function __construct(private readonly array $fields, private readonly ?string $chartName = null) {}

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): ChartDto
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $statistics = $this->getStatistics();

        $chart = new ChartDto();

        $data = [];

        $anomalies = [];

        foreach ($statistics as $statistic) {
            $date = Carbon::parse($statistic[GoogleAdCampaignInsight::PROPERTY_DATE]);

            foreach ($this->fields as $field => $settings) {
                $average = $statistics->average($field);

                $value = Arr::get($settings, self::SETTING_NEEDS_FORMATTING_FOR_VALUE, false)
                    ? app(FormatValue::class)->execute(
                        $statistic[$field],
                        Arr::get($settings, self::SETTING_DECIMALS, 0),
                    )
                    : $statistic[$field];

                $data[$field][] = (new ChartValueDto())
                    ->setValue($value)
                    ->setDate($date)
                    ->setPrefix(Arr::get($settings, self::SETTING_PREFIX))
                    ->setSuffix(Arr::get($settings, self::SETTING_SUFFIX))
                    ->setValueFormatted(
                        app(FormatValue::class)->execute(
                            $statistic[$field],
                            Arr::get($settings, self::SETTING_DECIMALS, 0),
                        ),
                    );

                if ($statistic[$field]) {
                    $anomalyPercentage = (($statistic[$field] * $average) / $statistic[$field]) * 100;
                } else {
                    $anomalyPercentage = 0;
                }

                if (
                    !$anomalyPercentage ||
                    ($anomalyPercentage < 0 && $anomalyPercentage >= -self::ANOMALY_FACTOR_PERCENTAGE) ||
                    ($anomalyPercentage > 0 && $anomalyPercentage <= self::ANOMALY_FACTOR_PERCENTAGE)
                ) {
                    continue;
                }

                $anomalies[] = (new ChartAnomalyDto())
                    ->setValue($value)
                    ->setDate($date)
                    ->setSeriesIndex(array_search($field, array_keys($this->fields)))
                    ->setColor($statistic[$field] < $average ? '#ef4444' : '#22c55e');
            }
        }

        $series = [];

        foreach ($data as $entry => $values) {
            if ($filter->getStartDate() && $filter->getEndDate()) {
                $values = app(GenerateMissingChartDateValues::class)->execute(
                    $values,
                    $filter->getStartDate()->copy(),
                    $filter->getEndDate()->copy(),
                );
            }

            $series[] = (new ChartSeriesDto())->setTitle($entry)->setValues($values);
        }

        $chart->setSeries($series);
        $chart->setAccuracy($filter->getAccuracy());
        $chart->setAnomalies($anomalies);
        $chart->setName($this->chartName);

        return $chart;
    }

    private function getStatistics(): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(' . GoogleAdCampaignInsight::PROPERTY_DATE . ', "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(' . GoogleAdCampaignInsight::PROPERTY_DATE . ', "%Y-%m")',
        };

        $baseQuery = GoogleAdCampaignInsight::query();

        foreach ($this->fields as $field => $settings) {
            $baseQuery->selectRaw(sprintf('SUM(%1$s) as %1$s', $field));
        }

        $baseQuery
            ->selectRaw(sprintf('%s as %s', $dateSelector, GoogleAdCampaignInsight::PROPERTY_DATE))
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget))
            ->groupByRaw($dateSelector);

        if ($this->filter->getStartDate()) {
            $baseQuery->where(GoogleAdCampaignInsight::PROPERTY_DATE, '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where(GoogleAdCampaignInsight::PROPERTY_DATE, '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }
}
