<?php

namespace App\Domain\Dashboard\Actions\Widget;

use App\Domain\Dashboard\Actions\GetDashboardSourceIdsForWidget;
use App\Domain\Dashboard\Dto\Widget\TableDto;
use App\Domain\Dashboard\Dto\WidgetStatisticsFilter;
use App\Domain\Dashboard\Models\Widget;
use App\Domain\Google\Models\GoogleAdCampaign;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Support\Actions\FormatValue;
use Illuminate\Database\Eloquent\Collection;

class GetCampaignPerformanceData
{
    private Widget $widget;
    private WidgetStatisticsFilter $filter;

    public function execute(Widget $widget, WidgetStatisticsFilter $filter): TableDto
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $statistics = $this->getStatistics();

        $dto = new TableDto();

        $dto->setHeaders(['Campaign', 'Clicks', 'Impressions', 'Cost', 'Leads', 'Cost/Lead']);

        $rows = [];

        foreach ($statistics as $statistic) {
            $costPerConversion = 0;

            $spend = $statistic[GoogleAdCampaignInsight::PROPERTY_SPEND];
            $conversions = $statistic[GoogleAdCampaignInsight::PROPERTY_CONVERSIONS];

            if ($spend && $conversions) {
                $costPerConversion = $spend / $conversions;
            }

            $rows[] = [
                $statistic[GoogleAdCampaign::PROPERTY_NAME],
                app(FormatValue::class)->execute($statistic[GoogleAdCampaignInsight::PROPERTY_CLICKS]),
                app(FormatValue::class)->execute($statistic[GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS]),
                '€' . app(FormatValue::class)->execute($spend, 2),
                app(FormatValue::class)->execute($conversions),
                '€' . app(FormatValue::class)->execute($costPerConversion, 2),
            ];
        }

        $dto->setRows($rows);

        return $dto;
    }

    private function getStatistics(): Collection
    {
        $baseQuery = GoogleAdCampaignInsight::query()
            ->selectRaw(
                sprintf(
                    'SUM(%s.%2$s) as %2$s',
                    GoogleAdCampaignInsight::TABLE_NAME,
                    GoogleAdCampaignInsight::PROPERTY_CLICKS,
                ),
            )
            ->selectRaw(
                sprintf(
                    'SUM(%s.%2$s) as %2$s',
                    GoogleAdCampaignInsight::TABLE_NAME,
                    GoogleAdCampaignInsight::PROPERTY_SPEND,
                ),
            )
            ->selectRaw(
                sprintf(
                    'SUM(%s.%2$s) as %2$s',
                    GoogleAdCampaignInsight::TABLE_NAME,
                    GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS,
                ),
            )
            ->selectRaw(
                sprintf(
                    'SUM(%s.%2$s) as %2$s',
                    GoogleAdCampaignInsight::TABLE_NAME,
                    GoogleAdCampaignInsight::PROPERTY_CONVERSIONS,
                ),
            )
            ->selectRaw(sprintf('%s.%2$s as %2$s', GoogleAdCampaign::TABLE_NAME, GoogleAdCampaign::PROPERTY_NAME))
            ->join(
                GoogleAdCampaign::TABLE_NAME,
                sprintf(
                    '%s.%s',
                    GoogleAdCampaignInsight::TABLE_NAME,
                    GoogleAdCampaignInsight::PROPERTY_GOOGLE_AD_CAMPAIGN_ID,
                ),
                '=',
                sprintf('%s.%s', GoogleAdCampaign::TABLE_NAME, GoogleAdCampaign::PROPERTY_ID),
            )
            ->withDashboardSources(app(GetDashboardSourceIdsForWidget::class)->execute($this->widget))
            ->groupBy(GoogleAdCampaign::PROPERTY_NAME);

        if ($this->filter->getStartDate()) {
            $baseQuery->where(GoogleAdCampaignInsight::PROPERTY_DATE, '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where(GoogleAdCampaignInsight::PROPERTY_DATE, '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }
}
