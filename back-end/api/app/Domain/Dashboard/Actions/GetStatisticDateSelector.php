<?php

namespace App\Domain\Dashboard\Actions;

use App\Domain\Dashboard\Support\Enums\WidgetAccuracy;

class GetStatisticDateSelector
{
    public function execute(WidgetAccuracy $accuracy, string $dateColumn): string
    {
        return match ($accuracy) {
            WidgetAccuracy::WEEK, WidgetAccuracy::DAY => 'CONCAT(DATE_FORMAT(' .
                $dateColumn .
                ', "%Y"), "-", LPAD(WEEK(' .
                $dateColumn .
                ', 1), 2, "0"))',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(' . $dateColumn . ', "%Y-%m")',
            WidgetAccuracy::QUARTER => 'CONCAT(DATE_FORMAT(' .
                $dateColumn .
                ', "%Y"), "-Q", QUARTER(' .
                $dateColumn .
                '))',
            WidgetAccuracy::YEAR => 'DATE_FORMAT(' . $dateColumn . ', "%Y")',
        };
    }
}
