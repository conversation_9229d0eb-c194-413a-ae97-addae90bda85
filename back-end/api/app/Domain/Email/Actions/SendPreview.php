<?php

namespace App\Domain\Email\Actions;

use App\Domain\Email\Models\Email;
use App\Domain\Profile\Models\User;

class SendPreview
{
    public const DUMMY_EVENTS = [];

    public function execute(Email $email, User $user): bool
    {
        if (!array_key_exists($email->getEvent(), self::DUMMY_EVENTS)) {
            return false;
        }

        /** @var MailEvent $event */
        $event = app(self::DUMMY_EVENTS[$email->getEvent()])->execute();

        $event->setEmail($user->getEmail());
        $event->setNameOfReceiver($user->getFullName());
        $event->setLanguage($email->getLanguage());

        app(SendSystemEmail::class)->execute($event, $user->getAccount());

        return true;
    }
}
