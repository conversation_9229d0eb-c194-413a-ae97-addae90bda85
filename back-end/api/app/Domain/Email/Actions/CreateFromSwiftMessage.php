<?php

namespace App\Domain\Email\Actions;

use App\Domain\Email\Models\EmailLog;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mime\Email;

class CreateFromSwiftMessage
{
    private Email $email;
    private SentMessage $message;

    /**
     * @param SentMessage $message
     * @return EmailLog
     */
    public function execute(SentMessage $message): EmailLog
    {
        $this->message = $message;

        /** @var Email $email */
        $email = $message->getOriginalMessage();

        $this->email = $email;

        return $this->processItem();
    }

    private function processItem(): EmailLog
    {
        return EmailLog::query()->create([
            EmailLog::PROPERTY_TO => $this->formatAddressField('To'),
            EmailLog::PROPERTY_CC => $this->formatAddressField('Cc'),
            EmailLog::PROPERTY_BCC => $this->formatAddressField('Bcc'),
            EmailLog::PROPERTY_SUBJECT => $this->email->getSubject(),
            EmailLog::PROPERTY_SWIFT_ID => $this->getMessageId(),
            EmailLog::PROPERTY_HTML_CONTENT => $this->email->getHtmlBody(),
        ]);
    }

    private function getMessageId(): string
    {
        return preg_replace('/@.*/', '', $this->message->getMessageId());
    }

    private function formatAddressField(string $field): ?string
    {
        $headers = $this->email->getHeaders();

        if (!$headers->has($field)) {
            return null;
        }

        $mailboxes = $headers->get($field)->getBody();

        $strings = [];

        foreach ($mailboxes as $email => $name) {
            $mailboxStr = $email;

            if (null !== $name) {
                $mailboxStr = $name->toString() . ' <' . $mailboxStr . '>';
            }
            $strings[] = $mailboxStr;
        }

        return implode(', ', $strings);
    }
}
