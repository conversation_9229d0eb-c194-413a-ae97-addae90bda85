<?php

namespace App\Domain\Email\Actions;

use App\Domain\Email\Dto\EmailDto;
use App\Domain\Email\Models\Email;
use App\Domain\Profile\Models\Account;
use App\Support\Queues;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SendSystemEmail
{
    public function execute(EmailDto $event, ?Account $account = null): void
    {
        /** @var Email $email */
        $email = Email::query()
            ->where(Email::PROPERTY_EVENT, get_class($event))
            ->when(!$account, fn(Builder $query) => $query->whereNull(Email::PROPERTY_ACCOUNT_ID))
            ->when(
                $account,
                fn(Builder $query) => $query->whereIn(
                    DB::raw(
                        sprintf('(%1$s.%2$s, %1$s.%3$s)', Email::TABLE_NAME, Email::PROPERTY_ID, Email::PROPERTY_EVENT),
                    ),
                    fn($query) => $query
                        ->select(
                            DB::raw(
                                sprintf(
                                    'MAX(%1$s.%2$s), %1$s.%3$s',
                                    Email::TABLE_NAME,
                                    Email::PROPERTY_ID,
                                    Email::PROPERTY_EVENT,
                                ),
                            ),
                        )
                        ->from(Email::TABLE_NAME)
                        ->where(Email::PROPERTY_WHITELABEL, true)
                        ->where(Email::PROPERTY_LANGUAGE, $event->getLanguage())
                        ->where(
                            fn($subquery) => $subquery
                                ->where(Email::PROPERTY_ACCOUNT_ID, $account->getId())
                                ->orWhereNull(Email::PROPERTY_ACCOUNT_ID),
                        )
                        ->groupBy(implode('.', [Email::TABLE_NAME, Email::PROPERTY_EVENT])),
                ),
            )
            ->firstOrFail();

        $html = Blade::render($email->getHtml(), $event->toArray());
        $subject = Blade::render($email->getSubject(), $event->toArray());

        $event->setHtml($html)->setSubject($subject);

        $mail = app(GetMailFromEvent::class)->execute($event);

        Mail::queue(new $mail($event), Queues::EMAIL);
    }
}
