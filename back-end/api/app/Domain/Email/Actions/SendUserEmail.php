<?php

namespace App\Domain\Email\Actions;

use App\Domain\Email\Dto\EmailDto;
use App\Domain\Email\Models\Email;
use App\Domain\Profile\Models\User;
use App\Support\Queues;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SendUserEmail
{
    public function execute(EmailDto $event, User $sender): bool
    {
        /** @var Email $email */
        $email = Email::query()
            ->where(Email::PROPERTY_EVENT, get_class($event))
            ->whereIn(
                DB::raw(
                    sprintf('(%1$s.%2$s, %1$s.%3$s)', Email::TABLE_NAME, Email::PROPERTY_ID, Email::PROPERTY_EVENT),
                ),
                fn($query) => $query
                    ->select(
                        DB::raw(
                            sprintf(
                                'MAX(%1$s.%2$s), %1$s.%3$s',
                                Email::TABLE_NAME,
                                Email::PROPERTY_ID,
                                Email::PROPERTY_EVENT,
                            ),
                        ),
                    )
                    ->from(Email::TABLE_NAME)
                    ->where(Email::PROPERTY_WHITELABEL, true)
                    ->where(Email::PROPERTY_LANGUAGE, $event->getLanguage())
                    ->where(
                        fn($subquery) => $subquery
                            ->where(Email::PROPERTY_ACCOUNT_ID, $sender->getAccountId())
                            ->orWhereNull(Email::PROPERTY_ACCOUNT_ID),
                    )
                    ->groupBy(implode('.', [Email::TABLE_NAME, Email::PROPERTY_EVENT])),
            )
            ->firstOrFail();

        $event->setNameOfSender($sender->getFullName())->setCompanyOfSender($sender->getAccount()?->getName());

        $html = Blade::render($email->getHtml(), $event->toArray());

        $event->setHtml($html)->setSubject(Blade::render($email->getSubject(), $event->toArray()));

        $cc = $email->getCc() ?? [];

        if ($email->getCcSender()) {
            $cc[] = $sender->getEmail();
        }

        $event->setCc($cc);

        //        if (
        //            $sender->getAccount()?->getActiveSubscription()?->getProduct()?->getWhiteLabel() &&
        //            $sender->getPostmarkSignatureId() &&
        //            $sender->getPostmarkSignatureConfirmed()
        //        ) {
        //            $event->setEmailOfSender($sender->getEmail());
        //        }

        $mail = app(GetMailFromEvent::class)->execute($event);

        Mail::queue(new $mail($event), Queues::EMAIL);

        return true;
    }
}
