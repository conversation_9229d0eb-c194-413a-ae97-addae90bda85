<?php

namespace App\Domain\Email\Actions;

use App\Domain\Email\Dto\EmailDto;
use App\Domain\Email\Dto\Mails\Authentication\AccountRegisteredDto;
use App\Domain\Email\Mails\Authentication\AccountRegisteredMail;
use App\Domain\Email\Support\Mailable;

class GetMailFromEvent
{
    public function execute(EmailDto $event): ?string
    {
        return match (get_class($event)) {
            AccountRegisteredDto::class => AccountRegisteredMail::class,
            default => null,
        };
    }
}
