<?php

namespace App\Domain\Email\Actions;

use App\Domain\Email\Models\Email;
use Illuminate\Support\Pluralizer;
use Illuminate\Support\Str;
use ReflectionClass;

class GetVariables
{
    public function execute(Email $email): array
    {
        $constants = (new ReflectionClass($email->getEvent()))->getConstants();

        $constants = array_filter($constants, fn($item) => Str::contains($item, 'ARRAY'), ARRAY_FILTER_USE_KEY);

        $variables = [];

        foreach ($constants as $constant => $variable) {
            $name = Str::ucfirst(Str::replace('_', ' ', $variable));

            $value = sprintf('{{$%s}}', $variable);

            if (Pluralizer::plural($name) === $name) {
                $value = sprintf('@foreach($%s as $value)<p>{{$value}}</p>@endforeach', $variable);
            }

            $variables[$variable] = [
                'name' => $name,
                'value' => $value,
            ];
        }

        return $variables;
    }
}
