<?php

namespace App\Domain\Email\Models;

use App\Domain\Profile\Models\Account;
use App\Domain\Profile\Models\User;
use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Email extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'emails';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_ACCOUNT_ID = 'account_id';
    public const PROPERTY_UPDATED_BY = 'updated_by';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_EVENT = 'event';
    public const PROPERTY_WHITELABEL = 'whitelabel';
    public const PROPERTY_HTML = 'html';
    public const PROPERTY_LANGUAGE = 'language';
    public const PROPERTY_SUBJECT = 'subject';
    public const PROPERTY_CC_SENDER = 'cc_sender';
    public const PROPERTY_CC = 'cc';

    public const RELATION_ACCOUNT = 'account';
    public const RELATION_UPDATED_BY = 'updatedBy';

    protected $casts = [
        self::PROPERTY_WHITELABEL => 'bool',
        self::PROPERTY_CC => 'array',
    ];

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID, self::PROPERTY_UPDATED_BY];

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class, self::PROPERTY_ACCOUNT_ID);
    }

    public function getAccount(): ?Account
    {
        return $this->{self::RELATION_ACCOUNT};
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::PROPERTY_UPDATED_BY);
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getAccountId(): ?int
    {
        return $this->{self::PROPERTY_ACCOUNT_ID};
    }

    public function setAccountId(?int $value): self
    {
        $this->{self::PROPERTY_ACCOUNT_ID} = $value;

        return $this;
    }

    public function getUpdatedBy(): ?int
    {
        return $this->{self::PROPERTY_UPDATED_BY};
    }

    public function setUpdatedBy(?int $value): self
    {
        $this->{self::PROPERTY_UPDATED_BY} = $value;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(?string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;

        return $this;
    }

    public function getEvent(): string
    {
        return $this->{self::PROPERTY_EVENT};
    }

    public function setEvent(string $value): self
    {
        $this->{self::PROPERTY_EVENT} = $value;

        return $this;
    }

    public function getWhitelabel(): bool
    {
        return $this->{self::PROPERTY_WHITELABEL};
    }

    public function setWhitelabel(bool $value): self
    {
        $this->{self::PROPERTY_WHITELABEL} = $value;

        return $this;
    }

    public function getHtml(): ?string
    {
        return $this->{self::PROPERTY_HTML};
    }

    public function setHtml(?string $value): self
    {
        $this->{self::PROPERTY_HTML} = $value;

        return $this;
    }

    public function getLanguage(): ?string
    {
        return $this->{self::PROPERTY_LANGUAGE};
    }

    public function setLanguage(?string $value): self
    {
        $this->{self::PROPERTY_LANGUAGE} = $value;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->{self::PROPERTY_SUBJECT};
    }

    public function setSubject(?string $value): self
    {
        $this->{self::PROPERTY_SUBJECT} = $value;

        return $this;
    }

    public function getCcSender(): bool
    {
        return $this->{self::PROPERTY_CC_SENDER};
    }

    public function setCcSender(bool $value): self
    {
        $this->{self::PROPERTY_CC_SENDER} = $value;

        return $this;
    }

    public function getCc(): ?array
    {
        return $this->{self::PROPERTY_CC};
    }

    public function setCc(?array $value): self
    {
        $this->{self::PROPERTY_CC} = $value;

        return $this;
    }
}
