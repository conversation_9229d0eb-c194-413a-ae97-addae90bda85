<?php

namespace App\Domain\Email\Models;

use App\Support\Models\Traits\TimestampableMethods;
use Illuminate\Database\Eloquent\Model;

class EmailLog extends Model
{
    use TimestampableMethods;

    public const TABLE_NAME = 'email_logs';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_TO = 'to';
    public const PROPERTY_CC = 'cc';
    public const PROPERTY_BCC = 'bcc';
    public const PROPERTY_SUBJECT = 'subject';
    public const PROPERTY_HTML_CONTENT = 'html_content';
    public const PROPERTY_TEXT_CONTENT = 'text_content';
    public const PROPERTY_SWIFT_ID = 'swift_id';

    protected $table = self::TABLE_NAME;

    protected $guarded = [self::PROPERTY_ID];

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;

        return $this;
    }

    public function getTo(): ?string
    {
        return $this->{self::PROPERTY_TO};
    }

    public function setTo(?string $value): self
    {
        $this->{self::PROPERTY_TO} = $value;

        return $this;
    }

    public function getCc(): ?string
    {
        return $this->{self::PROPERTY_CC};
    }

    public function setCc(?string $value): self
    {
        $this->{self::PROPERTY_CC} = $value;

        return $this;
    }

    public function getBcc(): ?string
    {
        return $this->{self::PROPERTY_BCC};
    }

    public function setBcc(?string $value): self
    {
        $this->{self::PROPERTY_BCC} = $value;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->{self::PROPERTY_SUBJECT};
    }

    public function setSubject(?string $value): self
    {
        $this->{self::PROPERTY_SUBJECT} = $value;

        return $this;
    }

    public function getHtmlContent(): ?string
    {
        return $this->{self::PROPERTY_HTML_CONTENT};
    }

    public function setHtmlContent(?string $value): self
    {
        $this->{self::PROPERTY_HTML_CONTENT} = $value;

        return $this;
    }

    public function getTextContent(): ?string
    {
        return $this->{self::PROPERTY_TEXT_CONTENT};
    }

    public function setTextContent(?string $value): self
    {
        $this->{self::PROPERTY_TEXT_CONTENT} = $value;

        return $this;
    }

    public function getSwiftId(): ?string
    {
        return $this->{self::PROPERTY_SWIFT_ID};
    }

    public function setSwiftId(?string $value): self
    {
        $this->{self::PROPERTY_SWIFT_ID} = $value;

        return $this;
    }
}
