<?php

namespace App\Domain\Email\Support;

use App\Domain\Email\Dto\EmailDto;
use Illuminate\Mail\Mailable as ParentMailable;

class Mailable extends ParentMailable
{
    public function __construct(private readonly EmailDto $event) {}

    public function build(): self
    {
        return $this->subject($this->event->getSubject())
            ->replyTo(config('mail.from.reply_to'), 'LinkMyAgency')
            ->cc($this->event->getCc() ?? [])
            ->to($this->event->getEmail())
            ->from(
                $this->event?->getEmailOfSender() ?? config('mail.from.address'),
                $this->event?->getNameOfSender() ?? config('mail.from.name'),
            )
            ->html($this->event->getHtml());
    }

    protected function getVariables(): array
    {
        return [];
    }
}
