<?php

namespace App\Domain\Email\Dto\Mails\Authentication;

use App\Domain\Email\Dto\EmailDto;

class AccountRegisteredDto extends EmailDto
{
    public const ARRAY_ACCOUNT_NAME = 'account_name';

    private string $name;

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            self::ARRAY_ACCOUNT_NAME => $this->name,
        ]);
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): AccountRegisteredDto
    {
        $this->name = $name;
        return $this;
    }
}
