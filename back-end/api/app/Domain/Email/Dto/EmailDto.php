<?php

namespace App\Domain\Email\Dto;

abstract class EmailDto
{
    protected const ARRAY_NAME_OF_RECEIVER = 'name_of_receiver';
    protected const ARRAY_NAME_OF_SENDER = 'name_of_sender';
    protected const ARRAY_COMPANY_OF_SENDER = 'company_of_sender';

    protected string $email;
    protected ?string $nameOfReceiver = null;
    protected ?string $language = 'en';
    protected ?string $html = null;
    protected ?string $nameOfSender = null;
    protected ?string $emailOfSender = null;
    protected ?string $companyOfSender = null;
    protected ?string $subject = null;
    protected ?array $cc = null;

    public function toArray(): array
    {
        return [
            self::ARRAY_NAME_OF_SENDER => $this->getNameOfSender(),
            self::ARRAY_COMPANY_OF_SENDER => $this->getCompanyOfSender(),
            self::ARRAY_NAME_OF_RECEIVER => $this->getNameOfReceiver(),
        ];
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): EmailDto
    {
        $this->email = $email;
        return $this;
    }

    public function getNameOfReceiver(): ?string
    {
        return $this->nameOfReceiver;
    }

    public function setNameOfReceiver(?string $nameOfReceiver): EmailDto
    {
        $this->nameOfReceiver = $nameOfReceiver;
        return $this;
    }

    public function getLanguage(): ?string
    {
        return $this->language;
    }

    public function setLanguage(?string $language): EmailDto
    {
        $this->language = $language;
        return $this;
    }

    public function getHtml(): ?string
    {
        return $this->html;
    }

    public function setHtml(?string $html): EmailDto
    {
        $this->html = $html;
        return $this;
    }

    public function getNameOfSender(): ?string
    {
        return $this->nameOfSender;
    }

    public function setNameOfSender(?string $nameOfSender): EmailDto
    {
        $this->nameOfSender = $nameOfSender;
        return $this;
    }

    public function getEmailOfSender(): ?string
    {
        return $this->emailOfSender;
    }

    public function setEmailOfSender(?string $emailOfSender): EmailDto
    {
        $this->emailOfSender = $emailOfSender;
        return $this;
    }

    public function getCompanyOfSender(): ?string
    {
        return $this->companyOfSender;
    }

    public function setCompanyOfSender(?string $companyOfSender): EmailDto
    {
        $this->companyOfSender = $companyOfSender;
        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(?string $subject): EmailDto
    {
        $this->subject = $subject;
        return $this;
    }

    public function getCc(): ?array
    {
        return $this->cc;
    }

    public function setCc(?array $cc): EmailDto
    {
        $this->cc = $cc;
        return $this;
    }
}
