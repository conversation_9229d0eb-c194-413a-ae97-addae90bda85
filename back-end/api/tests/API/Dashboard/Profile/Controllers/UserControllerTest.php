<?php

namespace Tests\API\Dashboard\Profile\Controllers;

use App\Domain\Profile\Models\Account;
use App\Domain\Profile\Models\User;
use App\Domain\Profile\Support\Enums\UserRole;
use App\Support\Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class UserControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    public function test_Index_ReturnsUserCollection(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        User::factory()->count(3)->withAccount($account)->create();

        // Act
        $response = $this->getJson(route('api.users.index'));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(4, 'data');
    }

    public function test_Index_FiltersBySearchTerm(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()
            ->verified()
            ->withAccount($account)
            ->create([
                User::PROPERTY_FIRSTNAME => 'test',
            ]);
        $this->actingAsUser($user);

        User::factory()
            ->withAccount($account)
            ->create([
                User::PROPERTY_FIRSTNAME => 'John',
                User::PROPERTY_LASTNAME => 'Doe',
            ]);

        User::factory()
            ->withAccount($account)
            ->create([
                User::PROPERTY_FIRSTNAME => 'Jane',
                User::PROPERTY_LASTNAME => 'Smith',
            ]);

        // Act
        $response = $this->getJson(route('api.users.index', ['search' => 'John']));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $response->assertJsonPath('data.0.firstname', 'John');
    }

    public function test_Store_CreatesNewUser(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        $userData = [
            'firstname' => 'New',
            'lastname' => 'User',
            'email' => '<EMAIL>',
            'role' => UserRole::USER,
        ];

        // Act
        $response = $this->postJson(route('api.users.store'), $userData);

        // Assert
        $response->assertStatus(201);
        $response->assertJsonPath('data.firstname', 'New');
        $response->assertJsonPath('data.lastname', 'User');
        $response->assertJsonPath('data.email', '<EMAIL>');

        $this->assertDatabaseHas(User::TABLE_NAME, [
            User::PROPERTY_ACCOUNT_ID => $account->getId(),
            User::PROPERTY_FIRSTNAME => 'New',
            User::PROPERTY_LASTNAME => 'User',
            User::PROPERTY_EMAIL => '<EMAIL>',
        ]);
    }

    public function test_Store_CreatesNewUserWithImage(): void
    {
        Storage::fake();
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        $image = UploadedFile::fake()->image('avatar.jpg');

        $userData = [
            'firstname' => 'New',
            'lastname' => 'User',
            'email' => '<EMAIL>',
            'image' => $image,
            'role' => UserRole::USER,
        ];

        // Act
        $response = $this->postJson(route('api.users.store'), $userData);

        // Assert
        $response->assertStatus(201);
        $response->assertJsonPath('data.firstname', 'New');
        $response->assertJsonPath('data.lastname', 'User');
        $response->assertJsonPath('data.email', '<EMAIL>');

        $this->assertNotNull($response->json('data.image_url'));

        $createdUser = User::query()->where(User::PROPERTY_EMAIL, '<EMAIL>')->first();
        $this->assertNotNull($createdUser->getImagePath());
    }

    public function test_Store_ValidatesRequiredFields(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        // Act
        $response = $this->postJson(route('api.users.store'), []);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['firstname', 'lastname', 'email']);
    }

    public function test_Show_ReturnsUserDetails(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        $targetUser = User::factory()->withAccount($account)->create();

        // Act
        $response = $this->getJson(route('api.users.show', ['id' => $targetUser->getId()]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonPath('data.id', $targetUser->getId());
        $response->assertJsonPath('data.firstname', $targetUser->getFirstname());
        $response->assertJsonPath('data.lastname', $targetUser->getLastname());
        $response->assertJsonPath('data.email', $targetUser->getEmail());
    }

    public function test_Show_Returns404_WhenUserNotFound(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        // Act
        $response = $this->getJson(route('api.users.show', ['id' => 9999]));

        // Assert
        $response->assertStatus(404);
    }

    public function test_Update_UpdatesExistingUser(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        $targetUser = User::factory()
            ->withAccount($account)
            ->create([
                User::PROPERTY_FIRSTNAME => 'Original',
                User::PROPERTY_LASTNAME => 'Name',
            ]);

        $userData = [
            'firstname' => 'Updated',
            'lastname' => 'User',
            'role' => UserRole::USER,
        ];

        // Act
        $response = $this->postJson(route('api.users.update', ['id' => $targetUser->getId()]), $userData);

        // Assert
        $response->assertStatus(200);
        $response->assertJsonPath('data.firstname', 'Updated');
        $response->assertJsonPath('data.lastname', 'User');

        $targetUser->refresh();
        $this->assertEquals('Updated', $targetUser->getFirstname());
        $this->assertEquals('User', $targetUser->getLastname());
    }

    public function test_Update_UpdatesUserWithImage(): void
    {
        Storage::fake();

        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        $targetUser = User::factory()
            ->withAccount($account)
            ->create([
                User::PROPERTY_FIRSTNAME => 'Original',
                User::PROPERTY_LASTNAME => 'Name',
            ]);

        $image = UploadedFile::fake()->image('avatar.jpg');

        $userData = [
            'firstname' => 'Updated',
            'lastname' => 'User',
            'image' => $image,
            'role' => UserRole::USER,
        ];

        // Act
        $response = $this->postJson(route('api.users.update', ['id' => $targetUser->getId()]), $userData);

        // Assert
        $response->assertStatus(200);
        $response->assertJsonPath('data.firstname', 'Updated');
        $response->assertJsonPath('data.lastname', 'User');

        $targetUser->refresh();
        $this->assertEquals('Updated', $targetUser->getFirstname());
        $this->assertEquals('User', $targetUser->getLastname());
        $this->assertNotNull($targetUser->getImagePath());
    }

    public function test_Update_ValidatesRequiredFields(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        $targetUser = User::factory()->withAccount($account)->create();

        // Act
        $response = $this->postJson(route('api.users.update', ['id' => $targetUser->getId()]), []);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['firstname', 'lastname']);
    }

    public function test_Delete_RemovesUser(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        $targetUser = User::factory()->withAccount($account)->create();

        // Act
        $response = $this->deleteJson(route('api.users.delete', ['id' => $targetUser->getId()]));

        // Assert
        $response->assertStatus(200);
        $this->assertDatabaseMissing(User::TABLE_NAME, [User::PROPERTY_ID => $targetUser->getId()]);
    }

    public function test_Delete_Returns404_WhenUserNotFound(): void
    {
        // Arrange
        $account = Account::factory()->verified()->create();
        $user = User::factory()->verified()->withAccount($account)->create();
        $this->actingAsUser($user);

        // Act
        $response = $this->deleteJson(route('api.users.delete', ['id' => 9999]));

        // Assert
        $response->assertStatus(404);
    }
}
