<?php

namespace Tests\API\Dashboard\Authentication\Controllers;

use App\Domain\Profile\Actions\RegisterAccount;
use App\Domain\Profile\Models\Account;
use App\Domain\Profile\Models\User;
use App\Support\Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class AuthControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_Login_ReturnsSuccess_WhenCredentialsAreValid(): void
    {
        // Arrange
        $account = Account::factory()->create(['verified_at' => now()]);
        $user = User::factory()->create([
            'account_id' => $account->getId(),
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'verified_at' => now(),
        ]);

        // Act
        $response = $this->postJson(route('api.auth.login'), [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        // Assert
        $response->assertStatus(200);
        $this->assertAuthenticatedAs($user, 'user');

        // Verify last_login_at was updated
        $user->refresh();
        $this->assertNotNull($user->getLastLoginAt());
        $this->assertEqualsWithDelta(now()->timestamp, $user->getLastLoginAt()->timestamp, 5);
    }

    public function test_Login_Returns401_WhenCredentialsAreInvalid(): void
    {
        // Arrange
        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Act
        $response = $this->postJson(route('api.auth.login'), [
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ]);

        // Assert
        $response->assertStatus(401);
        $this->assertGuest('user');
    }

    public function test_Login_Returns403_WhenUserIsNotVerified(): void
    {
        // Arrange
        $account = Account::factory()->create(['verified_at' => now()]);
        User::factory()->create([
            'account_id' => $account->getId(),
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'verified_at' => null,
        ]);

        // Act
        $response = $this->postJson(route('api.auth.login'), [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        // Assert
        $response->assertStatus(403);
    }

    public function test_Login_Returns403_WhenAccountIsNotVerified(): void
    {
        // Arrange
        $account = Account::factory()->create(['verified_at' => null]);
        User::factory()->create([
            'account_id' => $account->getId(),
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'verified_at' => now(),
        ]);

        // Act
        $response = $this->postJson(route('api.auth.login'), [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        // Assert
        $response->assertStatus(403);
    }

    public function test_Register_CreatesNewAccountAndUser(): void
    {
        // Arrange
        $mockRegisterAccount = Mockery::mock(RegisterAccount::class);
        $mockRegisterAccount->shouldReceive('execute')->once();
        $this->app->instance(RegisterAccount::class, $mockRegisterAccount);

        $registerData = [
            'name' => 'Test Agency',
            'firstname' => 'John',
            'lastname' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password',
        ];

        // Act
        $response = $this->postJson(route('api.auth.register'), $registerData);

        // Assert
        $response->assertStatus(200);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
