<?php

namespace Tests\API\Dashboard\Google;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Profile\Models\User;
use App\Support\Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class GoogleAccountControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_Index_ReturnsAllAccounts()
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAsUser($user);

        $accounts = GoogleAccount::factory()->for($user->getAccount())->count(2)->create();
        $response = $this->getJson(route('api.google.accounts.index'));
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');
    }

    public function test_Index_FiltersBySearchTerm()
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAsUser($user);

        GoogleAccount::factory()
            ->for($user->getAccount())
            ->create([
                GoogleAccount::PROPERTY_NAME => 'Alpha',
                GoogleAccount::PROPERTY_EMAIL => '<EMAIL>',
            ]);
        GoogleAccount::factory()
            ->for($user->getAccount())
            ->create([
                GoogleAccount::PROPERTY_NAME => 'Beta',
                GoogleAccount::PROPERTY_EMAIL => '<EMAIL>',
            ]);
        $response = $this->getJson(route('api.google.accounts.index', ['search' => 'Alpha']));
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $response->assertJsonPath('data.0.name', 'Alpha');
    }

    public function test_Index_PaginatesResults()
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAsUser($user);

        GoogleAccount::factory()->for($user->getAccount())->count(5)->create();
        $response = $this->getJson(route('api.google.accounts.index', ['page' => 1, 'page_size' => 2]));
        $response->assertStatus(200);
        $response->assertJsonPath('meta.per_page', 2);
    }

    public function test_Delete_RemovesAccount()
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAsUser($user);

        $account = GoogleAccount::factory()->for($user->getAccount())->create();
        $response = $this->deleteJson(route('api.google.accounts.delete', ['id' => $account->getId()]));
        $response->assertStatus(200);
        $this->assertDatabaseMissing(GoogleAccount::TABLE_NAME, [GoogleAccount::PROPERTY_ID => $account->getId()]);
    }

    public function test_Delete_Returns404_WhenAccountNotFound()
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAsUser($user);

        $response = $this->deleteJson(route('api.google.accounts.delete', ['id' => 9999]));
        $response->assertStatus(404);
    }
}
