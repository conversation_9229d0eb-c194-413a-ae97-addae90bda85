<?php

namespace Tests\API\Dashboard\Google;

use App\Domain\Google\Actions\Authentication\GoogleOAuthRedirectLink;
use App\Domain\Profile\Models\User;
use App\Support\Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class GoogleAuthenticationControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_OauthRedirectLink_ReturnsResource()
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->actingAsUser($user);

        $mock = Mockery::mock(GoogleOAuthRedirectLink::class);
        $mock->shouldReceive('execute')->once()->andReturn('https://mocked.url');
        $this->app->instance(GoogleOAuthRedirectLink::class, $mock);
        $response = $this->postJson(route('api.google.authentication.oauthRedirectLink'));
        $response->assertStatus(200);
        $response->assertJsonPath('data.url', 'https://mocked.url');
    }
}
