<?php

namespace Tests\Domain\Google\Factories;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleAdAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleAdAccountFactory extends Factory
{
    protected $model = GoogleAdAccount::class;

    public function definition(): array
    {
        return [
            GoogleAdAccount::PROPERTY_GOOGLE_ACCOUNT_ID => GoogleAccount::factory(),
            GoogleAdAccount::PROPERTY_CUSTOMER_ID => $this->faker->randomNumber(5),
            GoogleAdAccount::PROPERTY_EXTERNAL_ID => $this->faker->randomNumber(5),
            GoogleAdAccount::PROPERTY_NAME => $this->faker->name(),
            GoogleAdAccount::PROPERTY_STATUS => 'OPEN',
            GoogleAdAccount::PROPERTY_TIME_ZONE => 'Europe/Amsterdam',
            GoogleAdAccount::PROPERTY_CURRENCY => 'EUR',
            GoogleAdAccount::PROPERTY_LEVEL => 0,
            GoogleAdAccount::PROPERTY_IS_HIDDEN => false,
            GoogleAdAccount::PROPERTY_IS_MANAGER => false,
            GoogleAdAccount::PROPERTY_IS_TEST_ACCOUNT => false,
            GoogleAdAccount::PROPERTY_LAST_SYNCED_AT => now(),
        ];
    }
}
