<?php

namespace Tests\Domain\Google\Factories;

use App\Domain\Google\Models\GoogleAdCampaign;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleAdCampaignInsightFactory extends Factory
{
    protected $model = GoogleAdCampaignInsight::class;

    public function definition(): array
    {
        return [
            GoogleAdCampaignInsight::PROPERTY_GOOGLE_AD_CAMPAIGN_ID => GoogleAdCampaign::factory(),
            GoogleAdCampaignInsight::PROPERTY_DATE => now(),
            GoogleAdCampaignInsight::PROPERTY_CLICKS => $this->faker->randomNumber(3),
            GoogleAdCampaignInsight::PROPERTY_VIDEO_VIEWS => $this->faker->randomNumber(3),
            GoogleAdCampaignInsight::PROPERTY_CTR => $this->faker->randomNumber(3),
            GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS => $this->faker->randomNumber(3),
            GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPC => $this->faker->randomNumber(3),
            GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPM => $this->faker->randomNumber(3),
            GoogleAdCampaignInsight::PROPERTY_SPEND => $this->faker->randomNumber(3),
        ];
    }
}
