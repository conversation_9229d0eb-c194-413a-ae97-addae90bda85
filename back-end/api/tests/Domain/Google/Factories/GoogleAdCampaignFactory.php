<?php

namespace Tests\Domain\Google\Factories;

use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Google\Models\GoogleAdCampaign;
use App\Domain\Google\Support\Enums\Ads\AdvertisingChannelType;
use Illuminate\Database\Eloquent\Factories\Factory;

class GoogleAd<PERSON>ampaignFactory extends Factory
{
    protected $model = GoogleAdCampaign::class;

    public function definition(): array
    {
        return [
            GoogleAdCampaign::PROPERTY_GOOGLE_AD_ACCOUNT_ID => GoogleAdAccount::factory(),
            GoogleAdCampaign::PROPERTY_EXTERNAL_ID => $this->faker->randomNumber(5),
            GoogleAdCampaign::PROPERTY_NAME => $this->faker->name,
            GoogleAdCampaign::PROPERTY_ADVERTISING_CHANNEL_TYPE => AdvertisingChannelType::SEARCH,
        ];
    }
}
