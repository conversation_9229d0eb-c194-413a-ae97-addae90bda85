<?php

namespace Tests\Domain\Google\Factories;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Profile\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class GoogleAccountFactory extends Factory
{
    protected $model = GoogleAccount::class;

    public function definition(): array
    {
        return [
            GoogleAccount::PROPERTY_ACCOUNT_ID => Account::factory(),
            GoogleAccount::PROPERTY_EXTERNAL_ID => Str::random(),
            GoogleAccount::PROPERTY_EMAIL => $this->faker->email,
            GoogleAccount::PROPERTY_NAME => $this->faker->name,
            GoogleAccount::PROPERTY_STATUS => null,
            GoogleAccount::PROPERTY_IMAGE_URL => $this->faker->imageUrl,
            GoogleAccount::PROPERTY_REFRESH_TOKEN => Str::random(),
            GoogleAccount::PROPERTY_TOKEN => Str::random(),
            GoogleAccount::PROPERTY_TOKEN_CREATED_AT => now(),
        ];
    }
}
