<?php

namespace Tests\Domain\Google\Jobs\Ads;

use App\Domain\Google\Actions\Ads\SyncCustomerClients;
use App\Domain\Google\Jobs\Ads\SyncCustomerClientsJob;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleAdAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Mockery;
use App\Support\Tests\TestCase;

class SyncCustomerClientsJobTest extends TestCase
{
    use RefreshDatabase;

    public function test_HandleDispatchesNestedJobsForManagerAccounts()
    {
        Bus::fake();
        $account = GoogleAccount::factory()->create();
        $managerAd = GoogleAdAccount::factory()->create([
            GoogleAdAccount::PROPERTY_GOOGLE_ACCOUNT_ID => $account->getId(),
            GoogleAdAccount::PROPERTY_EXTERNAL_ID => 999,
            GoogleAdAccount::PROPERTY_CUSTOMER_ID => 888,
            GoogleAdAccount::PROPERTY_IS_MANAGER => true,
        ]);
        $nonManagerAd = GoogleAdAccount::factory()->create([
            GoogleAdAccount::PROPERTY_GOOGLE_ACCOUNT_ID => $account->getId(),
            GoogleAdAccount::PROPERTY_EXTERNAL_ID => 777,
            GoogleAdAccount::PROPERTY_CUSTOMER_ID => 666,
            GoogleAdAccount::PROPERTY_IS_MANAGER => false,
        ]);
        $mock = Mockery::mock(SyncCustomerClients::class);
        $mock
            ->shouldReceive('execute')
            ->once()
            ->andReturn([$managerAd, $nonManagerAd]);
        $this->app->instance(SyncCustomerClients::class, $mock);
        $job = new SyncCustomerClientsJob($account, 123, null);
        $job->handle();
        Bus::assertDispatched(SyncCustomerClientsJob::class);
    }
}
