<?php

namespace Tests\Domain\Google\Jobs\Ads;

use App\Domain\Google\Actions\Ads\ListAccessibleAccounts;
use App\Domain\Google\Jobs\Ads\ListAccessibleAccountsJob;
use App\Domain\Google\Jobs\Ads\SyncCustomerClientsJob;
use App\Domain\Google\Models\GoogleAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Mockery;
use App\Support\Tests\TestCase;

class ListAccessibleAccountsJobTest extends TestCase
{
    use RefreshDatabase;

    public function test_HandleDispatchesSyncCustomerClientsJobForEachAccount()
    {
        Bus::fake();
        $account = GoogleAccount::factory()->create();
        $resourceNames = ['customers/123', 'customers/456'];

        $this->mock(ListAccessibleAccounts::class, function ($mock) use ($account, $resourceNames) {
            $mock->shouldReceive('execute')->once()->andReturn($resourceNames);
        });

        $job = new ListAccessibleAccountsJob($account);
        $job->handle();

        Bus::assertDispatched(SyncCustomerClientsJob::class, 2);
    }
}
