<?php

namespace Tests\Domain\Google\Jobs\Ads;

use App\Domain\Google\Actions\Ads\SyncCampaignsWithMetrics;
use App\Domain\Google\Jobs\Ads\SyncCampaignsWithMetricsJob;
use App\Domain\Google\Models\GoogleAdAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use App\Support\Tests\TestCase;
use Carbon\Carbon;

class SyncCampaignsWithMetricsJobTest extends TestCase
{
    use RefreshDatabase;

    public function test_HandleCallsActionWithCorrectArguments()
    {
        $adAccount = GoogleAdAccount::factory()->create();
        $start = Carbon::now()->subDays(10);
        $end = Carbon::now();
        $job = new SyncCampaignsWithMetricsJob($adAccount, $start, $end);
        $mock = Mockery::mock(SyncCampaignsWithMetrics::class);
        $mock
            ->shouldReceive('execute')
            ->once()
            ->withArgs(function ($argAccount, $argStart, $argEnd) use ($adAccount, $start, $end) {
                return $argAccount->getId() === $adAccount->getId() && $argStart->eq($start) && $argEnd->eq($end);
            });
        $this->app->instance(SyncCampaignsWithMetrics::class, $mock);
        $job->handle();
    }
}
