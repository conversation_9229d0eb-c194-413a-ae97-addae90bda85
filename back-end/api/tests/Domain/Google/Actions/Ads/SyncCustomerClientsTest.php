<?php

namespace Tests\Domain\Google\Actions\Ads;

use App\Domain\Google\Actions\Ads\SyncCustomerClients;
use App\Domain\Google\Jobs\Ads\SyncCampaignsWithMetricsJob;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Google\Support\Exceptions\SyncCustomerClientsException;
use App\Support\Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Http;

class SyncCustomerClientsTest extends TestCase
{
    use RefreshDatabase;

    public function test_ExecuteSyncsCustomerClientsAndDispatchesJob()
    {
        Bus::fake();
        $account = GoogleAccount::factory()->create();
        $responseBody = [
            [
                'results' => [
                    [
                        'customerClient' => [
                            'resourceName' => 'customers/123',
                            'descriptiveName' => 'Test Ad Account',
                            'status' => 'ENABLED',
                            'level' => 1,
                            'manager' => true,
                            'hidden' => false,
                            'testAccount' => false,
                            'timeZone' => 'Europe/Amsterdam',
                            'currencyCode' => 'EUR',
                        ],
                    ],
                ],
            ],
        ];
        Http::fake([
            '*' => Http::response($responseBody, 200),
        ]);
        $action = app(SyncCustomerClients::class);
        $result = $action->execute($account, 123);
        $this->assertCount(1, $result);
        $adAccount = $result[0];
        $this->assertInstanceOf(GoogleAdAccount::class, $adAccount);
        $this->assertEquals('Test Ad Account', $adAccount->getName());
        Bus::assertDispatched(SyncCampaignsWithMetricsJob::class);
    }

    public function test_ExecuteThrowsExceptionOnFailedResponse()
    {
        $account = GoogleAccount::factory()->create();
        Http::fake([
            '*' => Http::response([], 400),
        ]);
        $action = app(SyncCustomerClients::class);
        $this->expectException(SyncCustomerClientsException::class);
        $action->execute($account, 123);
    }
}
