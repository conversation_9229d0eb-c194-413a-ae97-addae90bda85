<?php

namespace Tests\Domain\Google\Actions\Ads;

use App\Domain\Google\Actions\Ads\ListAccessibleAccounts;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Support\Exceptions\ListAccessibleAccountsException;
use App\Support\Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class ListAccessibleAccountsTest extends TestCase
{
    use RefreshDatabase;

    public function test_ExecuteReturnsResourceNames()
    {
        $account = GoogleAccount::factory()->create();
        $resourceNames = ['customers/123', 'customers/456'];
        Http::fake([
            '*' => Http::response(['resourceNames' => $resourceNames], 200),
        ]);
        $action = app(ListAccessibleAccounts::class);
        $result = $action->execute($account);
        $this->assertEquals($resourceNames, $result);
    }

    public function test_ExecuteThrowsExceptionOnFailedResponse()
    {
        $account = GoogleAccount::factory()->create();
        Http::fake([
            '*' => Http::response([], 400),
        ]);
        $action = app(ListAccessibleAccounts::class);
        $this->expectException(ListAccessibleAccountsException::class);
        $action->execute($account);
    }
}
