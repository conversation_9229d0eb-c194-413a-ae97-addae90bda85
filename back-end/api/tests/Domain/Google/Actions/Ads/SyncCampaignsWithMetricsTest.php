<?php

namespace Tests\Domain\Google\Actions\Ads;

use App\Domain\Google\Actions\Ads\SyncCampaignsWithMetrics;
use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Google\Models\GoogleAdCampaign;
use App\Domain\Google\Models\GoogleAdCampaignInsight;
use App\Domain\Google\Support\Exceptions\SyncCampaignsWithMetricsException;
use App\Support\Tests\TestCase;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class SyncCampaignsWithMetricsTest extends TestCase
{
    use RefreshDatabase;

    public function test_ExecuteSyncsCampaignsAndInsights()
    {
        $adAccount = GoogleAdAccount::factory()->create();
        $responseBody = [
            [
                'results' => [
                    [
                        'campaign' => [
                            'resourceName' => 'customers/123/campaigns/456',
                            'id' => 456,
                            'name' => 'Test Campaign',
                            'advertisingChannelType' => 'SEARCH',
                        ],
                        'metrics' => [
                            'clicks' => 10,
                            'ctr' => 1.23,
                            'impressions' => 100,
                            'averageCpc' => 2000000,
                            'averageCpm' => 3000000,
                            'videoViews' => 5,
                            'costMicros' => 4000000,
                        ],
                        'segments' => [
                            'date' => '2024-01-01',
                        ],
                    ],
                ],
            ],
        ];
        Http::fake([
            '*' => Http::response($responseBody, 200),
        ]);
        $action = app(SyncCampaignsWithMetrics::class);
        $action->execute($adAccount, Carbon::parse('2024-01-01'), Carbon::parse('2024-01-01'));
        $this->assertDatabaseHas(GoogleAdCampaign::TABLE_NAME, [
            GoogleAdCampaign::PROPERTY_GOOGLE_AD_ACCOUNT_ID => $adAccount->getId(),
            GoogleAdCampaign::PROPERTY_EXTERNAL_ID => 456,
            GoogleAdCampaign::PROPERTY_NAME => 'Test Campaign',
        ]);
        $campaign = GoogleAdCampaign::query()->where(GoogleAdCampaign::PROPERTY_EXTERNAL_ID, 456)->first();
        $this->assertDatabaseHas(GoogleAdCampaignInsight::TABLE_NAME, [
            GoogleAdCampaignInsight::PROPERTY_GOOGLE_AD_CAMPAIGN_ID => $campaign->getId(),
            GoogleAdCampaignInsight::PROPERTY_DATE => '2024-01-01',
            GoogleAdCampaignInsight::PROPERTY_CLICKS => 10,
            GoogleAdCampaignInsight::PROPERTY_VIDEO_VIEWS => 5,
            GoogleAdCampaignInsight::PROPERTY_CTR => 1,
            GoogleAdCampaignInsight::PROPERTY_IMPRESSIONS => 100,
            GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPC => '2',
            GoogleAdCampaignInsight::PROPERTY_AVERAGE_CPM => '3',
            GoogleAdCampaignInsight::PROPERTY_SPEND => '4',
        ]);
    }

    public function test_ExecuteThrowsExceptionOnFailedResponse()
    {
        $adAccount = GoogleAdAccount::factory()->create();
        Http::fake([
            '*' => Http::response([], 400),
        ]);
        $action = app(SyncCampaignsWithMetrics::class);
        $this->expectException(SyncCampaignsWithMetricsException::class);
        $action->execute($adAccount, Carbon::parse('2024-01-01'), Carbon::parse('2024-01-01'));
    }
}
