<?php

namespace Tests\Domain\Google\Actions\Authentication;

use App\Domain\Google\Actions\Authentication\GoogleRefreshToken;
use App\Domain\Google\Models\GoogleAccount;
use App\Support\Tests\TestCase;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class GoogleRefreshTokenTest extends TestCase
{
    use RefreshDatabase;

    public function test_ExecuteRefreshesTokenWhenExpired()
    {
        $account = GoogleAccount::factory()->create([
            GoogleAccount::PROPERTY_TOKEN_CREATED_AT => Carbon::now()->subHours(2),
        ]);
        Http::fake([
            '*' => Http::response(['access_token' => 'new-token'], 200),
        ]);
        $action = app(GoogleRefreshToken::class);
        $token = $action->execute($account);
        $this->assertEquals('new-token', $token);
        $account->refresh();
        $this->assertEquals('new-token', $account->getToken());
    }

    public function test_ExecuteReturnsCurrentTokenIfNotExpired()
    {
        $account = GoogleAccount::factory()->create([
            GoogleAccount::PROPERTY_TOKEN_CREATED_AT => Carbon::now(),
            GoogleAccount::PROPERTY_TOKEN => 'current-token',
        ]);
        $action = app(GoogleRefreshToken::class);
        $token = $action->execute($account);
        $this->assertEquals('current-token', $token);
    }

    public function test_ExecuteReturnsNullIfRefreshFails()
    {
        $account = GoogleAccount::factory()->create([
            GoogleAccount::PROPERTY_TOKEN_CREATED_AT => Carbon::now()->subHours(2),
        ]);
        Http::fake([
            '*' => Http::response([], 400),
        ]);
        $action = app(GoogleRefreshToken::class);
        $token = $action->execute($account);
        $this->assertNull($token);
    }
}
