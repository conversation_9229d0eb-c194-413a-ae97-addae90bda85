<?php

namespace Tests\Domain\Google\Actions\Authentication;

use App\Domain\Google\Actions\Authentication\BySocialiteUser;
use App\Domain\Google\Jobs\Ads\ListAccessibleAccountsJob;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Profile\Models\Account;
use App\Support\Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Laravel\Socialite\Contracts\User as SocialiteUserContract;
use Mockery;

class BySocialiteUserTest extends TestCase
{
    use RefreshDatabase;

    public function test_ExecuteCreatesGoogleAccountAndDispatchesJob()
    {
        Bus::fake();
        $account = Account::factory()->create();
        $user = Mockery::mock(SocialiteUserContract::class);
        $user->shouldReceive('getId')->andReturn('socialite-id');
        $user->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $user->shouldReceive('getName')->andReturn('Test User');
        $user->shouldReceive('getAvatar')->andReturn('http://avatar.url');
        $user->refreshToken = 'refresh-token';
        $user->token = 'access-token';
        $scopes = ['scope1', 'scope2'];
        $action = app(BySocialiteUser::class);
        $googleAccount = $action->execute($account, $user, $scopes);
        $this->assertInstanceOf(GoogleAccount::class, $googleAccount);
        $this->assertEquals('<EMAIL>', $googleAccount->getEmail());
        $this->assertEquals('Test User', $googleAccount->getName());
        $this->assertEquals('refresh-token', $googleAccount->getRefreshToken());
        $this->assertEquals('access-token', $googleAccount->getToken());
        $this->assertEquals($scopes, $googleAccount->getScopes());
        Bus::assertDispatched(ListAccessibleAccountsJob::class);
    }
}
