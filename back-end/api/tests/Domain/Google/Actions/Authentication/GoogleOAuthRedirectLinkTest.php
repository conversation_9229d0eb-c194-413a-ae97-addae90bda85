<?php

namespace Tests\Domain\Google\Actions\Authentication;

use App\Domain\Google\Actions\Authentication\GoogleOAuthRedirectLink;
use App\Domain\Profile\Models\Account;
use App\Support\Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Laravel\Socialite\Two\GoogleProvider;
use Mockery;

class GoogleOAuthRedirectLinkTest extends TestCase
{
    use RefreshDatabase;

    public function test_ExecuteReturnsRedirectUrl()
    {
        $account = Account::factory()->create();
        $provider = Mockery::mock(GoogleProvider::class);
        $provider->shouldReceive('scopes')->andReturnSelf();
        $provider->shouldReceive('with')->andReturnSelf();
        $provider->shouldReceive('redirectUrl')->andReturnSelf();
        $provider->shouldReceive('stateless')->andReturnSelf();
        $provider->shouldReceive('redirect')->andReturnSelf();
        $provider->shouldReceive('getTargetUrl')->andReturn('https://accounts.google.com/o/oauth2/auth?mocked');
        Socialite::shouldReceive('driver')->with('google')->andReturn($provider);
        $action = app(GoogleOAuthRedirectLink::class);
        $url = $action->execute($account);
        $this->assertStringContainsString('https://accounts.google.com/o/oauth2/auth', $url);
    }
}
