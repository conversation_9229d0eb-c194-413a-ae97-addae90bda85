<?php

namespace Tests\Domain\Profile\Factories;

use App\Domain\Profile\Models\Account;
use App\Domain\Profile\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        $account = Account::factory()->create();

        return [
            User::PROPERTY_ACCOUNT_ID => $account->getId(),
            User::PROPERTY_FIRSTNAME => $this->faker->firstName(),
            User::PROPERTY_LASTNAME => $this->faker->lastName(),
            User::PROPERTY_EMAIL => $this->faker->unique()->safeEmail(),
            User::PROPERTY_PASSWORD => Hash::make('password'),
            User::PROPERTY_IMAGE_PATH => null,
            User::PROPERTY_VERIFIED_AT => null,
        ];
    }

    public function verified(): self
    {
        return $this->state([
            User::PROPERTY_VERIFIED_AT => Carbon::now(),
        ]);
    }

    public function withAccount(Account $account): self
    {
        return $this->state([
            User::PROPERTY_ACCOUNT_ID => $account->getId(),
        ]);
    }
}
