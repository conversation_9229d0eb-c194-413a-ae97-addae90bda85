<?php

namespace Tests\Domain\Profile\Factories;

use App\Domain\Profile\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class AccountFactory extends Factory
{
    protected $model = Account::class;

    public function definition(): array
    {
        return [
            Account::PROPERTY_NAME => $this->faker->company(),
            Account::PROPERTY_VERIFIED_AT => null,
        ];
    }

    public function verified(): self
    {
        return $this->state([
            Account::PROPERTY_VERIFIED_AT => Carbon::now(),
        ]);
    }
}
