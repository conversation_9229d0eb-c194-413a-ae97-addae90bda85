<?php

namespace Tests\Domain\Profile\Actions;

use App\Domain\Profile\Actions\RegisterAccount;
use App\Domain\Profile\Dto\RegisterAccountDto;
use App\Domain\Profile\Models\Account;
use App\Domain\Profile\Models\User;
use App\Support\Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class RegisterAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_execute_creates_account_and_user(): void
    {
        // Arrange
        $dto = new RegisterAccountDto();
        $dto->setName('Test Account');
        $dto->setFirstname('John');
        $dto->setLastname('Doe');
        $dto->setEmail('<EMAIL>');
        $dto->setPassword('password123');
        $dto->setImagePath('/path/to/image.jpg');

        $action = new RegisterAccount();

        // Act
        $account = $action->execute($dto);

        // Assert
        $this->assertInstanceOf(Account::class, $account);
        $this->assertEquals('Test Account', $account->getName());

        // Verify User was created
        $user = User::query()->where(User::PROPERTY_EMAIL, '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('John', $user->getFirstname());
        $this->assertEquals('Doe', $user->getLastname());
        $this->assertEquals('<EMAIL>', $user->getEmail());
        $this->assertEquals('/path/to/image.jpg', $user->getImagePath());
        $this->assertTrue(Hash::check('password123', $user->getPassword()));
    }

    public function test_execute_creates_user_with_null_password(): void
    {
        // Arrange
        $dto = new RegisterAccountDto();
        $dto->setName('Test Account');
        $dto->setFirstname('Jane');
        $dto->setLastname('Doe');
        $dto->setEmail('<EMAIL>');
        $dto->setPassword(null);

        $action = new RegisterAccount();

        // Act
        $account = $action->execute($dto);

        // Assert
        $this->assertInstanceOf(Account::class, $account);

        // Verify User was created with null password
        $user = User::query()->where(User::PROPERTY_EMAIL, '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNull($user->getPassword());
    }

    public function test_user_factory_creates_valid_user(): void
    {
        // Create a user with the factory
        $user = User::factory()->create([
            User::PROPERTY_FIRSTNAME => 'Factory',
            User::PROPERTY_LASTNAME => 'User',
            User::PROPERTY_EMAIL => '<EMAIL>',
        ]);

        // Assert
        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('Factory', $user->getFirstname());
        $this->assertEquals('User', $user->getLastname());
        $this->assertEquals('<EMAIL>', $user->getEmail());

        // Verify the account was also created
        $account = Account::find($user->getAccountId());
        $this->assertInstanceOf(Account::class, $account);
    }

    public function test_account_factory_creates_valid_account(): void
    {
        // Create an account with the factory
        $account = Account::factory()->create([
            Account::PROPERTY_NAME => 'Factory Account',
        ]);

        // Assert
        $this->assertInstanceOf(Account::class, $account);
        $this->assertEquals('Factory Account', $account->getName());
    }

    public function test_execute_runs_in_transaction(): void
    {
        // This test ensures that the database transaction works
        // If User creation fails, the Account should also not be created

        // Arrange
        $dto = new RegisterAccountDto();
        $dto->setName('Test Account');
        $dto->setFirstname('Invalid');
        $dto->setLastname('User');
        $dto->setEmail('<EMAIL>');

        // Create a user with the same email using the factory
        $account = Account::factory()->create();
        User::factory()->create([
            User::PROPERTY_EMAIL => '<EMAIL>',
            User::PROPERTY_ACCOUNT_ID => $account->getId(),
        ]);

        $action = new RegisterAccount();

        // Act & Assert
        $this->expectException(\Illuminate\Database\QueryException::class);

        try {
            $action->execute($dto);
        } catch (\Exception $e) {
            // Check that no accounts were created
            $this->assertEquals(1, Account::query()->where(Account::PROPERTY_NAME, $account->getName())->count());
            $this->assertEquals(0, Account::query()->where(Account::PROPERTY_NAME, 'Test Account')->count());
            throw $e;
        }
    }
}
